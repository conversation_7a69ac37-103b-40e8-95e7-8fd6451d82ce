#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل جميع تطبيقات تعلم الأرمينية
Run All Armenian Learning Apps
"""

import webbrowser
from pathlib import Path
import json
import time

def main():
    print("🇦🇲 مجموعة تطبيقات تعلم الأرمينية الشاملة")
    print("=" * 60)
    
    app_dir = Path(__file__).parent
    
    # فحص الملفات
    files_to_check = {
        'المفردات': 'armenian_vocabulary_final.json',
        'التطبيق النهائي': 'armenian_app_final.html',
        'مولد الصوت المتقدم': 'advanced_audio_generator.html',
        'مولد الصوت البسيط': 'armenian_audio_generator.html'
    }
    
    available_apps = {}
    
    print("🔍 فحص التطبيقات المتاحة:")
    for app_name, filename in files_to_check.items():
        file_path = app_dir / filename
        if file_path.exists():
            available_apps[app_name] = file_path
            print(f"   ✅ {app_name}: متوفر")
        else:
            print(f"   ❌ {app_name}: غير متوفر")
    
    if not available_apps:
        print("\n❌ لا توجد تطبيقات متاحة")
        print("💡 شغل: python quick_armenian_extractor.py أولاً")
        return
    
    # عرض إحصائيات المفردات
    vocab_file = app_dir / 'armenian_vocabulary_final.json'
    if vocab_file.exists():
        try:
            with open(vocab_file, 'r', encoding='utf-8') as f:
                vocabulary = json.load(f)
            
            total_categories = len(vocabulary)
            total_words = sum(len(cat['words']) for cat in vocabulary.values())
            
            print(f"\n📊 إحصائيات المفردات:")
            print(f"   📝 {total_words} كلمة أرمينية")
            print(f"   📁 {total_categories} فئة منظمة")
            print(f"   🔤 نطق عربي فريد لكل كلمة")
            
        except Exception as e:
            print(f"⚠️ خطأ في قراءة المفردات: {e}")
    
    # قائمة التطبيقات
    print(f"\n🚀 التطبيقات المتاحة:")
    print(f"   1️⃣ التطبيق النهائي الشامل (تعلم تفاعلي)")
    print(f"   2️⃣ مولد الصوت المتقدم (توليد أصوات)")
    print(f"   3️⃣ مولد الصوت البسيط")
    print(f"   4️⃣ فتح جميع التطبيقات")
    print(f"   0️⃣ خروج")
    
    while True:
        try:
            choice = input(f"\n🎯 اختر التطبيق (1-4): ").strip()
            
            if choice == '0':
                print("👋 وداعاً!")
                break
            elif choice == '1':
                open_app('التطبيق النهائي', 'armenian_app_final.html', app_dir)
            elif choice == '2':
                open_app('مولد الصوت المتقدم', 'advanced_audio_generator.html', app_dir)
            elif choice == '3':
                open_app('مولد الصوت البسيط', 'armenian_audio_generator.html', app_dir)
            elif choice == '4':
                open_all_apps(app_dir)
            else:
                print("❌ اختيار غير صحيح، جرب مرة أخرى")
                
        except KeyboardInterrupt:
            print("\n👋 تم الإلغاء")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")

def open_app(app_name, filename, app_dir):
    """فتح تطبيق واحد"""
    file_path = app_dir / filename
    
    if not file_path.exists():
        print(f"❌ {app_name} غير متوفر")
        return
    
    try:
        absolute_path = file_path.resolve()
        file_url = absolute_path.as_uri()
        webbrowser.open(file_url)
        print(f"🚀 تم فتح {app_name}!")
        
        if 'مولد الصوت' in app_name:
            print(f"🔊 تعليمات {app_name}:")
            print(f"   1️⃣ انقر 'توليد جميع الأصوات'")
            print(f"   2️⃣ انتظر حتى يكتمل التوليد")
            print(f"   3️⃣ انقر 'تحميل البيانات'")
        else:
            print(f"📚 تعليمات {app_name}:")
            print(f"   1️⃣ تصفح الفئات المختلفة")
            print(f"   2️⃣ انقر 🔊 لسماع النطق")
            print(f"   3️⃣ استخدم البحث للعثور على كلمات")
        
    except Exception as e:
        print(f"❌ فشل فتح {app_name}: {e}")

def open_all_apps(app_dir):
    """فتح جميع التطبيقات"""
    apps_to_open = [
        ('التطبيق النهائي الشامل', 'armenian_app_final.html'),
        ('مولد الصوت المتقدم', 'advanced_audio_generator.html')
    ]
    
    print("🚀 فتح جميع التطبيقات...")
    
    for app_name, filename in apps_to_open:
        file_path = app_dir / filename
        if file_path.exists():
            try:
                absolute_path = file_path.resolve()
                file_url = absolute_path.as_uri()
                webbrowser.open(file_url)
                print(f"   ✅ تم فتح {app_name}")
                time.sleep(1)  # توقف قصير بين فتح التطبيقات
            except Exception as e:
                print(f"   ❌ فشل فتح {app_name}: {e}")
        else:
            print(f"   ⚠️ {app_name} غير متوفر")
    
    print("\n🎉 تم فتح جميع التطبيقات المتاحة!")
    print("\n📋 ملخص التطبيقات:")
    print("   🎓 التطبيق النهائي: للتعلم التفاعلي")
    print("   🔊 مولد الصوت: لتوليد الملفات الصوتية")
    print("\n💡 نصائح:")
    print("   📱 استخدم مولد الصوت لإنشاء ملفات للموبايل")
    print("   📚 استخدم التطبيق النهائي للتعلم والمراجعة")

if __name__ == "__main__":
    main()
