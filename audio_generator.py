#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد الملفات الصوتية للمفردات الأرمينية
Armenian Audio Generator for Vocabulary
"""

import json
import os
import re
from gtts import gTTS
from pydub import AudioSegment
import requests
import time

class ArmenianAudioGenerator:
    def __init__(self, vocabulary_file='vocabulary_data.json'):
        self.vocabulary_file = vocabulary_file
        self.audio_dir = 'audio'
        self.vocabulary_data = {}
        self.created_files = []
        
        # إنشاء مجلد الصوتيات
        if not os.path.exists(self.audio_dir):
            os.makedirs(self.audio_dir)
            print(f"✅ تم إنشاء مجلد {self.audio_dir}")

    def load_vocabulary(self):
        """تحميل بيانات المفردات"""
        try:
            with open(self.vocabulary_file, 'r', encoding='utf-8') as f:
                self.vocabulary_data = json.load(f)
            print(f"✅ تم تحميل المفردات من {self.vocabulary_file}")
            return True
        except FileNotFoundError:
            print(f"❌ لم يتم العثور على ملف {self.vocabulary_file}")
            return False
        except Exception as e:
            print(f"❌ خطأ في تحميل المفردات: {e}")
            return False

    def generate_audio_from_phonetic(self, phonetic, audio_filename):
        """إنشاء ملف صوتي من النطق اللاتيني"""
        try:
            # تنظيف النطق
            clean_phonetic = self.clean_phonetic_text(phonetic)
            
            # محاولة استخدام Google TTS مع اللغة الأرمينية
            audio_path = os.path.join(self.audio_dir, f"{audio_filename}.mp3")
            
            # إذا كان الملف موجود، تخطي
            if os.path.exists(audio_path):
                print(f"⏭️ الملف موجود: {audio_filename}.mp3")
                return audio_path
            
            try:
                # محاولة مع اللغة الأرمينية
                tts = gTTS(text=clean_phonetic, lang='hy', slow=False)
                tts.save(audio_path)
                print(f"✅ تم إنشاء: {audio_filename}.mp3 (أرمني)")
                
            except Exception:
                try:
                    # محاولة مع الإنجليزية كبديل
                    tts = gTTS(text=clean_phonetic, lang='en', slow=True)
                    tts.save(audio_path)
                    print(f"✅ تم إنشاء: {audio_filename}.mp3 (إنجليزي)")
                    
                except Exception as e:
                    print(f"❌ فشل في إنشاء {audio_filename}.mp3: {e}")
                    return None
            
            # تحسين جودة الصوت
            self.enhance_audio(audio_path)
            self.created_files.append(audio_path)
            
            return audio_path
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الصوت لـ {phonetic}: {e}")
            return None

    def clean_phonetic_text(self, phonetic):
        """تنظيف النص الصوتي"""
        if not phonetic:
            return ""
        
        # إزالة الرموز الخاصة
        cleaned = re.sub(r'[^\w\s]', ' ', phonetic)
        
        # إزالة المسافات الزائدة
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        # تحويل بعض الأحرف للنطق الصحيح
        replacements = {
            'ç': 'ch',
            'ğ': 'gh',
            'š': 'sh',
            'ž': 'zh',
            'ć': 'ch',
            'đ': 'dh'
        }
        
        for old, new in replacements.items():
            cleaned = cleaned.replace(old, new)
        
        return cleaned

    def enhance_audio(self, audio_path):
        """تحسين جودة الملف الصوتي"""
        try:
            # تحميل الملف الصوتي
            audio = AudioSegment.from_mp3(audio_path)
            
            # تطبيق تحسينات
            # زيادة الصوت قليلاً
            audio = audio + 3  # زيادة 3 ديسيبل
            
            # تطبيع الصوت
            audio = audio.normalize()
            
            # إضافة صمت قصير في البداية والنهاية
            silence = AudioSegment.silent(duration=200)  # 200ms صمت
            audio = silence + audio + silence
            
            # حفظ الملف المحسن
            audio.export(audio_path, format="mp3", bitrate="128k")
            
        except Exception as e:
            print(f"⚠️ تعذر تحسين الصوت لـ {audio_path}: {e}")

    def generate_all_audio_files(self):
        """إنشاء جميع الملفات الصوتية"""
        if not self.load_vocabulary():
            return
        
        print("🎵 بدء إنشاء الملفات الصوتية...")
        print("=" * 50)
        
        total_words = 0
        successful_files = 0
        
        for category, words in self.vocabulary_data.items():
            print(f"\n📂 معالجة فئة: {category}")
            
            for word in words:
                total_words += 1
                phonetic = word.get('phonetic', '')
                audio_filename = word.get('audio', f"word_{total_words}")
                
                if phonetic:
                    result = self.generate_audio_from_phonetic(phonetic, audio_filename)
                    if result:
                        successful_files += 1
                    
                    # توقف قصير لتجنب حدود API
                    time.sleep(1)
                else:
                    print(f"⚠️ لا يوجد نطق لـ: {word.get('arabic', 'غير معروف')}")
        
        print(f"\n🎉 انتهى! تم إنشاء {successful_files} من {total_words} ملف صوتي")
        
        # إنشاء ملف فهرس للملفات الصوتية
        self.create_audio_index()

    def create_audio_index(self):
        """إنشاء فهرس للملفات الصوتية"""
        try:
            index_data = {
                'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                'total_files': len(self.created_files),
                'files': []
            }
            
            for file_path in self.created_files:
                filename = os.path.basename(file_path)
                file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                
                index_data['files'].append({
                    'filename': filename,
                    'path': file_path,
                    'size_bytes': file_size,
                    'size_kb': round(file_size / 1024, 2)
                })
            
            # حفظ الفهرس
            with open('audio_index.json', 'w', encoding='utf-8') as f:
                json.dump(index_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ تم إنشاء فهرس الملفات الصوتية: audio_index.json")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء فهرس الملفات: {e}")

    def create_test_audio(self):
        """إنشاء ملفات صوتية تجريبية"""
        test_words = [
            {"phonetic": "Barev", "audio": "barev"},
            {"phonetic": "Mayr", "audio": "mayr"},
            {"phonetic": "Hayr", "audio": "hayr"},
            {"phonetic": "Mek", "audio": "mek"},
            {"phonetic": "Yerku", "audio": "yerku"}
        ]
        
        print("🧪 إنشاء ملفات صوتية تجريبية...")
        
        for word in test_words:
            self.generate_audio_from_phonetic(word['phonetic'], word['audio'])
            time.sleep(1)
        
        print("✅ انتهى إنشاء الملفات التجريبية")

    def update_javascript_with_audio(self, js_file='script.js'):
        """تحديث ملف JavaScript لتفعيل الصوت الحقيقي"""
        try:
            with open(js_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # استبدال دالة محاكاة الصوت بالصوت الحقيقي
            old_function = '''function simulateAudioPlayback(audioFile, cardIndex) {
    // محاكاة تشغيل الصوت
    console.log(`Playing audio: ${audioFile}`);
    
    // في التطبيق الحقيقي، ستستخدم:
    // currentAudio = new Audio(`audio/${audioFile}.mp3`);
    // currentAudio.play();
    
    // محاكاة انتهاء التشغيل بعد 2 ثانية
    setTimeout(() => {
        const card = document.querySelectorAll('.vocab-card')[cardIndex];
        if (card) {
            card.classList.remove('playing');
        }
        if (currentPlayingCard === cardIndex) {
            currentPlayingCard = null;
        }
    }, 2000);
}'''

            new_function = '''function simulateAudioPlayback(audioFile, cardIndex) {
    // تشغيل الصوت الحقيقي
    try {
        currentAudio = new Audio(`audio/${audioFile}.mp3`);
        
        currentAudio.addEventListener('ended', () => {
            const card = document.querySelectorAll('.vocab-card')[cardIndex];
            if (card) {
                card.classList.remove('playing');
            }
            if (currentPlayingCard === cardIndex) {
                currentPlayingCard = null;
            }
        });
        
        currentAudio.addEventListener('error', (e) => {
            console.warn(`تعذر تشغيل الصوت: ${audioFile}.mp3`);
            // العودة للمحاكاة في حالة الخطأ
            setTimeout(() => {
                const card = document.querySelectorAll('.vocab-card')[cardIndex];
                if (card) {
                    card.classList.remove('playing');
                }
                if (currentPlayingCard === cardIndex) {
                    currentPlayingCard = null;
                }
            }, 2000);
        });
        
        currentAudio.play();
        
    } catch (error) {
        console.warn(`خطأ في تشغيل الصوت: ${error}`);
        // العودة للمحاكاة في حالة الخطأ
        setTimeout(() => {
            const card = document.querySelectorAll('.vocab-card')[cardIndex];
            if (card) {
                card.classList.remove('playing');
            }
            if (currentPlayingCard === cardIndex) {
                currentPlayingCard = null;
            }
        }, 2000);
    }
}'''

            # استبدال الدالة
            updated_content = content.replace(old_function, new_function)
            
            # حفظ الملف المحدث
            with open(js_file, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            print(f"✅ تم تحديث {js_file} لتفعيل الصوت الحقيقي")
            
        except Exception as e:
            print(f"❌ خطأ في تحديث ملف JavaScript: {e}")

def main():
    """الدالة الرئيسية"""
    print("🎵 مولد الملفات الصوتية للمفردات الأرمينية")
    print("=" * 50)
    
    generator = ArmenianAudioGenerator()
    
    # خيارات المستخدم
    print("اختر العملية المطلوبة:")
    print("1. إنشاء ملفات صوتية تجريبية")
    print("2. إنشاء جميع الملفات الصوتية")
    print("3. تحديث JavaScript لتفعيل الصوت")
    print("4. تنفيذ جميع العمليات")
    
    choice = input("\nأدخل رقم الخيار (1-4): ").strip()
    
    if choice == "1":
        generator.create_test_audio()
    elif choice == "2":
        generator.generate_all_audio_files()
    elif choice == "3":
        generator.update_javascript_with_audio()
    elif choice == "4":
        generator.create_test_audio()
        generator.generate_all_audio_files()
        generator.update_javascript_with_audio()
    else:
        print("❌ خيار غير صحيح")

if __name__ == "__main__":
    main()