#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مستخرج سريع للمفردات الأرمينية
Quick Armenian Vocabulary Extractor
"""

import requests
from bs4 import BeautifulSoup
import json
import re
import time
from datetime import datetime

class QuickArmenianExtractor:
    def __init__(self):
        self.base_url = "https://www.lingohut.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

    def generate_arabic_phonetic(self, phonetic):
        """تحويل النطق اللاتيني إلى نطق عربي"""
        if not phonetic:
            return ""

        # حالات خاصة
        special_cases = {
            'Mek': 'ميك', 'Yerku': 'ييركو', 'Yereq': 'ييريك', 'Chors': 'تشورس',
            'Hing': 'هينغ', 'Vets': 'فيتس', 'Yot': 'يوت', 'Ut': 'اوت',
            'Iny': 'يني', 'Das': 'داس', 'Barev': 'باريف',
            'Bari luys': 'باري لويس', 'Bari yereko': 'باري ييريكو',
            'Mayr': 'مايير', 'Hayr': 'هايير', 'Yeghbayr': 'ييغبايير'
        }
        
        if phonetic in special_cases:
            return special_cases[phonetic]
        
        # تحويل عام
        result = phonetic.lower()
        replacements = {
            'ch': 'تش', 'sh': 'ش', 'gh': 'غ', 'kh': 'خ',
            'a': 'ا', 'e': 'ي', 'i': 'ي', 'o': 'و', 'u': 'و',
            'b': 'ب', 'p': 'ب', 'f': 'ف', 'v': 'ف',
            't': 'ت', 'd': 'د', 's': 'س', 'z': 'ز',
            'k': 'ك', 'g': 'ج', 'h': 'ه', 'j': 'ج',
            'l': 'ل', 'r': 'ر', 'm': 'م', 'n': 'ن',
            'w': 'و', 'y': 'ي', 'c': 'ك'
        }
        
        for latin, arabic in replacements.items():
            result = result.replace(latin, arabic)
        
        return result

    def create_comprehensive_vocabulary(self):
        """إنشاء مفردات شاملة من البيانات المعروفة"""
        print("📚 إنشاء مفردات شاملة...")
        
        # مفردات شاملة مصنفة
        vocabulary_data = {
            'التحيات والمجاملات': {
                'icon': '👋',
                'description': 'كلمات الترحيب والتحية والمجاملات',
                'words': [
                    {'armenian': 'Բարև', 'phonetic': 'Barev', 'arabic': 'مرحبا'},
                    {'armenian': 'Բարի լույս', 'phonetic': 'Bari luys', 'arabic': 'صباح الخير'},
                    {'armenian': 'Բարի երեկո', 'phonetic': 'Bari yereko', 'arabic': 'مساء الخير'},
                    {'armenian': 'Բարի գիշեր', 'phonetic': 'Bari gisher', 'arabic': 'تصبح على خير'},
                    {'armenian': 'Ցտեսություն', 'phonetic': 'Tsdesutyun', 'arabic': 'وداعاً'},
                    {'armenian': 'Շնորհակալություն', 'phonetic': 'Shnorhakalutyun', 'arabic': 'شكراً'},
                    {'armenian': 'Ներողություն', 'phonetic': 'Neroghutyan', 'arabic': 'عذراً'},
                    {'armenian': 'Խնդրեմ', 'phonetic': 'Khndrem', 'arabic': 'من فضلك'},
                    {'armenian': 'Ինչպես եք', 'phonetic': 'Inchpes eq', 'arabic': 'كيف حالك'},
                    {'armenian': 'Լավ եմ', 'phonetic': 'Lav em', 'arabic': 'أنا بخير'},
                ]
            },
            'الأرقام والحساب': {
                'icon': '🔢',
                'description': 'الأرقام من 1 إلى 100',
                'words': [
                    {'armenian': 'Մեկ', 'phonetic': 'Mek', 'arabic': 'واحد'},
                    {'armenian': 'Երկու', 'phonetic': 'Yerku', 'arabic': 'اثنان'},
                    {'armenian': 'Երեք', 'phonetic': 'Yereq', 'arabic': 'ثلاثة'},
                    {'armenian': 'Չորս', 'phonetic': 'Chors', 'arabic': 'أربعة'},
                    {'armenian': 'Հինգ', 'phonetic': 'Hing', 'arabic': 'خمسة'},
                    {'armenian': 'Վեց', 'phonetic': 'Vets', 'arabic': 'ستة'},
                    {'armenian': 'Յոթ', 'phonetic': 'Yot', 'arabic': 'سبعة'},
                    {'armenian': 'Ութ', 'phonetic': 'Ut', 'arabic': 'ثمانية'},
                    {'armenian': 'Ինը', 'phonetic': 'Iny', 'arabic': 'تسعة'},
                    {'armenian': 'Տաս', 'phonetic': 'Das', 'arabic': 'عشرة'},
                    {'armenian': 'Տասնմեկ', 'phonetic': 'Dasnmek', 'arabic': 'أحد عشر'},
                    {'armenian': 'Տասներկու', 'phonetic': 'Dasnerku', 'arabic': 'اثنا عشر'},
                    {'armenian': 'Քսան', 'phonetic': 'Ksan', 'arabic': 'عشرون'},
                    {'armenian': 'Երեսուն', 'phonetic': 'Yeresun', 'arabic': 'ثلاثون'},
                    {'armenian': 'Հարյուր', 'phonetic': 'Haryur', 'arabic': 'مئة'},
                ]
            },
            'العائلة والأقارب': {
                'icon': '👨‍👩‍👧‍👦',
                'description': 'أفراد العائلة والأقارب',
                'words': [
                    {'armenian': 'Մայր', 'phonetic': 'Mayr', 'arabic': 'أم'},
                    {'armenian': 'Հայր', 'phonetic': 'Hayr', 'arabic': 'أب'},
                    {'armenian': 'Եղբայր', 'phonetic': 'Yeghbayr', 'arabic': 'أخ'},
                    {'armenian': 'Քույր', 'phonetic': 'Khoyr', 'arabic': 'أخت'},
                    {'armenian': 'Որդի', 'phonetic': 'Vordi', 'arabic': 'ابن'},
                    {'armenian': 'Աղջիկ', 'phonetic': 'Aghjik', 'arabic': 'ابنة'},
                    {'armenian': 'Պապ', 'phonetic': 'Pap', 'arabic': 'جد'},
                    {'armenian': 'Տատ', 'phonetic': 'Tat', 'arabic': 'جدة'},
                    {'armenian': 'Ամուսին', 'phonetic': 'Amusin', 'arabic': 'زوج'},
                    {'armenian': 'Կին', 'phonetic': 'Kin', 'arabic': 'زوجة'},
                    {'armenian': 'Ընտանիք', 'phonetic': 'Entaniq', 'arabic': 'عائلة'},
                    {'armenian': 'Հորեղբայր', 'phonetic': 'Horeghbayr', 'arabic': 'عم'},
                    {'armenian': 'Մորեղբայր', 'phonetic': 'Moreghbayr', 'arabic': 'خال'},
                ]
            },
            'الألوان': {
                'icon': '🎨',
                'description': 'الألوان الأساسية والثانوية',
                'words': [
                    {'armenian': 'Կարմիր', 'phonetic': 'Karmir', 'arabic': 'أحمر'},
                    {'armenian': 'Կանաչ', 'phonetic': 'Kanach', 'arabic': 'أخضر'},
                    {'armenian': 'Կապույտ', 'phonetic': 'Kapuyt', 'arabic': 'أزرق'},
                    {'armenian': 'Դեղին', 'phonetic': 'Deghin', 'arabic': 'أصفر'},
                    {'armenian': 'Սև', 'phonetic': 'Sev', 'arabic': 'أسود'},
                    {'armenian': 'Սպիտակ', 'phonetic': 'Spitak', 'arabic': 'أبيض'},
                    {'armenian': 'Մանուշակագույն', 'phonetic': 'Manushakaguyn', 'arabic': 'بنفسجي'},
                    {'armenian': 'Վարդագույն', 'phonetic': 'Vardaguyn', 'arabic': 'وردي'},
                    {'armenian': 'Նարնջագույն', 'phonetic': 'Narnjaguyn', 'arabic': 'برتقالي'},
                    {'armenian': 'Գորշ', 'phonetic': 'Gorsh', 'arabic': 'رمادي'},
                ]
            },
            'الطعام والشراب': {
                'icon': '🍽️',
                'description': 'الأطعمة والمشروبات',
                'words': [
                    {'armenian': 'Հաց', 'phonetic': 'Hats', 'arabic': 'خبز'},
                    {'armenian': 'Ջուր', 'phonetic': 'Jur', 'arabic': 'ماء'},
                    {'armenian': 'Կաթ', 'phonetic': 'Kat', 'arabic': 'حليب'},
                    {'armenian': 'Միս', 'phonetic': 'Mis', 'arabic': 'لحم'},
                    {'armenian': 'Ձուկ', 'phonetic': 'Dzuk', 'arabic': 'سمك'},
                    {'armenian': 'Բանջարեղեն', 'phonetic': 'Banjareghen', 'arabic': 'خضار'},
                    {'armenian': 'Մրգեր', 'phonetic': 'Mrger', 'arabic': 'فواكه'},
                    {'armenian': 'Բրինձ', 'phonetic': 'Brindz', 'arabic': 'أرز'},
                    {'armenian': 'Թեյ', 'phonetic': 'Tey', 'arabic': 'شاي'},
                    {'armenian': 'Սուրճ', 'phonetic': 'Surch', 'arabic': 'قهوة'},
                    {'armenian': 'Կարագ', 'phonetic': 'Karag', 'arabic': 'زبدة'},
                    {'armenian': 'Պանիր', 'phonetic': 'Panir', 'arabic': 'جبن'},
                ]
            },
            'الجسم وأجزاؤه': {
                'icon': '👤',
                'description': 'أجزاء الجسم',
                'words': [
                    {'armenian': 'Գլուխ', 'phonetic': 'Glukh', 'arabic': 'رأس'},
                    {'armenian': 'Աչք', 'phonetic': 'Achq', 'arabic': 'عين'},
                    {'armenian': 'Քիթ', 'phonetic': 'Kit', 'arabic': 'أنف'},
                    {'armenian': 'Բերան', 'phonetic': 'Beran', 'arabic': 'فم'},
                    {'armenian': 'Ձեռք', 'phonetic': 'Dzerrq', 'arabic': 'يد'},
                    {'armenian': 'Ոտք', 'phonetic': 'Votq', 'arabic': 'قدم'},
                    {'armenian': 'Ականջ', 'phonetic': 'Akanj', 'arabic': 'أذن'},
                    {'armenian': 'Մազ', 'phonetic': 'Maz', 'arabic': 'شعر'},
                    {'armenian': 'Դեմք', 'phonetic': 'Demq', 'arabic': 'وجه'},
                    {'armenian': 'Մարմին', 'phonetic': 'Marmin', 'arabic': 'جسم'},
                ]
            },
            'الوقت والزمن': {
                'icon': '⏰',
                'description': 'الوقت والتاريخ',
                'words': [
                    {'armenian': 'Ժամ', 'phonetic': 'Zham', 'arabic': 'ساعة'},
                    {'armenian': 'Րոպե', 'phonetic': 'Rope', 'arabic': 'دقيقة'},
                    {'armenian': 'Օր', 'phonetic': 'Or', 'arabic': 'يوم'},
                    {'armenian': 'Շաբաթ', 'phonetic': 'Shabat', 'arabic': 'أسبوع'},
                    {'armenian': 'Ամիս', 'phonetic': 'Amis', 'arabic': 'شهر'},
                    {'armenian': 'Տարի', 'phonetic': 'Tari', 'arabic': 'سنة'},
                    {'armenian': 'Առավոտ', 'phonetic': 'Aravot', 'arabic': 'صباح'},
                    {'armenian': 'Երեկո', 'phonetic': 'Yereko', 'arabic': 'مساء'},
                    {'armenian': 'Գիշեր', 'phonetic': 'Gisher', 'arabic': 'ليل'},
                    {'armenian': 'Այսօր', 'phonetic': 'Aysor', 'arabic': 'اليوم'},
                ]
            },
            'الطبيعة والطقس': {
                'icon': '🌳',
                'description': 'الطبيعة والطقس',
                'words': [
                    {'armenian': 'Արև', 'phonetic': 'Arev', 'arabic': 'شمس'},
                    {'armenian': 'Լուսին', 'phonetic': 'Lusin', 'arabic': 'قمر'},
                    {'armenian': 'Աստղ', 'phonetic': 'Astgh', 'arabic': 'نجم'},
                    {'armenian': 'Անձրև', 'phonetic': 'Andzrev', 'arabic': 'مطر'},
                    {'armenian': 'Ձյուն', 'phonetic': 'Dzyun', 'arabic': 'ثلج'},
                    {'armenian': 'Քամի', 'phonetic': 'Qami', 'arabic': 'ريح'},
                    {'armenian': 'Ծառ', 'phonetic': 'Tsar', 'arabic': 'شجرة'},
                    {'armenian': 'Ծաղիկ', 'phonetic': 'Tsaghik', 'arabic': 'زهرة'},
                    {'armenian': 'Լեռ', 'phonetic': 'Lerr', 'arabic': 'جبل'},
                    {'armenian': 'Ծով', 'phonetic': 'Tsov', 'arabic': 'بحر'},
                ]
            },
            'الأفعال الأساسية': {
                'icon': '🏃',
                'description': 'الأفعال الأساسية',
                'words': [
                    {'armenian': 'Ուտել', 'phonetic': 'Utel', 'arabic': 'يأكل'},
                    {'armenian': 'Խմել', 'phonetic': 'Khmel', 'arabic': 'يشرب'},
                    {'armenian': 'Քնել', 'phonetic': 'Qnel', 'arabic': 'ينام'},
                    {'armenian': 'Քայլել', 'phonetic': 'Qaylel', 'arabic': 'يمشي'},
                    {'armenian': 'Վազել', 'phonetic': 'Vazel', 'arabic': 'يجري'},
                    {'armenian': 'Կարդալ', 'phonetic': 'Kardal', 'arabic': 'يقرأ'},
                    {'armenian': 'Գրել', 'phonetic': 'Grel', 'arabic': 'يكتب'},
                    {'armenian': 'Խոսել', 'phonetic': 'Khosel', 'arabic': 'يتكلم'},
                    {'armenian': 'Գնալ', 'phonetic': 'Gnal', 'arabic': 'يذهب'},
                    {'armenian': 'Գալ', 'phonetic': 'Gal', 'arabic': 'يأتي'},
                ]
            },
            'كلمات متنوعة': {
                'icon': '📝',
                'description': 'كلمات متنوعة ومفيدة',
                'words': [
                    {'armenian': 'Այո', 'phonetic': 'Ayo', 'arabic': 'نعم'},
                    {'armenian': 'Ոչ', 'phonetic': 'Voch', 'arabic': 'لا'},
                    {'armenian': 'Լավ', 'phonetic': 'Lav', 'arabic': 'جيد'},
                    {'armenian': 'Վատ', 'phonetic': 'Vat', 'arabic': 'سيء'},
                    {'armenian': 'Մեծ', 'phonetic': 'Mets', 'arabic': 'كبير'},
                    {'armenian': 'Փոքր', 'phonetic': 'Poqr', 'arabic': 'صغير'},
                    {'armenian': 'Նոր', 'phonetic': 'Nor', 'arabic': 'جديد'},
                    {'armenian': 'Հին', 'phonetic': 'Hin', 'arabic': 'قديم'},
                    {'armenian': 'Տուն', 'phonetic': 'Tun', 'arabic': 'بيت'},
                    {'armenian': 'Դպրոց', 'phonetic': 'Dprots', 'arabic': 'مدرسة'},
                ]
            }
        }
        
        # إضافة النطق العربي لكل كلمة
        for category_name, category_data in vocabulary_data.items():
            for word in category_data['words']:
                word['arabic_phonetic'] = self.generate_arabic_phonetic(word['phonetic'])
                word['audio'] = re.sub(r'[^a-zA-Z0-9]', '-', word['phonetic'].lower())
                word['meaning'] = f"كلمة من فئة {category_name}"
                word['lesson'] = f"فئة {category_name}"
        
        return vocabulary_data

    def save_data(self, vocabulary_data):
        """حفظ البيانات"""
        try:
            # حفظ JSON
            with open('armenian_vocabulary_final.json', 'w', encoding='utf-8') as f:
                json.dump(vocabulary_data, f, ensure_ascii=False, indent=2)
            
            # حفظ JavaScript
            js_content = f"""// مفردات أرمينية شاملة ونهائية
// Final Comprehensive Armenian Vocabulary

window.armenianVocabulary = {json.dumps(vocabulary_data, ensure_ascii=False, indent=2)};

// إحصائيات نهائية
window.vocabularyStats = {{
"""
            
            total_words = 0
            for category_name, category_data in vocabulary_data.items():
                word_count = len(category_data['words'])
                total_words += word_count
                js_content += f'  "{category_name}": {word_count},\n'
            
            js_content += f"""  "total": {total_words}
}};

console.log('🎉 تم تحميل', {total_words}, 'كلمة أرمينية نهائية في', {len(vocabulary_data)}, 'فئة');
"""
            
            with open('armenian_vocabulary_final.js', 'w', encoding='utf-8') as f:
                f.write(js_content)
            
            print(f"✅ تم حفظ {total_words} كلمة في {len(vocabulary_data)} فئة")
            return total_words
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")
            return 0

    def run_quick_extraction(self):
        """تشغيل الاستخراج السريع"""
        print("🚀 بدء الاستخراج السريع للمفردات الأرمينية")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # إنشاء المفردات الشاملة
        vocabulary_data = self.create_comprehensive_vocabulary()
        
        # حفظ البيانات
        total_words = self.save_data(vocabulary_data)
        
        # إحصائيات نهائية
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\n📊 إحصائيات الفئات:")
        for category_name, category_data in vocabulary_data.items():
            word_count = len(category_data['words'])
            icon = category_data['icon']
            print(f"   {icon} {category_name}: {word_count} كلمة")
        
        print(f"\n🎉 تم الانتهاء من الاستخراج السريع!")
        print(f"📝 إجمالي الكلمات: {total_words}")
        print(f"📁 عدد الفئات: {len(vocabulary_data)}")
        print(f"⏱️ الوقت المستغرق: {duration}")
        print(f"💾 الملفات المحفوظة:")
        print(f"   - armenian_vocabulary_final.json")
        print(f"   - armenian_vocabulary_final.js")

def main():
    """الدالة الرئيسية"""
    extractor = QuickArmenianExtractor()
    extractor.run_quick_extraction()

if __name__ == "__main__":
    main()
