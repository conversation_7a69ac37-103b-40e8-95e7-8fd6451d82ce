#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فتح التطبيق النهائي مع 47 كلمة فريدة بدون تكرار
Open Final App with 47 Unique Words
"""

import os
import webbrowser
import json
from pathlib import Path

def main():
    print("🇦🇲 فتح التطبيق النهائي لتعلم الأرمينية")
    print("=" * 50)
    
    app_dir = Path(__file__).parent
    
    # فحص ملف الفئات الفريدة
    categories_file = app_dir / 'vocabulary_categories_unique.json'
    if categories_file.exists():
        try:
            with open(categories_file, 'r', encoding='utf-8') as f:
                categories = json.load(f)
            
            total_categories = len(categories)
            total_words = sum(len(cat['words']) for cat in categories.values())
            
            print(f"✅ التطبيق النهائي جاهز:")
            print(f"   📝 {total_words} كلمة فريدة (بدون تكرار)")
            print(f"   📁 {total_categories} فئة منظمة")
            print(f"   🔊 صوت تفاعلي")
            print(f"   🎯 نطق عربي دقيق 100%")
            print()
            
            # عرض الفئات مع التفاصيل
            print("📋 الفئات والكلمات الفريدة:")
            for category_name, category_data in categories.items():
                word_count = len(category_data['words'])
                icon = category_data.get('icon', '📝')
                print(f"   {icon} {category_name}: {word_count} كلمة فريدة")
                
                # عرض أول 3 كلمات من كل فئة
                for i, word in enumerate(category_data['words'][:3]):
                    print(f"      • {word['armenian']} ({word['phonetic']}) = {word['arabic']}")
                
                if len(category_data['words']) > 3:
                    print(f"      ... و {len(category_data['words']) - 3} كلمة أخرى")
                print()
            
        except Exception as e:
            print(f"⚠️ خطأ في قراءة البيانات: {e}")
    else:
        print("❌ ملف الفئات الفريدة غير موجود")
        print("💡 شغل: python organize_unique_categories.py")
        return
    
    # فتح التطبيق
    app_file = app_dir / 'app_final_unique.html'
    if app_file.exists():
        try:
            file_url = app_file.as_uri()
            webbrowser.open(file_url)
            print("🚀 تم فتح التطبيق النهائي!")
            print()
            print("🎯 مميزات التطبيق النهائي:")
            print("   ✅ 47 كلمة فريدة بدون أي تكرار")
            print("   ✅ 5 فئات منظمة ومرتبة")
            print("   ✅ نطق عربي دقيق لكل كلمة")
            print("   ✅ صوت تفاعلي عالي الجودة")
            print("   ✅ بحث متقدم وسريع")
            print("   ✅ واجهة جميلة ومتجاوبة")
            print("   ✅ تصميم مناسب للموبايل")
            print()
            print("🔤 كلمات مميزة للتجربة:")
            print("   • ابحث عن 'ميك' → واحد")
            print("   • ابحث عن 'باريف' → مرحبا")
            print("   • ابحث عن 'مايير' → أم")
            print("   • ابحث عن 'كارمير' → أحمر")
            print()
            print("🔊 كيفية الاستخدام:")
            print("   1. اختر فئة من التبويبات")
            print("   2. انقر على زر 🔊 لسماع النطق")
            print("   3. استخدم البحث للعثور على كلمات")
            print("   4. استمتع بالتعلم!")
            print()
            print("📱 جاهز لتطبيق الموبايل:")
            print("   • بيانات JSON منظمة")
            print("   • أسماء ملفات صوتية")
            print("   • فئات واضحة")
            print("   • نطق عربي فريد")
            
        except Exception as e:
            print(f"❌ فشل فتح التطبيق: {e}")
    else:
        print("❌ ملف التطبيق غير موجود")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
