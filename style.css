* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5rem;
    color: #4a5568;
    margin-bottom: 10px;
    font-weight: 700;
}

header p {
    font-size: 1.1rem;
    color: #718096;
    font-weight: 400;
}

.controls {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
    justify-content: center;
}

#lessonSelect {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    background: white;
    font-size: 1rem;
    font-family: 'Cairo', sans-serif;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
}

#lessonSelect:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.search-box {
    display: flex;
    background: white;
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.search-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

#searchInput {
    padding: 12px 20px;
    border: none;
    outline: none;
    font-size: 1rem;
    font-family: 'Cairo', sans-serif;
    width: 250px;
}

#searchBtn {
    padding: 12px 20px;
    border: none;
    background: #667eea;
    color: white;
    cursor: pointer;
    transition: background 0.3s ease;
}

#searchBtn:hover {
    background: #5a67d8;
}

.stats {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    justify-content: center;
    flex-wrap: wrap;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #718096;
    font-weight: 500;
}

.vocabulary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    margin-bottom: 50px;
}

.vocab-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    min-height: 200px;
}

.vocab-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.vocab-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.vocab-card.playing {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.armenian-text {
    font-size: 1.4rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 10px;
    text-align: center;
    line-height: 1.4;
}

.vocab-card.playing .armenian-text {
    color: white;
}

.phonetic {
    font-size: 1.2rem;
    color: #667eea;
    font-weight: 500;
    margin-bottom: 12px;
    text-align: center;
    font-style: italic;
}

.vocab-card.playing .phonetic {
    color: #e2e8f0;
}

.arabic-meaning {
    font-size: 1.3rem;
    color: #2d3748;
    font-weight: 600;
    text-align: center;
    margin-bottom: 10px;
}

.vocab-card.playing .arabic-meaning {
    color: white;
}

.word-meaning {
    font-size: 0.95rem;
    color: #718096;
    text-align: center;
    line-height: 1.4;
    margin-bottom: 15px;
    font-style: italic;
    border-top: 1px solid #e2e8f0;
    padding-top: 10px;
}

.vocab-card.playing .word-meaning {
    color: #e2e8f0;
    border-top-color: rgba(255, 255, 255, 0.3);
}

.lesson-tag {
    display: inline-block;
    background: #e2e8f0;
    color: #4a5568;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 10px;
}

.vocab-card.playing .lesson-tag {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.play-button {
    position: absolute;
    top: 15px;
    left: 15px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.play-button:hover {
    background: #5a67d8;
    transform: scale(1.1);
}

.vocab-card.playing .play-button {
    background: rgba(255, 255, 255, 0.2);
}

.floating-player {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    min-width: 300px;
}

.player-content {
    text-align: center;
}

#currentWord {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 15px;
}

.player-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.player-controls button {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.player-controls button:hover {
    background: #5a67d8;
    transform: scale(1.1);
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    #searchInput {
        width: 200px;
    }
    
    .vocabulary-grid {
        grid-template-columns: 1fr;
    }
    
    .floating-player {
        right: 10px;
        bottom: 10px;
        min-width: 250px;
    }
    
    .vocab-card {
        min-height: 180px;
    }
}

.loading {
    text-align: center;
    padding: 50px;
    font-size: 1.2rem;
    color: #718096;
}

.no-results {
    text-align: center;
    padding: 50px;
    color: #718096;
    grid-column: 1 / -1;
}

.no-results h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

/* تحسينات إضافية للنص الأرمني */
.armenian-text {
    font-family: 'Arial Unicode MS', 'Lucida Grande', sans-serif;
    letter-spacing: 0.5px;
}

/* تحسين عرض النطق */
.phonetic {
    background: rgba(102, 126, 234, 0.1);
    padding: 8px 15px;
    border-radius: 20px;
    display: inline-block;
}

.vocab-card.playing .phonetic {
    background: rgba(255, 255, 255, 0.2);
}

/* تحسين زر التصدير */
.export-btn {
    position: fixed;
    top: 20px;
    left: 20px;
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    z-index: 1000;
}

.export-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* تحسين الاستجابة للأجهزة الصغيرة */
@media (max-width: 480px) {
    .vocabulary-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .vocab-card {
        padding: 20px;
        min-height: 160px;
    }
    
    .armenian-text {
        font-size: 1.2rem;
    }
    
    .phonetic {
        font-size: 1rem;
    }
    
    .arabic-meaning {
        font-size: 1.1rem;
    }
    
    .word-meaning {
        font-size: 0.9rem;
    }
}

/* تأثيرات الحركة */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.vocab-card.playing {
    animation: pulse 2s infinite;
}

/* تحسين إمكانية الوصول */
.vocab-card:focus {
    outline: 3px solid #667eea;
    outline-offset: 2px;
}

.play-button:focus {
    outline: 2px solid white;
    outline-offset: 2px;
}

/* تحسين النصوص الطويلة */
.word-meaning {
    word-wrap: break-word;
    hyphens: auto;
}