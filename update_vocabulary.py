#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لتحديث ملف JavaScript بالمفردات المستخرجة
Script to update JavaScript file with extracted vocabulary
"""

import json
import os
import re

def load_extracted_vocabulary():
    """تحميل المفردات المستخرجة"""
    files_to_check = [
        'vocabulary_data_complete.json',
        'vocabulary_data.json'
    ]

    for filename in files_to_check:
        if os.path.exists(filename):
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"✅ تم تحميل المفردات من {filename}")
                return data
            except Exception as e:
                print(f"❌ خطأ في تحميل {filename}: {e}")
                continue

    print("❌ لم يتم العثور على ملف المفردات")
    return None

def update_javascript_file(vocabulary_data):
    """تحديث ملف JavaScript بالمفردات الجديدة"""
    try:
        # قراءة ملف JavaScript الحالي
        with open('script.js', 'r', encoding='utf-8') as f:
            js_content = f.read()

        # إنشاء بيانات المفردات الجديدة
        js_vocab_data = "const vocabularyData = " + json.dumps(vocabulary_data, ensure_ascii=False, indent=4) + ";"

        # البحث عن بداية ونهاية بيانات المفردات
        start_pattern = r'const vocabularyData\s*=\s*{'
        end_pattern = r'};'

        # العثور على الموقع
        start_match = re.search(start_pattern, js_content)
        if start_match:
            start_pos = start_match.start()

            # البحث عن النهاية بعد البداية
            remaining_content = js_content[start_pos:]
            brace_count = 0
            end_pos = start_pos

            for i, char in enumerate(remaining_content):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_pos = start_pos + i + 1
                        break

            # استبدال البيانات القديمة بالجديدة
            new_js_content = (
                js_content[:start_pos] +
                js_vocab_data +
                js_content[end_pos:]
            )

            # حفظ الملف المحدث
            with open('script.js', 'w', encoding='utf-8') as f:
                f.write(new_js_content)

            print("✅ تم تحديث ملف script.js بنجاح")
            return True
        else:
            print("❌ لم يتم العثور على بيانات المفردات في ملف JavaScript")
            return False

    except Exception as e:
        print(f"❌ خطأ في تحديث ملف JavaScript: {e}")
        return False

def count_vocabulary(vocabulary_data):
    """حساب عدد المفردات والدروس"""
    total_words = 0
    total_lessons = len(vocabulary_data)

    for category, words in vocabulary_data.items():
        total_words += len(words)

    return total_words, total_lessons

def create_backup():
    """إنشاء نسخة احتياطية من ملف JavaScript"""
    try:
        if os.path.exists('script.js'):
            with open('script.js', 'r', encoding='utf-8') as f:
                content = f.read()

            with open('script_backup.js', 'w', encoding='utf-8') as f:
                f.write(content)

            print("✅ تم إنشاء نسخة احتياطية: script_backup.js")
            return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔄 بدء تحديث ملف JavaScript بالمفردات الجديدة")
    print("🔤 سيتم تضمين النطق العربي والصوتيات")
    print("=" * 60)

    # إنشاء نسخة احتياطية
    create_backup()

    # تحميل المفردات المستخرجة
    vocabulary_data = load_extracted_vocabulary()

    if not vocabulary_data:
        print("❌ لا توجد مفردات للتحديث")
        return

    # حساب الإحصائيات
    total_words, total_lessons = count_vocabulary(vocabulary_data)

    # حساب الكلمات التي تحتوي على نطق عربي
    words_with_arabic_phonetic = 0
    words_with_audio = 0

    for category, words in vocabulary_data.items():
        for word in words:
            if word.get('arabic_phonetic'):
                words_with_arabic_phonetic += 1
            if word.get('audio_url') or word.get('audio'):
                words_with_audio += 1

    print(f"📊 المفردات المحملة:")
    print(f"   📝 إجمالي الكلمات: {total_words}")
    print(f"   📚 عدد الدروس: {total_lessons}")
    print(f"   🔤 كلمات بنطق عربي: {words_with_arabic_phonetic}")
    print(f"   🎵 كلمات بصوتيات: {words_with_audio}")

    # تحديث ملف JavaScript
    if update_javascript_file(vocabulary_data):
        print(f"\n🎉 تم التحديث بنجاح!")
        print(f"📁 الملفات المحدثة:")
        print(f"   - script.js (محدث مع النطق العربي)")
        print(f"   - script_backup.js (نسخة احتياطية)")

        print(f"\n✨ الميزات الجديدة:")
        print(f"   🔤 النطق العربي لكل كلمة لاتينية")
        print(f"   🎵 روابط الصوتيات المستخرجة")
        print(f"   🔍 بحث محسن يشمل النطق العربي")

        print(f"\n💡 لتطبيق التحديثات:")
        print(f"   1. أعد تحميل صفحة index.html")
        print(f"   2. أو افتح التطبيق من جديد")
        print(f"   3. جرب البحث بالنطق العربي (مثل: ميك)")
    else:
        print("❌ فشل في التحديث")

if __name__ == "__main__":
    main()