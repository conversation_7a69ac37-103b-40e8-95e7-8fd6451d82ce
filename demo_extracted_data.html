<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎉 عرض البيانات المستخرجة - النطق العربي</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            min-width: 150px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }

        .vocabulary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .vocab-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .vocab-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .lesson-tag {
            background: #e2e8f0;
            color: #4a5568;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-bottom: 10px;
            display: inline-block;
        }

        .armenian-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
            text-align: center;
            margin-bottom: 10px;
            font-family: 'Arial Unicode MS', 'Lucida Grande', sans-serif;
        }

        .phonetic {
            font-size: 1.1rem;
            color: #667eea;
            text-align: center;
            margin-bottom: 10px;
            font-style: italic;
            background: rgba(102, 126, 234, 0.1);
            padding: 8px 15px;
            border-radius: 20px;
            display: inline-block;
            width: 100%;
        }

        .arabic-phonetic {
            font-size: 1rem;
            color: #2c5aa0;
            font-weight: 600;
            margin: 8px 0;
            background: linear-gradient(135deg, #e3f2fd, #f8f9fa);
            padding: 8px 15px;
            border-radius: 20px;
            border: 1px solid #bbdefb;
            text-align: center;
            box-shadow: 0 2px 4px rgba(44, 90, 160, 0.1);
        }

        .arabic-meaning {
            font-size: 1.3rem;
            color: #2d3748;
            font-weight: 600;
            text-align: center;
            margin-bottom: 10px;
        }

        .word-meaning {
            font-size: 0.95rem;
            color: #718096;
            text-align: center;
            line-height: 1.4;
            border-top: 1px solid #e2e8f0;
            padding-top: 10px;
        }

        .loading {
            text-align: center;
            color: white;
            font-size: 1.2rem;
            padding: 50px;
        }

        .error {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            color: #e53e3e;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .vocabulary-grid {
                grid-template-columns: 1fr;
            }
            
            .stats {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 البيانات المستخرجة مع النطق العربي</h1>
            <p>عرض مباشر للـ 80 درس المستخرجة من موقع LingoHut</p>
        </div>

        <div class="stats" id="stats">
            <div class="stat-card">
                <span class="stat-number" id="totalWords">0</span>
                <span class="stat-label">إجمالي الكلمات</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="totalLessons">0</span>
                <span class="stat-label">عدد الدروس</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="wordsWithArabic">0</span>
                <span class="stat-label">كلمات بنطق عربي</span>
            </div>
        </div>

        <div class="loading" id="loading">
            🔄 جاري تحميل البيانات المستخرجة...
        </div>

        <div class="vocabulary-grid" id="vocabularyGrid" style="display: none;">
            <!-- سيتم ملؤها بواسطة JavaScript -->
        </div>
    </div>

    <script>
        async function loadExtractedData() {
            try {
                console.log('🔍 محاولة تحميل البيانات المستخرجة...');
                
                // محاولة تحميل ملف JSON
                const response = await fetch('vocabulary_data_complete.json');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                console.log('✅ تم تحميل البيانات بنجاح:', Object.keys(data).length, 'درس');
                
                return data;
                
            } catch (error) {
                console.error('❌ خطأ في تحميل البيانات:', error);
                
                // عرض رسالة خطأ
                document.getElementById('loading').innerHTML = `
                    <div class="error">
                        <h3>❌ لم يتم العثور على البيانات المستخرجة</h3>
                        <p>تأكد من تشغيل عملية الاستخراج أولاً</p>
                        <p>انقر نقراً مزدوجاً على: <strong>run_extraction_with_check.bat</strong></p>
                    </div>
                `;
                
                return null;
            }
        }

        function displayData(vocabularyData) {
            const grid = document.getElementById('vocabularyGrid');
            const loading = document.getElementById('loading');
            
            // حساب الإحصائيات
            let totalWords = 0;
            let wordsWithArabic = 0;
            const totalLessons = Object.keys(vocabularyData).length;
            
            // تحويل البيانات إلى مصفوفة
            const allWords = [];
            for (const [lessonKey, words] of Object.entries(vocabularyData)) {
                if (Array.isArray(words)) {
                    words.forEach(word => {
                        allWords.push(word);
                        totalWords++;
                        if (word.arabic_phonetic) {
                            wordsWithArabic++;
                        }
                    });
                }
            }
            
            // تحديث الإحصائيات
            document.getElementById('totalWords').textContent = totalWords;
            document.getElementById('totalLessons').textContent = totalLessons;
            document.getElementById('wordsWithArabic').textContent = wordsWithArabic;
            
            // عرض البطاقات
            grid.innerHTML = allWords.map((word, index) => `
                <div class="vocab-card">
                    <div class="lesson-tag">${word.lesson}</div>
                    <div class="armenian-text">${word.armenian}</div>
                    <div class="phonetic">(${word.phonetic})</div>
                    ${word.arabic_phonetic ? `<div class="arabic-phonetic">🔤 ${word.arabic_phonetic}</div>` : ''}
                    <div class="arabic-meaning">${word.arabic}</div>
                    <div class="word-meaning">${word.meaning}</div>
                </div>
            `).join('');
            
            // إخفاء التحميل وإظهار البيانات
            loading.style.display = 'none';
            grid.style.display = 'grid';
            
            console.log(`📊 تم عرض ${totalWords} كلمة من ${totalLessons} درس`);
            console.log(`🔤 ${wordsWithArabic} كلمة تحتوي على نطق عربي`);
        }

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', async function() {
            const data = await loadExtractedData();
            
            if (data) {
                displayData(data);
            }
        });
    </script>
</body>
</html>
