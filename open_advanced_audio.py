#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فتح مولد الصوت الأرمني المتقدم
Open Advanced Armenian Audio Generator
"""

import webbrowser
from pathlib import Path
import json

def main():
    print("🔊 فتح مولد الصوت الأرمني المتقدم")
    print("=" * 50)
    
    # التحقق من وجود الملفات
    audio_generator = Path('advanced_audio_generator.html')
    vocab_file = Path('armenian_vocabulary_final.json')
    
    if not audio_generator.exists():
        print("❌ ملف مولد الصوت المتقدم غير موجود")
        return
    
    if not vocab_file.exists():
        print("❌ ملف المفردات غير موجود")
        print("💡 شغل: python quick_armenian_extractor.py")
        return
    
    # قراءة إحصائيات المفردات
    try:
        with open(vocab_file, 'r', encoding='utf-8') as f:
            vocabulary = json.load(f)
        
        total_categories = len(vocabulary)
        total_words = sum(len(cat['words']) for cat in vocabulary.values())
        
        print("✅ جميع الملفات موجودة")
        print(f"📊 المفردات: {total_words} كلمة في {total_categories} فئة")
        print()
        
    except Exception as e:
        print(f"⚠️ خطأ في قراءة المفردات: {e}")
    
    # فتح مولد الصوت المتقدم
    try:
        file_url = audio_generator.as_uri()
        webbrowser.open(file_url)
        
        print("🚀 تم فتح مولد الصوت المتقدم!")
        print()
        print("🎯 ميزات المولد المتقدم:")
        print("   ✅ واجهة جميلة ومتطورة")
        print("   ✅ توليد صوت لـ 110 كلمة أرمينية")
        print("   ✅ شريط تقدم تفاعلي")
        print("   ✅ تشغيل فردي أو جماعي")
        print("   ✅ تحميل بيانات JSON شاملة")
        print("   ✅ أسماء ملفات منظمة")
        print("   ✅ معلومات كاملة لكل كلمة")
        print()
        print("🔊 كيفية الاستخدام:")
        print("   1️⃣ انقر 'توليد جميع الأصوات'")
        print("   2️⃣ انتظر حتى يكتمل التوليد")
        print("   3️⃣ انقر 'تحميل البيانات الصوتية'")
        print("   4️⃣ احفظ ملف armenian_complete_audio_data.json")
        print()
        print("📱 للتطبيق الموبايل:")
        print("   📄 ملف JSON شامل مع جميع البيانات")
        print("   🔊 أسماء ملفات صوتية منظمة")
        print("   📁 معلومات كاملة لكل كلمة")
        print("   🎯 جاهز للاستخدام في React Native/Flutter")
        print()
        print("🎵 ميزات الصوت:")
        print("   🔊 نطق واضح وعالي الجودة")
        print("   ⚡ توليد سريع ومتتالي")
        print("   🎛️ تحكم في السرعة والنبرة")
        print("   📊 إحصائيات مباشرة")
        print()
        print("🎉 استمتع بتوليد أفضل الأصوات الأرمينية!")
        
    except Exception as e:
        print(f"❌ فشل فتح مولد الصوت: {e}")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
