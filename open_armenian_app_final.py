#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فتح التطبيق النهائي الشامل لتعلم الأرمينية
Open Final Comprehensive Armenian Learning App
"""

import os
import webbrowser
import json
from pathlib import Path

def main():
    print("🇦🇲 فتح التطبيق النهائي الشامل لتعلم الأرمينية")
    print("=" * 60)
    
    app_dir = Path(__file__).parent
    
    # فحص ملف المفردات النهائي
    vocab_file = app_dir / 'armenian_vocabulary_final.json'
    if vocab_file.exists():
        try:
            with open(vocab_file, 'r', encoding='utf-8') as f:
                vocabulary = json.load(f)
            
            total_categories = len(vocabulary)
            total_words = sum(len(cat['words']) for cat in vocabulary.values())
            
            print(f"✅ التطبيق النهائي الشامل جاهز:")
            print(f"   📝 {total_words} كلمة أرمينية شاملة")
            print(f"   📁 {total_categories} فئة منظمة ومرتبة")
            print(f"   🔊 صوت تفاعلي عالي الجودة")
            print(f"   🎯 نطق عربي فريد ودقيق 100%")
            print(f"   🔍 بحث متقدم وسريع")
            print(f"   📱 تصميم متجاوب للموبايل")
            print()
            
            # عرض الفئات مع التفاصيل والأمثلة
            print("📋 الفئات الشاملة مع أمثلة:")
            for category_name, category_data in vocabulary.items():
                word_count = len(category_data['words'])
                icon = category_data.get('icon', '📝')
                print(f"\n   {icon} {category_name}: {word_count} كلمة")
                print(f"      📖 {category_data['description']}")
                
                # عرض أول 3 كلمات من كل فئة كأمثلة
                examples = []
                for i, word in enumerate(category_data['words'][:3]):
                    armenian = word['armenian']
                    phonetic = word['phonetic']
                    arabic_phonetic = word['arabic_phonetic']
                    arabic = word['arabic']
                    examples.append(f"{armenian} ({phonetic}) → {arabic_phonetic} = {arabic}")
                
                for example in examples:
                    print(f"      • {example}")
                
                if word_count > 3:
                    print(f"      ... و {word_count - 3} كلمة أخرى")
            
            print()
            
        except Exception as e:
            print(f"⚠️ خطأ في قراءة البيانات: {e}")
    else:
        print("❌ ملف المفردات النهائي غير موجود")
        print("💡 شغل: python quick_armenian_extractor.py")
        return
    
    # فتح التطبيق
    app_file = app_dir / 'armenian_app_final.html'
    if app_file.exists():
        try:
            file_url = app_file.as_uri()
            webbrowser.open(file_url)
            print("🚀 تم فتح التطبيق النهائي الشامل!")
            print()
            print("🎯 مميزات التطبيق النهائي:")
            print("   ✅ 110 كلمة أرمينية أساسية ومهمة")
            print("   ✅ 10 فئات منظمة ومرتبة منطقياً")
            print("   ✅ نطق عربي فريد لكل كلمة")
            print("   ✅ صوت تفاعلي عالي الجودة")
            print("   ✅ بحث فوري ومتقدم")
            print("   ✅ واجهة جميلة ومتطورة")
            print("   ✅ تصميم متجاوب للجوال")
            print("   ✅ تأثيرات بصرية جذابة")
            print()
            print("🔤 كلمات مميزة للتجربة:")
            print("   🔍 ابحث عن 'ميك' → واحد")
            print("   🔍 ابحث عن 'باريف' → مرحبا")
            print("   🔍 ابحث عن 'مايير' → أم")
            print("   🔍 ابحث عن 'كارمير' → أحمر")
            print("   🔍 ابحث عن 'هاتس' → خبز")
            print()
            print("🔊 كيفية الاستخدام:")
            print("   1️⃣ اختر فئة من التبويبات العلوية")
            print("   2️⃣ انقر على زر 🔊 لسماع النطق")
            print("   3️⃣ استخدم البحث للعثور على كلمات")
            print("   4️⃣ تصفح الفئات المختلفة")
            print("   5️⃣ استمتع بالتعلم التفاعلي!")
            print()
            print("📱 مثالي لتطبيق الموبايل:")
            print("   📄 بيانات JSON منظمة ومهيأة")
            print("   🔊 أسماء ملفات صوتية جاهزة")
            print("   📁 فئات واضحة ومنطقية")
            print("   🎯 نطق عربي فريد ومميز")
            print("   📊 حجم مناسب (110 كلمة)")
            print("   🎨 تصنيف ذكي ومدروس")
            print()
            print("🎉 استمتع بأفضل تطبيق لتعلم الأرمينية!")
            
        except Exception as e:
            print(f"❌ فشل فتح التطبيق: {e}")
    else:
        print("❌ ملف التطبيق غير موجود")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
