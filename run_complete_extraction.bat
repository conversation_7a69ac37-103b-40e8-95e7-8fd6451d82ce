@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    استخراج شامل للمفردات الأرمينية
echo    Complete Armenian Vocabulary Extraction
echo ========================================
echo.

echo 🚀 بدء عملية الاستخراج الشاملة...
echo.

REM التحقق من وجود Python
py --version >nul 2>&1
if errorlevel 1 (
    python --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Python غير مثبت على النظام
        echo 💡 يرجى تثبيت Python أولاً:
        echo    انقر نقراً مزدوجاً على: install_python_auto.bat
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=python
    )
) else (
    set PYTHON_CMD=py
)

echo ✅ Python متوفر
echo.

REM تثبيت المكتبات المطلوبة
echo 📦 تثبيت المكتبات المطلوبة...
%PYTHON_CMD% -m pip install selenium beautifulsoup4 requests webdriver-manager gtts pygame
if errorlevel 1 (
    echo ❌ فشل في تثبيت بعض المكتبات، محاولة بطريقة أخرى...
    %PYTHON_CMD% -m pip install --user selenium beautifulsoup4 requests webdriver-manager
)

echo ✅ تم تثبيت المكتبات الأساسية
echo.

echo 🔍 بدء استخراج جميع الـ 125 درس من الموقع...
echo 🔤 سيتم إضافة النطق العربي لكل كلمة لاتينية
echo 🎵 سيتم استخراج الصوتيات من كل درس
echo ⏳ هذا قد يستغرق 45-60 دقيقة للحصول على جميع الدروس...
echo.
echo أمثلة على النطق العربي:
echo   Meg → ميك
echo   Barev → باريف
echo   Bari luys → باري لويس
echo.

REM تشغيل سكريبت الاستخراج المتقدم
echo 🚀 بدء الاستخراج المتقدم...
%PYTHON_CMD% advanced_scraper.py
if errorlevel 1 (
    echo ❌ فشل في الاستخراج المتقدم
    echo 💡 جاري المحاولة بطريقة مبسطة...
    %PYTHON_CMD% simple_scraper.py
)

echo.
echo 🔄 تحديث ملف JavaScript...
if exist update_vocabulary.py (
    %PYTHON_CMD% update_vocabulary.py
) else (
    echo ⚠️ ملف التحديث غير موجود، تخطي هذه الخطوة
)

echo.
echo 🎵 استخراج الصوتيات...
if exist audio_extractor.py (
    %PYTHON_CMD% audio_extractor.py
) else (
    echo ⚠️ مستخرج الصوتيات غير موجود، تخطي هذه الخطوة
)

echo.
echo 🌐 فتح التطبيق في المتصفح...
start index.html

echo.
echo ✅ تم الانتهاء من جميع العمليات!
echo.
echo 📋 ما تم إنجازه:
echo    ✓ استخراج المفردات من الموقع
echo    ✓ تحديث ملف JavaScript
echo    ✓ إنشاء الملفات الصوتية
echo    ✓ فتح التطبيق في المتصفح
echo.
echo 📁 الملفات المُنشأة:
echo    - vocabulary_data_complete.json (بيانات JSON مع النطق العربي)
echo    - vocabulary_data_complete.js (JavaScript مع النطق العربي)
echo    - armenian_vocabulary_complete.txt (ملف نصي شامل)
echo    - audio/ (مجلد الملفات الصوتية المستخرجة)
echo.
echo 📊 النتائج المتوقعة:
echo    - 125 درس كامل
echo    - ~2000-3000 كلمة مع النطق العربي
echo    - صوتيات مستخرجة من الموقع
echo.
echo 💡 لإعادة تشغيل التطبيق فقط: انقر نقراً مزدوجاً على run_app.bat
echo.

pause