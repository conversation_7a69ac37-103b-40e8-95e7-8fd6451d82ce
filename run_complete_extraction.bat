@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    استخراج شامل للمفردات الأرمينية
echo    Complete Armenian Vocabulary Extraction
echo ========================================
echo.

echo 🚀 بدء عملية الاستخراج الشاملة...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

REM تثبيت المكتبات المطلوبة
echo 📦 تثبيت المكتبات المطلوبة...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ فشل في تثبيت المكتبات
    pause
    exit /b 1
)

echo ✅ تم تثبيت المكتبات بنجاح
echo.

REM تثبيت ChromeDriver
echo 🌐 تثبيت ChromeDriver...
pip install webdriver-manager
echo.

echo 🔍 بدء استخراج جميع الـ 125 درس من الموقع...
echo 🔤 سيتم إضافة النطق العربي لكل كلمة لاتينية
echo ⏳ هذا قد يستغرق 30-45 دقيقة...
echo.
echo مثال: كلمة "Meg" ستصبح "ميك"
echo.

REM تشغيل سكريبت الاستخراج المتقدم
python advanced_scraper.py
if errorlevel 1 (
    echo ❌ فشل في استخراج المفردات
    echo 💡 جاري المحاولة بالسكريبت البديل...
    python scraper.py
)

echo.
echo 🔄 تحديث ملف JavaScript...
python update_vocabulary.py

echo.
echo 🎵 إنشاء الملفات الصوتية...
python audio_generator.py

echo.
echo 🌐 فتح التطبيق في المتصفح...
start index.html

echo.
echo ✅ تم الانتهاء من جميع العمليات!
echo.
echo 📋 ما تم إنجازه:
echo    ✓ استخراج المفردات من الموقع
echo    ✓ تحديث ملف JavaScript
echo    ✓ إنشاء الملفات الصوتية
echo    ✓ فتح التطبيق في المتصفح
echo.
echo 📁 الملفات المُنشأة:
echo    - vocabulary_data_complete.json (بيانات JSON)
echo    - vocabulary_data_complete.js (مع النطق العربي)
echo    - armenian_vocabulary_complete.txt (ملف نصي شامل)
echo    - audio/ (مجلد الملفات الصوتية)
echo.
echo 💡 لإعادة تشغيل التطبيق فقط: انقر نقراً مزدوجاً على run_app.bat
echo.

pause