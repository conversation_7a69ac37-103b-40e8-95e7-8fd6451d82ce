#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فتح تطبيق تعلم الأرمينية مع الصوت المدمج
Open Armenian Learning App with Integrated Audio
"""

import webbrowser
from pathlib import Path
import json

def main():
    print("🔊 فتح تطبيق تعلم الأرمينية مع الصوت المدمج")
    print("=" * 60)
    
    app_dir = Path(__file__).parent
    
    # التحقق من وجود الملفات
    app_file = app_dir / 'armenian_app_with_audio.html'
    vocab_file = app_dir / 'armenian_vocabulary_final.json'
    js_file = app_dir / 'armenian_vocabulary_final.js'
    
    missing_files = []
    if not app_file.exists():
        missing_files.append("armenian_app_with_audio.html")
    if not vocab_file.exists():
        missing_files.append("armenian_vocabulary_final.json")
    if not js_file.exists():
        missing_files.append("armenian_vocabulary_final.js")
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n💡 شغل: python quick_armenian_extractor.py أولاً")
        return
    
    # قراءة إحصائيات المفردات
    try:
        with open(vocab_file, 'r', encoding='utf-8') as f:
            vocabulary = json.load(f)
        
        total_categories = len(vocabulary)
        total_words = sum(len(cat['words']) for cat in vocabulary.values())
        
        print("✅ جميع الملفات موجودة")
        print(f"📊 المفردات: {total_words} كلمة في {total_categories} فئة")
        print()
        
        # عرض عينة من الكلمات
        print("🔤 عينة من الكلمات مع النطق العربي:")
        sample_count = 0
        for category_name, category_data in vocabulary.items():
            if sample_count >= 5:
                break
            for word in category_data['words'][:2]:
                print(f"   • {word['armenian']} ({word['phonetic']}) → {word['arabic_phonetic']} = {word['arabic']}")
                sample_count += 1
                if sample_count >= 5:
                    break
        print("   ... والمزيد")
        print()
        
    except Exception as e:
        print(f"⚠️ خطأ في قراءة المفردات: {e}")
    
    # فتح التطبيق
    try:
        absolute_path = app_file.resolve()
        file_url = absolute_path.as_uri()
        webbrowser.open(file_url)
        
        print("🚀 تم فتح التطبيق مع الصوت المدمج!")
        print()
        print("🎯 مميزات التطبيق المحسن:")
        print("   ✅ 110 كلمة أرمينية مع صوت عالي الجودة")
        print("   ✅ إعدادات صوت متقدمة (السرعة، النبرة، الصوت)")
        print("   ✅ نطق عربي فريد لكل كلمة")
        print("   ✅ تأثيرات بصرية أثناء التشغيل")
        print("   ✅ واجهة جميلة ومتطورة")
        print("   ✅ بحث متقدم وسريع")
        print("   ✅ تصميم متجاوب للموبايل")
        print()
        print("🔊 إعدادات الصوت المتقدمة:")
        print("   🎛️ تحكم في سرعة النطق (0.3x - 1.5x)")
        print("   🎵 تحكم في نبرة الصوت (0.5 - 2.0)")
        print("   🔉 تحكم في مستوى الصوت (10% - 100%)")
        print("   🎵 اختبار فوري للإعدادات")
        print()
        print("🎮 كيفية الاستخدام:")
        print("   1️⃣ اضبط إعدادات الصوت حسب تفضيلك")
        print("   2️⃣ انقر 'اختبار الإعدادات' للتجربة")
        print("   3️⃣ تصفح الفئات أو استخدم البحث")
        print("   4️⃣ انقر 🔊 لسماع النطق مع التأثيرات")
        print("   5️⃣ شاهد النطق العربي الفريد")
        print()
        print("🔤 كلمات مميزة للتجربة:")
        print("   🔍 ابحث عن 'ميك' → واحد")
        print("   🔍 ابحث عن 'باريف' → مرحبا")
        print("   🔍 ابحث عن 'مايير' → أم")
        print("   🔍 ابحث عن 'كارمير' → أحمر")
        print("   🔍 ابحث عن 'شنورهاكالوتيون' → شكراً")
        print()
        print("📱 مثالي للتطبيق الموبايل:")
        print("   📄 بيانات JSON منظمة ومهيأة")
        print("   🔊 نظام صوت متقدم وقابل للتخصيص")
        print("   📁 110 كلمة مختارة بعناية")
        print("   🎯 نطق عربي فريد ومميز")
        print("   📊 تصنيف ذكي ومنطقي")
        print()
        print("🎉 استمتع بأفضل تجربة تعلم أرمينية مع الصوت!")
        
    except Exception as e:
        print(f"❌ فشل فتح التطبيق: {e}")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
