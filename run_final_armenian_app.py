#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل التطبيق النهائي الشامل لتعلم الأرمينية
Run Final Comprehensive Armenian Learning App
"""

import webbrowser
from pathlib import Path
import json
import time

def main():
    print("🇦🇲 التطبيق النهائي الشامل لتعلم اللغة الأرمينية")
    print("=" * 80)
    
    app_dir = Path(__file__).parent
    
    # قائمة التطبيقات المتاحة
    apps = {
        '1': {
            'name': 'التطبيق المبني على Excel (الأفضل ⭐)',
            'file': 'armenian_app_excel_based.html',
            'description': '743 كلمة مع 703 ملف صوتي حقيقي من ملف Excel',
            'features': ['743 كلمة', '703 صوت حقيقي', '22 فئة', 'من Excel'],
            'priority': 1
        },
        '2': {
            'name': 'التطبيق مع الصوت المدمج',
            'file': 'armenian_app_with_audio.html',
            'description': 'تطبيق متقدم مع إعدادات صوت قابلة للتخصيص',
            'features': ['110 كلمة', 'إعدادات صوت', 'تأثيرات بصرية', 'TTS متقدم'],
            'priority': 2
        },
        '3': {
            'name': 'التطبيق النهائي الشامل',
            'file': 'armenian_app_final.html',
            'description': 'تطبيق التعلم التفاعلي الكلاسيكي',
            'features': ['110 كلمة', 'واجهة جميلة', 'بحث متقدم', 'فئات منظمة'],
            'priority': 3
        },
        '4': {
            'name': 'مولد الصوت المتقدم',
            'file': 'advanced_audio_generator.html',
            'description': 'توليد وتحميل ملفات صوتية للموبايل',
            'features': ['توليد أصوات', 'تحميل JSON', 'للموبايل', 'واجهة متطورة'],
            'priority': 4
        }
    }
    
    # فحص التطبيقات المتاحة
    available_apps = {}
    print("🔍 فحص التطبيقات المتاحة:")
    
    for key, app_info in apps.items():
        file_path = app_dir / app_info['file']
        if file_path.exists():
            available_apps[key] = {**app_info, 'path': file_path}
            status = "✅"
            if app_info['priority'] == 1:
                status += " (الأفضل)"
            print(f"   {status} {key}. {app_info['name']}")
        else:
            print(f"   ❌ {key}. {app_info['name']} (غير متوفر)")
    
    if not available_apps:
        print(f"\n❌ لا توجد تطبيقات متاحة")
        print(f"💡 شغل: python excel_audio_processor.py أولاً")
        return
    
    # فحص البيانات
    print(f"\n📊 فحص البيانات:")
    
    # بيانات Excel
    excel_json = app_dir / 'armenian_vocabulary_from_excel.json'
    excel_js = app_dir / 'armenian_vocabulary_from_excel.js'
    if excel_json.exists() and excel_js.exists():
        try:
            with open(excel_json, 'r', encoding='utf-8') as f:
                excel_data = json.load(f)
            excel_words = sum(len(cat['words']) for cat in excel_data.values())
            excel_audio = sum(1 for cat in excel_data.values() for word in cat['words'] if word.get('has_real_audio'))
            print(f"   ✅ بيانات Excel: {excel_words} كلمة، {excel_audio} صوت حقيقي")
        except:
            print(f"   ⚠️ بيانات Excel: خطأ في القراءة")
    else:
        print(f"   ❌ بيانات Excel: غير متوفرة")
    
    # بيانات أساسية
    basic_json = app_dir / 'armenian_vocabulary_final.json'
    basic_js = app_dir / 'armenian_vocabulary_final.js'
    if basic_json.exists() and basic_js.exists():
        try:
            with open(basic_json, 'r', encoding='utf-8') as f:
                basic_data = json.load(f)
            basic_words = sum(len(cat['words']) for cat in basic_data.values())
            print(f"   ✅ بيانات أساسية: {basic_words} كلمة")
        except:
            print(f"   ⚠️ بيانات أساسية: خطأ في القراءة")
    else:
        print(f"   ❌ بيانات أساسية: غير متوفرة")
    
    # مجلد الصوتيات
    audio_dir = app_dir / 'armenian_audio'
    if audio_dir.exists():
        audio_files = list(audio_dir.glob('*.mp3')) + list(audio_dir.glob('*.wav')) + list(audio_dir.glob('*.ogg'))
        print(f"   ✅ مجلد الصوتيات: {len(audio_files)} ملف")
    else:
        print(f"   ❌ مجلد الصوتيات: غير موجود")
    
    # عرض التوصية
    print(f"\n🎯 التوصية:")
    if '1' in available_apps and excel_json.exists():
        print(f"   ⭐ الأفضل: التطبيق المبني على Excel")
        print(f"   📊 743 كلمة مع 703 ملف صوتي حقيقي")
        print(f"   🔊 أفضل جودة صوت من الملفات الحقيقية")
    elif '2' in available_apps:
        print(f"   ⭐ الأفضل: التطبيق مع الصوت المدمج")
        print(f"   🎵 إعدادات صوت متقدمة")
    else:
        print(f"   ⭐ الأفضل: أي تطبيق متوفر")
    
    # عرض قائمة التطبيقات
    print(f"\n🚀 التطبيقات المتاحة:")
    for key, app_info in available_apps.items():
        print(f"\n   {key}️⃣ {app_info['name']}")
        print(f"      📖 {app_info['description']}")
        print(f"      ✨ المميزات: {' | '.join(app_info['features'])}")
    
    print(f"\n   5️⃣ فتح أفضل تطبيق متوفر")
    print(f"   6️⃣ فتح جميع التطبيقات")
    print(f"   0️⃣ خروج")
    
    # حلقة الاختيار
    while True:
        try:
            choice = input(f"\n🎯 اختر التطبيق (0-6): ").strip()
            
            if choice == '0':
                print("👋 وداعاً!")
                break
            elif choice in available_apps:
                open_single_app(available_apps[choice])
            elif choice == '5':
                open_best_app(available_apps)
            elif choice == '6':
                open_all_apps(available_apps)
            else:
                print("❌ اختيار غير صحيح، جرب مرة أخرى")
                
        except KeyboardInterrupt:
            print("\n👋 تم الإلغاء")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")

def open_single_app(app_info):
    """فتح تطبيق واحد"""
    try:
        absolute_path = app_info['path'].resolve()
        file_url = absolute_path.as_uri()
        webbrowser.open(file_url)
        
        print(f"\n🚀 تم فتح {app_info['name']}!")
        print(f"📖 {app_info['description']}")
        print(f"✨ المميزات: {' | '.join(app_info['features'])}")
        
        # تعليمات خاصة
        if 'Excel' in app_info['name']:
            print(f"\n🔊 تعليمات الاستخدام:")
            print(f"   1️⃣ تصفح 22 فئة مختلفة")
            print(f"   2️⃣ انقر 🔊 (أخضر) للصوت الحقيقي")
            print(f"   3️⃣ انقر 🎵 (أحمر) للصوت المولد")
            print(f"   4️⃣ استخدم البحث في 743 كلمة")
            print(f"   5️⃣ استمتع بالنطق العربي الفريد")
        elif 'الصوت المدمج' in app_info['name']:
            print(f"\n🎵 تعليمات الاستخدام:")
            print(f"   1️⃣ اضبط إعدادات الصوت (السرعة، النبرة)")
            print(f"   2️⃣ انقر 'اختبار الإعدادات'")
            print(f"   3️⃣ تصفح الفئات أو استخدم البحث")
            print(f"   4️⃣ انقر 🔊 لسماع النطق المخصص")
        elif 'مولد الصوت' in app_info['name']:
            print(f"\n🎵 تعليمات الاستخدام:")
            print(f"   1️⃣ انقر 'توليد جميع الأصوات'")
            print(f"   2️⃣ انتظر حتى يكتمل التوليد")
            print(f"   3️⃣ انقر 'تحميل البيانات الصوتية'")
            print(f"   4️⃣ احفظ الملف للاستخدام في الموبايل")
        else:
            print(f"\n📚 تعليمات الاستخدام:")
            print(f"   1️⃣ تصفح الفئات المختلفة")
            print(f"   2️⃣ انقر 🔊 لسماع النطق")
            print(f"   3️⃣ استخدم البحث للعثور على كلمات")
            print(f"   4️⃣ استمتع بالتعلم التفاعلي")
        
    except Exception as e:
        print(f"❌ فشل فتح {app_info['name']}: {e}")

def open_best_app(available_apps):
    """فتح أفضل تطبيق متوفر"""
    # ترتيب حسب الأولوية
    best_app = min(available_apps.values(), key=lambda x: x['priority'])
    
    print(f"\n🌟 فتح أفضل تطبيق متوفر...")
    open_single_app(best_app)

def open_all_apps(available_apps):
    """فتح جميع التطبيقات المتاحة"""
    print(f"\n🚀 فتح جميع التطبيقات المتاحة...")
    
    # ترتيب حسب الأولوية
    sorted_apps = sorted(available_apps.values(), key=lambda x: x['priority'])
    
    opened_count = 0
    for app_info in sorted_apps:
        try:
            absolute_path = app_info['path'].resolve()
            file_url = absolute_path.as_uri()
            webbrowser.open(file_url)
            print(f"   ✅ تم فتح {app_info['name']}")
            opened_count += 1
            time.sleep(1.5)  # توقف قصير بين فتح التطبيقات
        except Exception as e:
            print(f"   ❌ فشل فتح {app_info['name']}: {e}")
    
    print(f"\n🎉 تم فتح {opened_count} تطبيق!")
    print(f"\n📋 ملخص التطبيقات المفتوحة:")
    
    for app_info in sorted_apps:
        print(f"   🎯 {app_info['name']}")
        print(f"      📖 {app_info['description']}")
    
    print(f"\n💡 نصائح للاستخدام:")
    print(f"   📱 استخدم التطبيق المبني على Excel للأفضل")
    print(f"   🔊 جرب الصوت الحقيقي مقابل المولد")
    print(f"   🎵 استخدم مولد الصوت لإنشاء ملفات للموبايل")
    print(f"   📚 استخدم التطبيقات الأخرى للمقارنة")

if __name__ == "__main__":
    main()
