#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص البيانات المستخرجة
Check Extracted Data
"""

import json
import os

def main():
    print("📊 فحص البيانات المستخرجة")
    print("=" * 40)
    
    # فحص ملف JSON
    json_file = 'vocabulary_data_complete.json'
    if os.path.exists(json_file):
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            lessons = len(data)
            total_words = sum(len(words) for words in data.values())
            words_with_arabic = sum(
                1 for words in data.values() 
                for word in words 
                if word.get('arabic_phonetic')
            )
            
            print(f"✅ البيانات موجودة:")
            print(f"   📚 عدد الدروس: {lessons}")
            print(f"   📝 إجمالي الكلمات: {total_words}")
            print(f"   🔤 كلمات بنطق عربي: {words_with_arabic}")
            
            # عرض عينة من البيانات
            if data:
                print(f"\n📋 عينة من البيانات:")
                first_lesson = list(data.values())[0]
                for i, word in enumerate(first_lesson[:5]):
                    print(f"   {i+1}. {word['armenian']} ({word['phonetic']}) → {word['arabic_phonetic']} = {word['arabic']}")
            
            print(f"\n📁 حجم الملف: {os.path.getsize(json_file):,} بايت")
            
        except Exception as e:
            print(f"❌ خطأ في قراءة البيانات: {e}")
    else:
        print("❌ ملف البيانات غير موجود")
    
    # فحص الملفات الأخرى
    print(f"\n📁 الملفات المتاحة:")
    files = [
        'vocabulary_data_complete.json',
        'vocabulary_data_complete.js', 
        'armenian_vocabulary_complete.txt',
        'index.html',
        'demo_extracted_data.html'
    ]
    
    for file in files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"   ✅ {file} ({size:,} بايت)")
        else:
            print(f"   ❌ {file} مفقود")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
