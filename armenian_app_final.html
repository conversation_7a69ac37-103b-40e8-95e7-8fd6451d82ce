<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🇦🇲 تطبيق تعلم الأرمينية الشامل - 110 كلمة</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.8rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            min-width: 140px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .search-container {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            min-width: 250px;
            padding: 15px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1.1rem;
            font-family: 'Cairo', sans-serif;
            transition: border-color 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .clear-btn {
            background: #e53e3e;
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 12px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            transition: background 0.3s ease;
        }

        .clear-btn:hover {
            background: #c53030;
        }

        .category-tabs {
            display: flex;
            gap: 12px;
            margin-bottom: 25px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .category-tab {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 15px 25px;
            border-radius: 30px;
            font-family: 'Cairo', sans-serif;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .category-tab:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }

        .category-tab.active {
            background: #667eea;
            color: white;
            transform: translateY(-3px);
        }

        .category-section {
            margin-bottom: 50px;
        }

        .category-header {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .category-title {
            font-size: 2rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .category-description {
            color: #666;
            font-size: 1.1rem;
            line-height: 1.5;
        }

        .vocabulary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
        }

        .vocab-card {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 18px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            border: 2px solid transparent;
        }

        .vocab-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .play-button {
            position: absolute;
            top: 20px;
            left: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 1.4rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .play-button:hover {
            transform: scale(1.15);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .play-button:active {
            transform: scale(0.95);
        }

        .armenian-text {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2d3748;
            text-align: center;
            margin-bottom: 15px;
            font-family: 'Arial Unicode MS', 'Lucida Grande', sans-serif;
            line-height: 1.3;
        }

        .phonetic {
            font-size: 1.2rem;
            color: #667eea;
            text-align: center;
            margin-bottom: 15px;
            font-style: italic;
            background: rgba(102, 126, 234, 0.1);
            padding: 12px 18px;
            border-radius: 25px;
            font-weight: 500;
        }

        .arabic-phonetic {
            font-size: 1.2rem;
            color: #2c5aa0;
            font-weight: 600;
            margin: 12px 0;
            background: linear-gradient(135deg, #e3f2fd, #f8f9fa);
            padding: 12px 18px;
            border-radius: 25px;
            border: 2px solid #bbdefb;
            text-align: center;
            box-shadow: 0 3px 8px rgba(44, 90, 160, 0.1);
        }

        .arabic-meaning {
            font-size: 1.5rem;
            color: #2d3748;
            font-weight: 600;
            text-align: center;
            margin-bottom: 15px;
        }

        .word-meaning {
            font-size: 1rem;
            color: #718096;
            text-align: center;
            line-height: 1.5;
            border-top: 2px solid #e2e8f0;
            padding-top: 15px;
        }

        .success {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            color: #38a169;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .audio-status {
            position: fixed;
            bottom: 25px;
            right: 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 30px;
            border-radius: 30px;
            font-size: 1.1rem;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            max-width: 350px;
            text-align: center;
        }

        .audio-status.show {
            opacity: 1;
        }

        .no-results {
            text-align: center;
            color: white;
            padding: 60px 20px;
            font-size: 1.3rem;
        }

        @media (max-width: 768px) {
            .vocabulary-grid {
                grid-template-columns: 1fr;
            }
            
            .stats {
                flex-direction: column;
                align-items: center;
            }

            .search-container {
                flex-direction: column;
            }

            .search-box {
                min-width: 100%;
            }

            .category-tabs {
                justify-content: flex-start;
                overflow-x: auto;
                padding-bottom: 15px;
            }

            .audio-status {
                bottom: 15px;
                right: 15px;
                left: 15px;
                max-width: none;
            }

            .header h1 {
                font-size: 2.2rem;
            }
        }

        .hidden {
            display: none;
        }

        /* تأثيرات إضافية */
        .vocab-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 18px 18px 0 0;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .vocab-card:hover::before {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇦🇲 تطبيق تعلم اللغة الأرمينية الشامل</h1>
            <p>110 كلمة مصنفة في 10 فئات مع النطق العربي الفريد والصوت التفاعلي</p>
        </div>

        <div class="stats" id="stats">
            <div class="stat-card">
                <span class="stat-number" id="totalWords">110</span>
                <span class="stat-label">كلمة أرمينية</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="totalCategories">10</span>
                <span class="stat-label">فئة منظمة</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="currentCategoryWords">0</span>
                <span class="stat-label">كلمات الفئة الحالية</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">100%</span>
                <span class="stat-label">نطق عربي دقيق</span>
            </div>
        </div>

        <div class="success">
            <h3>🎉 تطبيق شامل لتعلم الأرمينية!</h3>
            <p>✅ 110 كلمة مصنفة في 10 فئات مع نطق عربي فريد</p>
            <p>🔊 انقر على زر التشغيل لسماع النطق الصوتي عالي الجودة</p>
            <p>🔍 استخدم البحث للعثور على أي كلمة بسرعة</p>
        </div>

        <div class="controls">
            <div class="search-container">
                <input type="text" id="searchBox" class="search-box" placeholder="🔍 ابحث في 110 كلمة أرمينية (مثل: ميك، باريف، مايير، كارمير)...">
                <button class="clear-btn" onclick="clearSearch()">مسح البحث</button>
            </div>
        </div>

        <div class="category-tabs" id="categoryTabs">
            <!-- سيتم ملؤها بواسطة JavaScript -->
        </div>

        <div id="categoriesContainer">
            <!-- سيتم ملؤها بواسطة JavaScript -->
        </div>
    </div>

    <div class="audio-status" id="audioStatus">
        🔊 جاري تشغيل الصوت...
    </div>

    <script src="armenian_vocabulary_final.js"></script>
    <script>
        let currentCategory = 'all';
        let allWords = [];
        let filteredWords = [];

        function playAudio(word) {
            const audioStatus = document.getElementById('audioStatus');
            audioStatus.classList.add('show');
            audioStatus.innerHTML = `
                🔊 <strong>${word.arabic_phonetic}</strong><br>
                <small>${word.arabic}</small>
            `;
            
            try {
                if ('speechSynthesis' in window) {
                    speechSynthesis.cancel();
                    
                    const utterance = new SpeechSynthesisUtterance(word.phonetic);
                    utterance.lang = 'en-US';
                    utterance.rate = 0.6;
                    utterance.pitch = 1;
                    utterance.volume = 0.9;
                    
                    utterance.onend = function() {
                        setTimeout(() => {
                            audioStatus.classList.remove('show');
                        }, 2000);
                    };
                    
                    utterance.onerror = function() {
                        audioStatus.innerHTML = `❌ خطأ في تشغيل: ${word.arabic_phonetic}`;
                        setTimeout(() => {
                            audioStatus.classList.remove('show');
                        }, 3000);
                    };
                    
                    speechSynthesis.speak(utterance);
                } else {
                    audioStatus.innerHTML = `📢 ${word.arabic_phonetic}<br><small>${word.arabic}</small>`;
                    setTimeout(() => {
                        audioStatus.classList.remove('show');
                    }, 4000);
                }
            } catch (error) {
                console.log('خطأ في تشغيل الصوت:', error);
                audioStatus.innerHTML = `⚠️ غير متوفر: ${word.arabic_phonetic}`;
                setTimeout(() => {
                    audioStatus.classList.remove('show');
                }, 3000);
            }
        }

        function clearSearch() {
            document.getElementById('searchBox').value = '';
            showCategory(currentCategory);
        }

        function initializeApp() {
            if (typeof window.armenianVocabulary === 'undefined') {
                document.getElementById('categoriesContainer').innerHTML = `
                    <div class="no-results">
                        <h3>❌ لم يتم تحميل البيانات</h3>
                        <p>تأكد من وجود ملف armenian_vocabulary_final.js</p>
                    </div>
                `;
                return;
            }

            processData();
            createCategoryTabs();
            updateStats();
            showCategory('all');
            
            document.getElementById('searchBox').addEventListener('input', handleSearch);
            
            console.log('🎉 تم تحميل التطبيق الشامل مع 110 كلمة أرمينية');
        }

        function processData() {
            allWords = [];
            for (const [categoryName, categoryData] of Object.entries(window.armenianVocabulary)) {
                categoryData.words.forEach(word => {
                    allWords.push({
                        ...word,
                        category: categoryName,
                        categoryIcon: categoryData.icon
                    });
                });
            }
        }

        function createCategoryTabs() {
            const tabsContainer = document.getElementById('categoryTabs');
            
            const allTab = document.createElement('button');
            allTab.className = 'category-tab active';
            allTab.innerHTML = '📚 جميع الفئات (110)';
            allTab.onclick = () => showCategory('all');
            tabsContainer.appendChild(allTab);
            
            for (const [categoryName, categoryData] of Object.entries(window.armenianVocabulary)) {
                const tab = document.createElement('button');
                tab.className = 'category-tab';
                tab.innerHTML = `${categoryData.icon} ${categoryName} (${categoryData.words.length})`;
                tab.onclick = () => showCategory(categoryName);
                tabsContainer.appendChild(tab);
            }
        }

        function showCategory(categoryName) {
            currentCategory = categoryName;
            
            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            const container = document.getElementById('categoriesContainer');
            container.innerHTML = '';
            
            if (categoryName === 'all') {
                for (const [catName, catData] of Object.entries(window.armenianVocabulary)) {
                    createCategorySection(catName, catData, container);
                }
            } else {
                const categoryData = window.armenianVocabulary[categoryName];
                createCategorySection(categoryName, categoryData, container);
            }
            
            updateCurrentCategoryStats();
        }

        function createCategorySection(categoryName, categoryData, container) {
            const section = document.createElement('div');
            section.className = 'category-section';
            section.innerHTML = `
                <div class="category-header">
                    <div class="category-title">
                        <span>${categoryData.icon}</span>
                        <span>${categoryName}</span>
                        <span style="font-size: 1.2rem; color: #666;">(${categoryData.words.length} كلمة)</span>
                    </div>
                    <div class="category-description">${categoryData.description}</div>
                </div>
                <div class="vocabulary-grid">
                    ${categoryData.words.map(word => createWordCard(word)).join('')}
                </div>
            `;
            container.appendChild(section);
        }

        function createWordCard(word) {
            return `
                <div class="vocab-card">
                    <button class="play-button" onclick="playAudio(${JSON.stringify(word).replace(/"/g, '&quot;')})">
                        🔊
                    </button>
                    <div class="armenian-text">${word.armenian}</div>
                    <div class="phonetic">(${word.phonetic})</div>
                    <div class="arabic-phonetic">🔤 ${word.arabic_phonetic}</div>
                    <div class="arabic-meaning">${word.arabic}</div>
                    <div class="word-meaning">${word.meaning || 'كلمة أرمينية أساسية'}</div>
                </div>
            `;
        }

        function updateStats() {
            const totalWords = allWords.length;
            const totalCategories = Object.keys(window.armenianVocabulary).length;
            
            document.getElementById('totalWords').textContent = totalWords;
            document.getElementById('totalCategories').textContent = totalCategories;
        }

        function updateCurrentCategoryStats() {
            let currentWords = 0;
            if (currentCategory === 'all') {
                currentWords = allWords.length;
            } else {
                currentWords = window.armenianVocabulary[currentCategory]?.words.length || 0;
            }
            document.getElementById('currentCategoryWords').textContent = currentWords;
        }

        function handleSearch() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            
            if (!searchTerm) {
                showCategory(currentCategory);
                return;
            }
            
            const results = allWords.filter(word => 
                word.armenian.toLowerCase().includes(searchTerm) ||
                word.phonetic.toLowerCase().includes(searchTerm) ||
                word.arabic.includes(searchTerm) ||
                word.arabic_phonetic.includes(searchTerm) ||
                (word.meaning && word.meaning.includes(searchTerm))
            );
            
            const container = document.getElementById('categoriesContainer');
            
            if (results.length > 0) {
                container.innerHTML = `
                    <div class="category-section">
                        <div class="category-header">
                            <div class="category-title">
                                <span>🔍</span>
                                <span>نتائج البحث</span>
                                <span style="font-size: 1.2rem; color: #666;">(${results.length} كلمة)</span>
                            </div>
                            <div class="category-description">البحث عن: "${searchTerm}"</div>
                        </div>
                        <div class="vocabulary-grid">
                            ${results.map(word => createWordCard(word)).join('')}
                        </div>
                    </div>
                `;
            } else {
                container.innerHTML = `
                    <div class="no-results">
                        <h3>🔍 لا توجد نتائج</h3>
                        <p>لم يتم العثور على كلمات تحتوي على: "${searchTerm}"</p>
                        <p>جرب البحث بكلمات أخرى مثل: ميك، باريف، مايير</p>
                    </div>
                `;
            }
        }

        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>
