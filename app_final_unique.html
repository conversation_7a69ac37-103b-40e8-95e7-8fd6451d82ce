<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🇦🇲 تطبيق تعلم الأرمينية - 47 كلمة فريدة</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            padding: 15px 20px;
            border-radius: 15px;
            text-align: center;
            min-width: 120px;
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: #667eea;
            display: block;
        }

        .stat-label {
            font-size: 0.85rem;
            color: #666;
            margin-top: 5px;
        }

        .controls {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            min-width: 200px;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 1rem;
            font-family: 'Cairo', sans-serif;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
        }

        .category-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .category-tab {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            font-family: 'Cairo', sans-serif;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .category-tab:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
        }

        .category-tab.active {
            background: #667eea;
            color: white;
        }

        .category-section {
            margin-bottom: 40px;
        }

        .category-header {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .category-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .category-description {
            color: #666;
            font-size: 1rem;
        }

        .vocabulary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .vocab-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .vocab-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .play-button {
            position: absolute;
            top: 15px;
            left: 15px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            font-size: 1.3rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
        }

        .play-button:hover {
            background: #5a67d8;
            transform: scale(1.1);
        }

        .play-button:active {
            background: #4c51bf;
            transform: scale(0.95);
        }

        .armenian-text {
            font-size: 1.6rem;
            font-weight: 700;
            color: #2d3748;
            text-align: center;
            margin-bottom: 12px;
            font-family: 'Arial Unicode MS', 'Lucida Grande', sans-serif;
            line-height: 1.2;
        }

        .phonetic {
            font-size: 1.1rem;
            color: #667eea;
            text-align: center;
            margin-bottom: 12px;
            font-style: italic;
            background: rgba(102, 126, 234, 0.1);
            padding: 10px 15px;
            border-radius: 20px;
            font-weight: 500;
        }

        .arabic-phonetic {
            font-size: 1.1rem;
            color: #2c5aa0;
            font-weight: 600;
            margin: 10px 0;
            background: linear-gradient(135deg, #e3f2fd, #f8f9fa);
            padding: 10px 15px;
            border-radius: 20px;
            border: 1px solid #bbdefb;
            text-align: center;
            box-shadow: 0 2px 4px rgba(44, 90, 160, 0.1);
        }

        .arabic-meaning {
            font-size: 1.4rem;
            color: #2d3748;
            font-weight: 600;
            text-align: center;
            margin-bottom: 12px;
        }

        .word-meaning {
            font-size: 0.9rem;
            color: #718096;
            text-align: center;
            line-height: 1.4;
            border-top: 1px solid #e2e8f0;
            padding-top: 12px;
        }

        .success {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            color: #38a169;
            margin-bottom: 20px;
        }

        .audio-status {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #667eea;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-size: 1rem;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            max-width: 300px;
        }

        .audio-status.show {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .vocabulary-grid {
                grid-template-columns: 1fr;
            }
            
            .stats {
                flex-direction: column;
                align-items: center;
            }

            .controls {
                flex-direction: column;
            }

            .search-box {
                min-width: 100%;
            }

            .category-tabs {
                justify-content: flex-start;
                overflow-x: auto;
                padding-bottom: 10px;
            }

            .audio-status {
                bottom: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
            }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇦🇲 تطبيق تعلم اللغة الأرمينية</h1>
            <p>47 كلمة فريدة مصنفة في 5 فئات مع النطق العربي والصوت</p>
        </div>

        <div class="stats" id="stats">
            <div class="stat-card">
                <span class="stat-number" id="totalWords">47</span>
                <span class="stat-label">كلمة فريدة</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="totalCategories">5</span>
                <span class="stat-label">فئة منظمة</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="currentCategoryWords">0</span>
                <span class="stat-label">كلمات الفئة الحالية</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">100%</span>
                <span class="stat-label">نطق عربي</span>
            </div>
        </div>

        <div class="success">
            <h3>🎉 تم تنظيم 47 كلمة فريدة بدون تكرار!</h3>
            <p>✅ كل كلمة فريدة ومميزة مع نطق عربي دقيق</p>
            <p>🔊 انقر على زر التشغيل لسماع النطق الصوتي</p>
        </div>

        <div class="controls">
            <input type="text" id="searchBox" class="search-box" placeholder="🔍 ابحث في الكلمات الفريدة (مثل: ميك، باريف، مايير)...">
        </div>

        <div class="category-tabs" id="categoryTabs">
            <!-- سيتم ملؤها بواسطة JavaScript -->
        </div>

        <div id="categoriesContainer">
            <!-- سيتم ملؤها بواسطة JavaScript -->
        </div>
    </div>

    <div class="audio-status" id="audioStatus">
        🔊 جاري تشغيل الصوت...
    </div>

    <script src="vocabulary_categories_unique.js"></script>
    <script>
        let currentCategory = 'all';
        let allWords = [];
        let filteredWords = [];

        function playAudio(word) {
            const audioStatus = document.getElementById('audioStatus');
            audioStatus.classList.add('show');
            audioStatus.innerHTML = `🔊 <strong>${word.arabic_phonetic}</strong><br><small>${word.arabic}</small>`;
            
            try {
                if ('speechSynthesis' in window) {
                    speechSynthesis.cancel();
                    
                    const utterance = new SpeechSynthesisUtterance(word.phonetic);
                    utterance.lang = 'en-US';
                    utterance.rate = 0.6;
                    utterance.pitch = 1;
                    utterance.volume = 0.9;
                    
                    utterance.onend = function() {
                        setTimeout(() => {
                            audioStatus.classList.remove('show');
                        }, 1500);
                    };
                    
                    utterance.onerror = function() {
                        audioStatus.innerHTML = `❌ خطأ في تشغيل: ${word.arabic_phonetic}`;
                        setTimeout(() => {
                            audioStatus.classList.remove('show');
                        }, 2000);
                    };
                    
                    speechSynthesis.speak(utterance);
                } else {
                    audioStatus.innerHTML = `📢 ${word.arabic_phonetic}<br><small>${word.arabic}</small>`;
                    setTimeout(() => {
                        audioStatus.classList.remove('show');
                    }, 3000);
                }
            } catch (error) {
                console.log('خطأ في تشغيل الصوت:', error);
                audioStatus.innerHTML = `⚠️ غير متوفر: ${word.arabic_phonetic}`;
                setTimeout(() => {
                    audioStatus.classList.remove('show');
                }, 2000);
            }
        }

        function initializeApp() {
            if (typeof window.vocabularyCategories === 'undefined') {
                document.getElementById('categoriesContainer').innerHTML = `
                    <div style="text-align: center; color: white; padding: 50px;">
                        <h3>❌ لم يتم تحميل البيانات</h3>
                        <p>تأكد من وجود ملف vocabulary_categories_unique.js</p>
                    </div>
                `;
                return;
            }

            processData();
            createCategoryTabs();
            updateStats();
            showCategory('all');
            
            document.getElementById('searchBox').addEventListener('input', handleSearch);
            
            console.log('📊 تم تحميل التطبيق مع 47 كلمة فريدة');
        }

        function processData() {
            allWords = [];
            for (const [categoryName, categoryData] of Object.entries(window.vocabularyCategories)) {
                categoryData.words.forEach(word => {
                    allWords.push({
                        ...word,
                        category: categoryName,
                        categoryIcon: categoryData.icon
                    });
                });
            }
        }

        function createCategoryTabs() {
            const tabsContainer = document.getElementById('categoryTabs');
            
            const allTab = document.createElement('button');
            allTab.className = 'category-tab active';
            allTab.innerHTML = '📚 جميع الفئات (47)';
            allTab.onclick = () => showCategory('all');
            tabsContainer.appendChild(allTab);
            
            for (const [categoryName, categoryData] of Object.entries(window.vocabularyCategories)) {
                const tab = document.createElement('button');
                tab.className = 'category-tab';
                tab.innerHTML = `${categoryData.icon} ${categoryName} (${categoryData.words.length})`;
                tab.onclick = () => showCategory(categoryName);
                tabsContainer.appendChild(tab);
            }
        }

        function showCategory(categoryName) {
            currentCategory = categoryName;
            
            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            const container = document.getElementById('categoriesContainer');
            container.innerHTML = '';
            
            if (categoryName === 'all') {
                for (const [catName, catData] of Object.entries(window.vocabularyCategories)) {
                    createCategorySection(catName, catData, container);
                }
            } else {
                const categoryData = window.vocabularyCategories[categoryName];
                createCategorySection(categoryName, categoryData, container);
            }
            
            updateCurrentCategoryStats();
        }

        function createCategorySection(categoryName, categoryData, container) {
            const section = document.createElement('div');
            section.className = 'category-section';
            section.innerHTML = `
                <div class="category-header">
                    <div class="category-title">
                        <span>${categoryData.icon}</span>
                        <span>${categoryName}</span>
                        <span style="font-size: 1rem; color: #666;">(${categoryData.words.length} كلمة فريدة)</span>
                    </div>
                    <div class="category-description">${categoryData.description}</div>
                </div>
                <div class="vocabulary-grid">
                    ${categoryData.words.map(word => createWordCard(word)).join('')}
                </div>
            `;
            container.appendChild(section);
        }

        function createWordCard(word) {
            return `
                <div class="vocab-card">
                    <button class="play-button" onclick="playAudio(${JSON.stringify(word).replace(/"/g, '&quot;')})">
                        🔊
                    </button>
                    <div class="armenian-text">${word.armenian}</div>
                    <div class="phonetic">(${word.phonetic})</div>
                    <div class="arabic-phonetic">🔤 ${word.arabic_phonetic}</div>
                    <div class="arabic-meaning">${word.arabic}</div>
                    <div class="word-meaning">${word.meaning || 'كلمة أرمينية فريدة'}</div>
                </div>
            `;
        }

        function updateStats() {
            const totalWords = allWords.length;
            const totalCategories = Object.keys(window.vocabularyCategories).length;
            
            document.getElementById('totalWords').textContent = totalWords;
            document.getElementById('totalCategories').textContent = totalCategories;
        }

        function updateCurrentCategoryStats() {
            let currentWords = 0;
            if (currentCategory === 'all') {
                currentWords = allWords.length;
            } else {
                currentWords = window.vocabularyCategories[currentCategory]?.words.length || 0;
            }
            document.getElementById('currentCategoryWords').textContent = currentWords;
        }

        function handleSearch() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            
            if (!searchTerm) {
                showCategory(currentCategory);
                return;
            }
            
            const results = allWords.filter(word => 
                word.armenian.toLowerCase().includes(searchTerm) ||
                word.phonetic.toLowerCase().includes(searchTerm) ||
                word.arabic.includes(searchTerm) ||
                word.arabic_phonetic.includes(searchTerm) ||
                (word.meaning && word.meaning.includes(searchTerm))
            );
            
            const container = document.getElementById('categoriesContainer');
            container.innerHTML = `
                <div class="category-section">
                    <div class="category-header">
                        <div class="category-title">
                            <span>🔍</span>
                            <span>نتائج البحث</span>
                            <span style="font-size: 1rem; color: #666;">(${results.length} كلمة)</span>
                        </div>
                        <div class="category-description">البحث عن: "${searchTerm}"</div>
                    </div>
                    <div class="vocabulary-grid">
                        ${results.length > 0 ? 
                            results.map(word => createWordCard(word)).join('') :
                            '<div style="grid-column: 1 / -1; text-align: center; padding: 50px; color: #666;">لا توجد نتائج</div>'
                        }
                    </div>
                </div>
            `;
        }

        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>
