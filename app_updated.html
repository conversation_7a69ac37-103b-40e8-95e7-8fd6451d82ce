<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🇦🇲 تطبيق تعلم الأرمينية - 125 درس كامل</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            min-width: 150px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }

        .controls {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            min-width: 200px;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 1rem;
            font-family: 'Cairo', sans-serif;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
        }

        .filter-select {
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 1rem;
            font-family: 'Cairo', sans-serif;
            background: white;
        }

        .vocabulary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .vocab-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .vocab-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .lesson-tag {
            background: #e2e8f0;
            color: #4a5568;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-bottom: 10px;
            display: inline-block;
        }

        .armenian-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
            text-align: center;
            margin-bottom: 10px;
            font-family: 'Arial Unicode MS', 'Lucida Grande', sans-serif;
        }

        .phonetic {
            font-size: 1.1rem;
            color: #667eea;
            text-align: center;
            margin-bottom: 10px;
            font-style: italic;
            background: rgba(102, 126, 234, 0.1);
            padding: 8px 15px;
            border-radius: 20px;
        }

        .arabic-phonetic {
            font-size: 1rem;
            color: #2c5aa0;
            font-weight: 600;
            margin: 8px 0;
            background: linear-gradient(135deg, #e3f2fd, #f8f9fa);
            padding: 8px 15px;
            border-radius: 20px;
            border: 1px solid #bbdefb;
            text-align: center;
            box-shadow: 0 2px 4px rgba(44, 90, 160, 0.1);
        }

        .arabic-meaning {
            font-size: 1.3rem;
            color: #2d3748;
            font-weight: 600;
            text-align: center;
            margin-bottom: 10px;
        }

        .word-meaning {
            font-size: 0.95rem;
            color: #718096;
            text-align: center;
            line-height: 1.4;
            border-top: 1px solid #e2e8f0;
            padding-top: 10px;
        }

        .loading {
            text-align: center;
            color: white;
            font-size: 1.2rem;
            padding: 50px;
        }

        .error {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            color: #e53e3e;
            margin-bottom: 20px;
        }

        .success {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            color: #38a169;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .vocabulary-grid {
                grid-template-columns: 1fr;
            }
            
            .stats {
                flex-direction: column;
                align-items: center;
            }

            .controls {
                flex-direction: column;
            }

            .search-box {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇦🇲 تطبيق تعلم اللغة الأرمينية</h1>
            <p>مع النطق العربي الفريد - 125 درس كامل</p>
        </div>

        <div class="stats" id="stats">
            <div class="stat-card">
                <span class="stat-number" id="totalWords">0</span>
                <span class="stat-label">إجمالي الكلمات</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="totalLessons">0</span>
                <span class="stat-label">عدد الدروس</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="wordsWithArabic">0</span>
                <span class="stat-label">كلمات بنطق عربي</span>
            </div>
        </div>

        <div class="controls">
            <input type="text" id="searchBox" class="search-box" placeholder="🔍 ابحث بالأرمينية، النطق، أو النطق العربي (مثل: ميك، باريف)">
            <select id="lessonFilter" class="filter-select">
                <option value="">جميع الدروس</option>
            </select>
        </div>

        <div class="loading" id="loading">
            🔄 جاري تحميل البيانات المحدثة...
        </div>

        <div class="vocabulary-grid" id="vocabularyGrid" style="display: none;">
            <!-- سيتم ملؤها بواسطة JavaScript -->
        </div>
    </div>

    <script>
        let allWords = [];
        let filteredWords = [];

        async function loadVocabularyData() {
            try {
                console.log('🔍 تحميل البيانات المحدثة...');
                
                // إضافة timestamp لتجنب cache
                const timestamp = new Date().getTime();
                const response = await fetch(`vocabulary_data_complete.json?t=${timestamp}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('✅ تم تحميل البيانات:', Object.keys(data).length, 'درس');
                
                return data;
                
            } catch (error) {
                console.error('❌ خطأ في تحميل البيانات:', error);
                
                // محاولة تحميل من ملف JS كبديل
                try {
                    const jsResponse = await fetch(`vocabulary_data_complete.js?t=${timestamp}`);
                    if (jsResponse.ok) {
                        const jsContent = await jsResponse.text();
                        // استخراج البيانات من ملف JS
                        const match = jsContent.match(/window\.vocabularyData\s*=\s*({[\s\S]*});/);
                        if (match) {
                            const data = JSON.parse(match[1]);
                            console.log('✅ تم تحميل البيانات من ملف JS');
                            return data;
                        }
                    }
                } catch (jsError) {
                    console.error('❌ فشل تحميل ملف JS أيضاً:', jsError);
                }
                
                document.getElementById('loading').innerHTML = `
                    <div class="error">
                        <h3>❌ خطأ في تحميل البيانات</h3>
                        <p>تأكد من وجود ملف vocabulary_data_complete.json</p>
                        <p>أو شغل: python smart_extractor.py</p>
                        <p>خطأ: ${error.message}</p>
                    </div>
                `;
                
                return null;
            }
        }

        function processVocabularyData(vocabularyData) {
            allWords = [];
            const lessons = new Set();
            
            for (const [lessonKey, words] of Object.entries(vocabularyData)) {
                if (Array.isArray(words)) {
                    words.forEach(word => {
                        allWords.push({
                            ...word,
                            lessonKey: lessonKey
                        });
                        lessons.add(word.lesson || lessonKey);
                    });
                }
            }
            
            // تحديث قائمة الدروس
            const lessonFilter = document.getElementById('lessonFilter');
            lessonFilter.innerHTML = '<option value="">جميع الدروس</option>';
            
            Array.from(lessons).sort().forEach(lesson => {
                const option = document.createElement('option');
                option.value = lesson;
                option.textContent = lesson;
                lessonFilter.appendChild(option);
            });
            
            return allWords;
        }

        function updateStats() {
            const totalWords = allWords.length;
            const totalLessons = new Set(allWords.map(w => w.lesson || w.lessonKey)).size;
            const wordsWithArabic = allWords.filter(w => w.arabic_phonetic).length;
            
            document.getElementById('totalWords').textContent = totalWords;
            document.getElementById('totalLessons').textContent = totalLessons;
            document.getElementById('wordsWithArabic').textContent = wordsWithArabic;
        }

        function displayWords(words) {
            const grid = document.getElementById('vocabularyGrid');
            
            if (words.length === 0) {
                grid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; color: white; padding: 50px;">
                        <h3>لا توجد نتائج</h3>
                        <p>جرب البحث بكلمات أخرى</p>
                    </div>
                `;
                return;
            }
            
            grid.innerHTML = words.map((word, index) => `
                <div class="vocab-card" onclick="playAudio('${word.audio || word.phonetic}')">
                    <div class="lesson-tag">${word.lesson || word.lessonKey}</div>
                    <div class="armenian-text">${word.armenian || 'غير متوفر'}</div>
                    <div class="phonetic">(${word.phonetic || 'غير متوفر'})</div>
                    ${word.arabic_phonetic ? `<div class="arabic-phonetic">🔤 ${word.arabic_phonetic}</div>` : ''}
                    <div class="arabic-meaning">${word.arabic || 'غير متوفر'}</div>
                    <div class="word-meaning">${word.meaning || 'كلمة أرمينية'}</div>
                </div>
            `).join('');
        }

        function filterWords() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            const selectedLesson = document.getElementById('lessonFilter').value;
            
            filteredWords = allWords.filter(word => {
                const matchesSearch = !searchTerm || 
                    (word.armenian && word.armenian.toLowerCase().includes(searchTerm)) ||
                    (word.phonetic && word.phonetic.toLowerCase().includes(searchTerm)) ||
                    (word.arabic && word.arabic.includes(searchTerm)) ||
                    (word.arabic_phonetic && word.arabic_phonetic.includes(searchTerm)) ||
                    (word.meaning && word.meaning.includes(searchTerm));
                
                const matchesLesson = !selectedLesson || 
                    (word.lesson && word.lesson.includes(selectedLesson)) ||
                    (word.lessonKey && word.lessonKey.includes(selectedLesson));
                
                return matchesSearch && matchesLesson;
            });
            
            displayWords(filteredWords);
        }

        function playAudio(audioName) {
            // محاولة تشغيل الصوت (إذا كان متوفراً)
            console.log('🔊 محاولة تشغيل:', audioName);
            // يمكن إضافة منطق تشغيل الصوت هنا لاحقاً
        }

        async function initializeApp() {
            const data = await loadVocabularyData();
            
            if (data) {
                processVocabularyData(data);
                updateStats();
                filterWords(); // عرض جميع الكلمات
                
                // إخفاء التحميل وإظهار التطبيق
                document.getElementById('loading').style.display = 'none';
                document.getElementById('vocabularyGrid').style.display = 'grid';
                
                // إضافة مستمعي الأحداث
                document.getElementById('searchBox').addEventListener('input', filterWords);
                document.getElementById('lessonFilter').addEventListener('change', filterWords);
                
                // عرض رسالة نجاح
                const successMsg = document.createElement('div');
                successMsg.className = 'success';
                successMsg.innerHTML = `
                    <h3>🎉 تم تحميل البيانات بنجاح!</h3>
                    <p>✅ ${allWords.length} كلمة من ${new Set(allWords.map(w => w.lesson || w.lessonKey)).size} درس</p>
                    <p>🔤 جميع الكلمات تحتوي على نطق عربي فريد</p>
                `;
                
                document.querySelector('.container').insertBefore(successMsg, document.getElementById('vocabularyGrid'));
                
                // إخفاء رسالة النجاح بعد 5 ثوان
                setTimeout(() => {
                    successMsg.style.display = 'none';
                }, 5000);
                
                console.log(`📊 تم عرض ${allWords.length} كلمة من ${new Set(allWords.map(w => w.lesson || w.lessonKey)).size} درس`);
            }
        }

        // تحميل التطبيق عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>
