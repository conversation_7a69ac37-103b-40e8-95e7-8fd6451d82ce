@echo off
chcp 65001 >nul
cd /d "%~dp0"
echo.
echo ========================================
echo    🇦🇲 فتح تطبيق تعلم الأرمينية
echo    Open Armenian Learning App
========================================
echo.
echo 📍 المجلد: %CD%
echo.

REM فتح التطبيق الرئيسي
if exist "%~dp0index.html" (
    echo 🌐 فتح التطبيق الرئيسي...
    start "" "%~dp0index.html"
    echo ✅ تم فتح index.html
) else (
    echo ❌ index.html غير موجود في المجلد الحالي
)

REM فتح عرض البيانات المستخرجة
if exist "%~dp0demo_extracted_data.html" (
    echo 🎉 فتح عرض البيانات المستخرجة...
    timeout /t 2 /nobreak >nul
    start "" "%~dp0demo_extracted_data.html"
    echo ✅ تم فتح demo_extracted_data.html
) else (
    echo ❌ demo_extracted_data.html غير موجود
)

REM فتح مجلد المشروع
if exist "%~dp0vocabulary_data_complete.json" (
    echo 📊 البيانات المستخرجة موجودة
) else (
    echo ⚠️ لا توجد بيانات مستخرجة
    echo 💡 لاستخراج البيانات: انقر على run_extraction_simple.bat
)

echo.
echo 📁 محتويات المجلد:
dir /b *.html *.json *.js 2>nul

echo.
echo ✅ تم فتح التطبيق!
echo.
pause
