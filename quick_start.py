#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لتطبيق تعلم الأرمينية
Quick Start for Armenian Learning App
"""

import os
import sys
import subprocess
import json
import webbrowser
from pathlib import Path

def detect_python():
    """اكتشاف أمر Python"""
    for cmd in ['py', 'python', 'python3']:
        try:
            result = subprocess.run([cmd, '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return cmd
        except:
            continue
    return None

def install_basic_packages():
    """تثبيت المكتبات الأساسية"""
    python_cmd = detect_python()
    if not python_cmd:
        print("❌ Python غير متاح")
        return False
    
    print("📦 تثبيت المكتبات الأساسية...")
    packages = ['requests', 'beautifulsoup4']
    
    for package in packages:
        try:
            subprocess.run([
                python_cmd, '-m', 'pip', 'install', package, '--quiet'
            ], timeout=30)
        except:
            pass
    
    return True

def check_data():
    """فحص البيانات المستخرجة"""
    app_dir = Path(__file__).parent
    json_file = app_dir / 'vocabulary_data_complete.json'
    
    if json_file.exists():
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            lessons = len(data)
            words = sum(len(w) for w in data.values())
            return True, lessons, words
        except:
            return False, 0, 0
    
    return False, 0, 0

def open_app():
    """فتح التطبيق"""
    app_dir = Path(__file__).parent
    
    # فتح عرض البيانات المستخرجة
    demo_file = app_dir / 'demo_extracted_data.html'
    if demo_file.exists():
        webbrowser.open(demo_file.as_uri())
        print("✅ تم فتح عرض البيانات المستخرجة")
        return True
    
    # فتح التطبيق الرئيسي
    index_file = app_dir / 'index.html'
    if index_file.exists():
        webbrowser.open(index_file.as_uri())
        print("✅ تم فتح التطبيق الرئيسي")
        return True
    
    print("❌ لا توجد ملفات HTML")
    return False

def run_extraction():
    """تشغيل الاستخراج"""
    python_cmd = detect_python()
    if not python_cmd:
        print("❌ Python غير متاح")
        return False
    
    app_dir = Path(__file__).parent
    
    # البحث عن ملف الاستخراج
    scraper_file = None
    for file in ['simple_scraper.py', 'advanced_scraper.py']:
        if (app_dir / file).exists():
            scraper_file = app_dir / file
            break
    
    if not scraper_file:
        print("❌ لا توجد ملفات استخراج")
        return False
    
    print(f"🚀 بدء الاستخراج باستخدام {scraper_file.name}...")
    print("⏳ قد يستغرق 30-60 دقيقة...")
    
    try:
        result = subprocess.run([python_cmd, str(scraper_file)])
        return result.returncode == 0
    except Exception as e:
        print(f"❌ خطأ في الاستخراج: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🇦🇲 تطبيق تعلم اللغة الأرمينية - تشغيل سريع")
    print("=" * 50)
    
    # فحص البيانات
    has_data, lessons, words = check_data()
    
    if has_data:
        print(f"✅ البيانات موجودة: {lessons} درس، {words} كلمة")
        print("🌐 فتح التطبيق...")
        open_app()
    else:
        print("❌ لا توجد بيانات مستخرجة")
        print()
        
        choice = input("هل تريد استخراج البيانات الآن؟ (y/n): ").lower()
        
        if choice == 'y':
            # تثبيت المكتبات
            install_basic_packages()
            
            # تشغيل الاستخراج
            if run_extraction():
                print("\n✅ تم الاستخراج بنجاح!")
                print("🌐 فتح التطبيق...")
                open_app()
            else:
                print("\n❌ فشل الاستخراج")
        else:
            print("💡 لاستخراج البيانات لاحقاً، شغل: python armenian_app_manager.py")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف التطبيق")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
    
    input("\nاضغط Enter للخروج...")
