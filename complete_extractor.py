#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مستخرج شامل لجميع الدروس الأرمينية
Complete Armenian Lessons Extractor
"""

import requests
from bs4 import BeautifulSoup
import json
import re
import time
from datetime import datetime
import random

class CompleteArmenianExtractor:
    def __init__(self):
        self.base_url = "https://www.lingohut.com"
        self.main_page = "/ar/l76/%D8%AA%D8%B9%D9%84%D9%85-%D8%A7%D9%84%D9%84%D8%BA%D8%A9-%D8%A7%D9%84%D8%A3%D8%B1%D9%85%D9%8A%D9%86%D9%8A%D8%A9"
        self.vocabulary_data = {}
        self.total_extracted = 0
        self.session = requests.Session()
        
        # Headers متقدمة لتجنب الحظر
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(self.headers)

    def generate_arabic_phonetic(self, phonetic):
        """تحويل النطق اللاتيني إلى نطق عربي محسن"""
        if not phonetic:
            return ""

        text = phonetic.strip()
        
        # معالجة الحالات الخاصة
        special_cases = {
            'Meg': 'ميك', 'Mek': 'ميك',
            'Chors': 'تشورس',
            'Bari luys': 'باري لويس',
            'Bari yereko': 'باري ييريكو',
            'Bari gisher': 'باري غيشير',
            'Mayr': 'مايير', 'Hayr': 'هايير',
            'Yeghbayr': 'ييغبايير', 'Khoyr': 'خويير'
        }
        
        if text in special_cases:
            return special_cases[text]
        
        # قاموس تحويل شامل
        latin_to_arabic = {
            'ch': 'تش', 'sh': 'ش', 'th': 'ث', 'gh': 'غ',
            'kh': 'خ', 'zh': 'ج', 'ng': 'نغ', 'nk': 'نك',
            'a': 'ا', 'e': 'ي', 'i': 'ي', 'o': 'و', 'u': 'و',
            'A': 'ا', 'E': 'ي', 'I': 'ي', 'O': 'و', 'U': 'و',
            'b': 'ب', 'p': 'ب', 'f': 'ف', 'v': 'ف',
            't': 'ت', 'd': 'د', 's': 'س', 'z': 'ز',
            'k': 'ك', 'g': 'ج', 'q': 'ق', 'c': 'ك',
            'h': 'ه', 'j': 'ج', 'l': 'ل', 'r': 'ر',
            'm': 'م', 'n': 'ن', 'w': 'و', 'y': 'ي', 'x': 'كس'
        }
        
        # معالجة المسافات
        if ' ' in text:
            parts = text.split(' ')
            converted_parts = []
            for part in parts:
                converted_parts.append(self.convert_word(part, latin_to_arabic))
            return ' '.join(converted_parts)
        else:
            return self.convert_word(text, latin_to_arabic)

    def convert_word(self, word, latin_to_arabic):
        """تحويل كلمة واحدة"""
        result = word
        for latin, arabic in sorted(latin_to_arabic.items(), key=lambda x: len(x[0]), reverse=True):
            result = result.replace(latin, arabic)
        
        # تنظيف النتيجة
        cleaned = ""
        for char in result:
            if '\u0600' <= char <= '\u06FF' or char == ' ':
                cleaned += char
        
        return cleaned if cleaned else word

    def get_all_lesson_urls(self):
        """استخراج جميع روابط الدروس"""
        try:
            print("🔍 البحث عن جميع روابط الدروس...")
            
            response = self.session.get(self.base_url + self.main_page, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # البحث عن روابط الدروس بطرق متعددة
            lesson_links = []
            
            # الطريقة الأولى: روابط v477
            links_v477 = soup.find_all('a', href=re.compile(r'/ar/v477'))
            lesson_links.extend(links_v477)
            
            # الطريقة الثانية: روابط تحتوي على "درس"
            links_lesson = soup.find_all('a', string=re.compile(r'درس'))
            lesson_links.extend(links_lesson)
            
            # الطريقة الثالثة: روابط في قوائم الدروس
            lesson_lists = soup.find_all(['ul', 'ol'], class_=re.compile(r'lesson|course'))
            for lesson_list in lesson_lists:
                links = lesson_list.find_all('a')
                lesson_links.extend(links)
            
            # تنظيف وتجميع الروابط
            unique_lessons = set()
            
            for link in lesson_links:
                try:
                    href = link.get('href')
                    text = link.get_text(strip=True)
                    
                    if href and text:
                        if not href.startswith('http'):
                            href = self.base_url + href
                        
                        # فلترة الروابط المناسبة
                        if ('v477' in href or 'درس' in text) and len(text) > 5:
                            unique_lessons.add((href, text))
                
                except Exception:
                    continue
            
            # تحويل إلى قائمة مرتبة
            lessons = []
            for href, text in unique_lessons:
                lesson_num = self.extract_lesson_number(text)
                lessons.append({
                    'url': href,
                    'title': text,
                    'number': lesson_num
                })
            
            # ترتيب حسب رقم الدرس
            lessons.sort(key=lambda x: x['number'])
            
            print(f"✅ تم العثور على {len(lessons)} درس")
            
            # عرض أول 10 دروس
            for i, lesson in enumerate(lessons[:10]):
                print(f"   📖 {lesson['number']}: {lesson['title']}")
            
            if len(lessons) > 10:
                print(f"   ... و {len(lessons) - 10} درس آخر")
            
            return lessons
            
        except Exception as e:
            print(f"❌ خطأ في استخراج روابط الدروس: {e}")
            return []

    def extract_lesson_number(self, title):
        """استخراج رقم الدرس"""
        numbers = re.findall(r'\d+', title)
        return int(numbers[0]) if numbers else 999

    def extract_vocabulary_from_lesson(self, lesson_url, lesson_title):
        """استخراج المفردات من درس واحد"""
        try:
            print(f"📖 معالجة: {lesson_title}")
            
            # توقف عشوائي لتجنب الحظر
            time.sleep(random.uniform(2, 5))
            
            response = self.session.get(lesson_url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            vocabulary = []
            
            # البحث عن النصوص الأرمينية
            armenian_pattern = r'[\u0530-\u058F]+'
            armenian_texts = re.findall(armenian_pattern, response.text)
            
            # البحث عن النطق في الأقواس
            phonetic_pattern = r'\(([A-Za-z][^)]*)\)'
            phonetic_matches = re.findall(phonetic_pattern, response.text)
            
            # تصفية النطق الحقيقي
            phonetic_texts = []
            for p in phonetic_matches:
                if (not any(x in p.lower() for x in ['px', 'deg', 'width', 'height', 'color', 'font']) 
                    and len(p) > 1 and len(p) < 30):
                    phonetic_texts.append(p)
            
            # البحث عن المعاني العربية
            arabic_pattern = r'[\u0600-\u06FF]+'
            arabic_texts = re.findall(arabic_pattern, response.text)
            
            # تجميع البيانات
            max_items = min(len(armenian_texts), len(phonetic_texts))
            
            for i in range(max_items):
                armenian = armenian_texts[i] if i < len(armenian_texts) else ""
                phonetic = phonetic_texts[i] if i < len(phonetic_texts) else ""
                arabic = arabic_texts[i] if i < len(arabic_texts) else "غير متوفر"
                
                if armenian and phonetic:
                    # إنشاء النطق العربي
                    arabic_phonetic = self.generate_arabic_phonetic(phonetic)
                    
                    word_data = {
                        'armenian': armenian,
                        'phonetic': phonetic,
                        'arabic': arabic,
                        'arabic_phonetic': arabic_phonetic,
                        'meaning': f"كلمة من {lesson_title}",
                        'lesson': lesson_title,
                        'audio': re.sub(r'[^a-zA-Z0-9]', '-', phonetic.lower())
                    }
                    
                    vocabulary.append(word_data)
            
            print(f"   ✅ تم استخراج {len(vocabulary)} كلمة")
            return vocabulary
            
        except Exception as e:
            print(f"   ❌ خطأ في معالجة {lesson_title}: {e}")
            return []

    def save_data(self):
        """حفظ البيانات"""
        try:
            # حفظ JSON
            with open('vocabulary_data_complete.json', 'w', encoding='utf-8') as f:
                json.dump(self.vocabulary_data, f, ensure_ascii=False, indent=2)
            
            # حفظ JavaScript
            js_content = f"// بيانات المفردات الأرمينية مع النطق العربي\n"
            js_content += f"// تم الاستخراج في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            js_content += f"window.vocabularyData = {json.dumps(self.vocabulary_data, ensure_ascii=False, indent=2)};\n"
            
            with open('vocabulary_data_complete.js', 'w', encoding='utf-8') as f:
                f.write(js_content)
            
            print(f"✅ تم حفظ البيانات في ملفين")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")

    def run_complete_extraction(self):
        """تشغيل الاستخراج الكامل"""
        print("🚀 بدء الاستخراج الشامل للمفردات الأرمينية")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # استخراج قائمة الدروس
        lessons = self.get_all_lesson_urls()
        
        if not lessons:
            print("❌ لم يتم العثور على أي دروس")
            return
        
        print(f"📚 سيتم معالجة {len(lessons)} درس")
        print("🔤 سيتم إضافة النطق العربي لكل كلمة")
        print()
        
        # معالجة كل درس
        successful_lessons = 0
        failed_lessons = 0
        
        for i, lesson in enumerate(lessons, 1):
            try:
                print(f"[{i}/{len(lessons)}] ", end="")
                
                vocabulary = self.extract_vocabulary_from_lesson(lesson['url'], lesson['title'])
                
                if vocabulary:
                    lesson_key = f"lesson_{lesson['number']}_{lesson['title'].replace(' ', '_')}"
                    self.vocabulary_data[lesson_key] = vocabulary
                    self.total_extracted += len(vocabulary)
                    successful_lessons += 1
                else:
                    failed_lessons += 1
                
                # حفظ التقدم كل 10 دروس
                if i % 10 == 0:
                    self.save_data()
                    print(f"💾 تم حفظ التقدم: {i} درس")
                
            except Exception as e:
                print(f"❌ خطأ في الدرس {i}: {e}")
                failed_lessons += 1
                continue
        
        # حفظ البيانات النهائية
        self.save_data()
        
        # إحصائيات نهائية
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print("🎉 تم الانتهاء من الاستخراج!")
        print(f"📊 النتائج النهائية:")
        print(f"   📝 إجمالي الكلمات: {self.total_extracted}")
        print(f"   ✅ دروس ناجحة: {successful_lessons}")
        print(f"   ❌ دروس فاشلة: {failed_lessons}")
        print(f"   ⏱️ الوقت المستغرق: {duration}")
        print(f"   🎯 معدل النجاح: {(successful_lessons/(successful_lessons+failed_lessons)*100):.1f}%")

def main():
    """الدالة الرئيسية"""
    extractor = CompleteArmenianExtractor()
    extractor.run_complete_extraction()

if __name__ == "__main__":
    main()
