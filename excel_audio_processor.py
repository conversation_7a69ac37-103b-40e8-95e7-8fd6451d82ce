#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
معالج ملف Excel والصوتيات الأرمينية
Armenian Excel and Audio Processor
"""

import pandas as pd
import json
import os
from pathlib import Path
import re
import time
from datetime import datetime

class ExcelAudioProcessor:
    def __init__(self):
        self.excel_file = "armenian_vocabulary_with_audio.xlsx"
        self.audio_dir = Path("armenian_audio")

        self.vocabulary_data = {}
        self.audio_mapping = {}
        self.total_words = 0
        self.total_lessons = 0

    def generate_arabic_phonetic(self, phonetic):
        """تحويل النطق اللاتيني إلى نطق عربي محسن"""
        if not phonetic:
            return ""

        text = phonetic.strip()

        # حالات خاصة شائعة
        special_cases = {
            'Barev': 'باريف', 'Mek': 'ميك', 'Yerku': 'ييركو', 'Yereq': 'ييريك',
            'Chors': 'تشورس', 'Hing': 'هينغ', 'Vets': 'فيتس', 'Yot': 'يوت',
            'Ut': 'اوت', 'Iny': 'يني', 'Das': 'داس', 'Tasny': 'تاسني',
            'Bari luys': 'باري لويس', 'Bari yereko': 'باري ييريكو',
            'Bari gisher': 'باري غيشير', 'Tsdesutyun': 'تسديسوتيون',
            'Shnorhakalutyun': 'شنورهاكالوتيون', 'Neroghutyan': 'نيروغوتيان',
            'Mayr': 'مايير', 'Hayr': 'هايير', 'Yeghbayr': 'ييغبايير',
            'Khoyr': 'خويير', 'Vordi': 'فوردي', 'Aghjik': 'اغجيك',
            'Karmir': 'كارمير', 'Kanach': 'كاناتش', 'Kapuyt': 'كابويت',
            'Deghin': 'ديغين', 'Sev': 'سيف', 'Spitak': 'سبيتاك',
            'Hats': 'هاتس', 'Jur': 'جور', 'Kat': 'كات', 'Mis': 'ميس',
            'Dzuk': 'دزوك', 'Glukh': 'جلوخ', 'Achq': 'اتشك', 'Kit': 'كيت',
            'Beran': 'بيران', 'Dzerrq': 'دزيرك', 'Votq': 'فوتك',
            'Zham': 'جام', 'Rope': 'روبي', 'Or': 'اور', 'Shabat': 'شاباط',
            'Arev': 'اريف', 'Lusin': 'لوسين', 'Astgh': 'استغ', 'Andzrev': 'اندزريف',
            'Utel': 'اوتيل', 'Khmel': 'خميل', 'Qnel': 'كنيل', 'Qaylel': 'كايليل',
            'Ayo': 'ايو', 'Voch': 'فوتش', 'Lav': 'لاف', 'Vat': 'فات'
        }

        if text in special_cases:
            return special_cases[text]

        # تحويل عام محسن
        result = text.lower()

        # تحويلات متقدمة
        replacements = {
            # مجموعات حروف خاصة
            'shn': 'شن', 'tch': 'تش', 'ch': 'تش', 'sh': 'ش', 'th': 'ث',
            'gh': 'غ', 'kh': 'خ', 'zh': 'ج', 'ng': 'نغ', 'nk': 'نك',
            'ts': 'تس', 'dz': 'دز', 'ght': 'غت', 'kht': 'خت',

            # حروف العلة
            'a': 'ا', 'e': 'ي', 'i': 'ي', 'o': 'و', 'u': 'و',
            'á': 'ا', 'é': 'ي', 'í': 'ي', 'ó': 'و', 'ú': 'و',
            'y': 'ي', 'w': 'و',

            # الحروف الساكنة
            'b': 'ب', 'p': 'ب', 'f': 'ف', 'v': 'ف',
            't': 'ت', 'd': 'د', 's': 'س', 'z': 'ز',
            'k': 'ك', 'g': 'ج', 'h': 'ه', 'j': 'ج',
            'l': 'ل', 'r': 'ر', 'm': 'م', 'n': 'ن',
            'c': 'ك', 'q': 'ك', 'x': 'كس'
        }

        # تطبيق التحويلات بترتيب الطول (الأطول أولاً)
        for latin, arabic in sorted(replacements.items(), key=lambda x: len(x[0]), reverse=True):
            result = result.replace(latin, arabic)

        # تنظيف النتيجة
        cleaned = ''.join(char for char in result if '\u0600' <= char <= '\u06FF' or char == ' ')
        return cleaned if cleaned else text

    def determine_category(self, lesson_number, armenian_word, arabic_meaning):
        """تحديد فئة الكلمة بناءً على رقم الدرس والمعنى"""
        if not arabic_meaning:
            return "متنوعة"

        text = arabic_meaning.lower().strip()

        # فئات محددة بناءً على المعنى (بترتيب الأولوية)
        categories = {
            'التحيات والمجاملات': [
                'مرحبا', 'صباح الخير', 'مساء الخير', 'تصبح على خير', 'أراك لاحقا', 'أراك غدا',
                'سررت بلقائك', 'سررت برؤيتك', 'شكرا', 'بخير، شكرا', 'كيف حالك', 'وأنت',
                'ما اسمك', 'اسمي هو', 'من أي بلد أنت', 'أين تعيش', 'عذرا', 'لم أسمعك',
                'أتمنى لك نهارا سعيدا', 'هاي', 'باي', 'وداع', 'تحية'
            ],
            'الأرقام والحساب': [
                'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة',
                'ثمانية', 'تسعة', 'عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر',
                'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر',
                'تسعة عشر', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون',
                'سبعون', 'ثمانون', 'تسعون', 'مئة', 'ألف', 'رقم', 'عدد',
                'الأول', 'الثاني', 'الثالث', 'الرابع', 'الخامس'
            ],
            'أيام الأسبوع والوقت': [
                'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد',
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس',
                'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر', 'ساعة', 'دقيقة', 'يوم', 'شهر', 'سنة'
            ],
            'العائلة والأقارب': [
                'أم', 'أب', 'ابن', 'ابنة', 'أخ', 'أخت', 'جد', 'جدة',
                'عم', 'خال', 'زوج', 'زوجة', 'عائلة', 'أسرة', 'قريب'
            ],
            'الألوان': [
                'أحمر', 'أخضر', 'أزرق', 'أصفر', 'أسود', 'أبيض', 'بني',
                'وردي', 'بنفسجي', 'رمادي', 'برتقالي', 'لون', 'ذهبي', 'فضي'
            ],
            'الطعام والشراب': [
                'خبز', 'ماء', 'حليب', 'لحم', 'سمك', 'فاكهة', 'خضار', 'أرز', 'شاي', 'قهوة',
                'طعام', 'شراب', 'يأكل', 'يشرب', 'منضدة قهوة', 'شراب غازي', 'أريد كوب ماء',
                'قائمة الطعام', 'ماذا تحب أن تأكل', 'ما نوع اللحم', 'المزيد من الماء',
                'الفاكهة', 'شريحة لحم', 'اللحم نيئ', 'السمك طازج', 'مخبز', 'فاكهة العاطفة'
            ],
            'الجسم وأجزاؤه': [
                'رأس', 'عين', 'أنف', 'فم', 'يد', 'قدم', 'أذن', 'شعر',
                'وجه', 'جسم', 'ذراع', 'ساق', 'إصبع', 'ظهر'
            ],
            'الملابس': [
                'قميص', 'بنطال', 'فستان', 'حذاء', 'قبعة', 'جوارب',
                'معطف', 'ملابس', 'ثوب', 'سروال'
            ],
            'البيت والمنزل': [
                'بيت', 'غرفة', 'مطبخ', 'حمام', 'سرير', 'كرسي', 'طاولة',
                'نافذة', 'باب', 'منزل', 'دار', 'شقة'
            ],
            'الأسئلة والاستفهام': [
                'هل هناك', 'هل يمكنني', 'هل يمكنك', 'ماذا', 'أين', 'متى', 'كيف', 'لماذا',
                'تيارات خطرة تحت الماء', 'أن أرى', 'أن تعطيني', 'أن تحضر لي'
            ],
            'النقل والسفر': [
                'سيارة', 'حافلة', 'قطار', 'طائرة', 'دراجة', 'سفينة', 'مطار', 'محطة'
            ],
            'الطبيعة والطقس': [
                'شمس', 'قمر', 'نجم', 'مطر', 'ثلج', 'ريح', 'شجرة', 'زهرة',
                'جبل', 'بحر', 'طبيعة', 'سماء', 'أرض', 'نهر'
            ],
            'الحيوانات': [
                'كلب', 'قطة', 'حصان', 'بقرة', 'خروف', 'طائر', 'سمكة',
                'حيوان', 'أسد', 'فيل', 'دجاج'
            ],
            'الوقت والزمن': [
                'ساعة', 'دقيقة', 'يوم', 'أسبوع', 'شهر', 'سنة', 'صباح',
                'ظهر', 'مساء', 'ليل', 'وقت', 'زمن', 'تاريخ'
            ],
            'المشاعر والأحاسيس': [
                'سعيد', 'حزين', 'غاضب', 'خائف', 'متعب', 'جائع', 'عطشان',
                'حب', 'فرح', 'حزن', 'خوف', 'غضب'
            ],
            'الأفعال': [
                'يأكل', 'يشرب', 'ينام', 'يمشي', 'يجري', 'يقرأ', 'يكتب',
                'يتكلم', 'يذهب', 'يأتي', 'يعمل', 'يلعب'
            ],
            'التعليم والدراسة': [
                'كتاب', 'قلم', 'مدرسة', 'معلم', 'طالب', 'درس', 'تعلم',
                'دراسة', 'امتحان', 'واجب'
            ],
            'النقل والمواصلات': [
                'سيارة', 'حافلة', 'قطار', 'طائرة', 'دراجة', 'سفينة',
                'مواصلات', 'نقل', 'سفر'
            ],
            'المهن والوظائف': [
                'طبيب', 'معلم', 'مهندس', 'عامل', 'طباخ', 'سائق',
                'مهنة', 'وظيفة', 'عمل'
            ]
        }

        # البحث في الفئات (بحث دقيق)
        for category, keywords in categories.items():
            for keyword in keywords:
                if keyword in text:
                    return category

        # تصنيف إضافي بناءً على كلمات مفتاحية أخرى
        if any(word in text for word in ['كبير', 'صغير', 'طويل', 'قصير', 'سميك', 'رقيق', 'جميل', 'قبيح']):
            return 'الصفات والأوصاف'

        if any(word in text for word in ['كمبيوتر', 'شاشة', 'هاتف', 'آلة حاسبة', 'القرص']):
            return 'التكنولوجيا'

        if any(word in text for word in ['سؤال', 'إجابة', 'حقيقة', 'كذبة', 'صحيح', 'غير صحيح']):
            return 'التعليم والمعرفة'

        if any(word in text for word in ['بارد', 'حار', 'مظلم', 'مضيء', 'جاف', 'رطب', 'نظيف', 'قذر']):
            return 'الصفات والأحوال'

        if any(word in text for word in ['كرة القدم', 'لعبة', 'رياضة']):
            return 'الرياضة والألعاب'

        if any(word in text for word in ['شلال', 'جبل', 'نهر', 'بحيرة', 'غابة', 'صحراء']):
            return 'الطبيعة والجغرافيا'

        # تصنيف حسب رقم الدرس كحل أخير
        if lesson_number <= 10:
            return "الأساسيات والتحيات"
        elif lesson_number <= 25:
            return "الحياة اليومية الأساسية"
        elif lesson_number <= 50:
            return "المفردات المتوسطة"
        elif lesson_number <= 75:
            return "المفردات المتقدمة"
        elif lesson_number <= 100:
            return "المحادثة والتعبير"
        else:
            return "المستوى المتقدم"

    def get_category_icon(self, category):
        """الحصول على أيقونة الفئة"""
        icons = {
            'التحيات والمجاملات': '👋',
            'الأساسيات والتحيات': '👋',
            'الأرقام والحساب': '🔢',
            'أيام الأسبوع والوقت': '📅',
            'العائلة والأقارب': '👨‍👩‍👧‍👦',
            'الألوان': '🎨',
            'الطعام والشراب': '🍽️',
            'الجسم وأجزاؤه': '👤',
            'الملابس': '👕',
            'البيت والمنزل': '🏠',
            'الأسئلة والاستفهام': '❓',
            'النقل والسفر': '🚗',
            'الطبيعة والطقس': '🌳',
            'الحيوانات': '🐾',
            'الوقت والزمن': '⏰',
            'المشاعر والأحاسيس': '😊',
            'الأفعال': '🏃',
            'التعليم والدراسة': '📚',
            'النقل والمواصلات': '🚗',
            'المهن والوظائف': '👷',
            'الصفات والأوصاف': '📏',
            'التكنولوجيا': '💻',
            'التعليم والمعرفة': '🧠',
            'الصفات والأحوال': '🌡️',
            'الرياضة والألعاب': '⚽',
            'الطبيعة والجغرافيا': '🏔️',
            'الحياة اليومية': '🏠',
            'الطبيعة والبيئة': '🌍',
            'المجتمع والثقافة': '🏛️',
            'المتقدم والمحادثة': '💬',
            'الحياة اليومية الأساسية': '🏠',
            'المفردات المتوسطة': '📖',
            'المفردات المتقدمة': '📚',
            'المحادثة والتعبير': '💬',
            'المستوى المتقدم': '🎓',
            'متنوعة': '📝'
        }
        return icons.get(category, '📝')

    def scan_audio_files(self):
        """فحص الملفات الصوتية المتاحة"""
        print("🔊 فحص الملفات الصوتية...")

        if not self.audio_dir.exists():
            print(f"❌ مجلد الصوتيات غير موجود: {self.audio_dir}")
            return {}

        audio_files = {}
        audio_extensions = ['.mp3', '.wav', '.ogg', '.m4a']

        for audio_file in self.audio_dir.rglob('*'):
            if audio_file.is_file() and audio_file.suffix.lower() in audio_extensions:
                # استخدام اسم الملف بدون امتداد كمفتاح
                key = audio_file.stem.lower()
                audio_files[key] = {
                    'filename': audio_file.name,
                    'path': str(audio_file),
                    'relative_path': f"armenian_audio/{audio_file.name}",
                    'size': audio_file.stat().st_size
                }

        print(f"✅ تم العثور على {len(audio_files)} ملف صوتي")
        return audio_files

    def find_audio_for_word(self, armenian_word, phonetic, audio_files):
        """البحث عن ملف صوتي للكلمة"""
        if not audio_files:
            return None

        # قائمة المفاتيح للبحث
        search_keys = []

        if phonetic:
            # تنظيف النطق
            clean_phonetic = re.sub(r'[^\w]', '', phonetic.lower())
            search_keys.append(clean_phonetic)

            # إضافة تنويعات
            search_keys.append(phonetic.lower().replace(' ', ''))
            search_keys.append(phonetic.lower().replace(' ', '_'))
            search_keys.append(phonetic.lower().replace(' ', '-'))

        if armenian_word:
            # تنظيف النص الأرمني
            clean_armenian = re.sub(r'[^\w]', '', armenian_word.lower())
            search_keys.append(clean_armenian)

        # البحث في الملفات الصوتية
        for key in search_keys:
            if key in audio_files:
                return audio_files[key]

            # البحث الجزئي
            for audio_key, audio_info in audio_files.items():
                if key in audio_key or audio_key in key:
                    return audio_info

        return None

    def process_excel_file(self):
        """معالجة ملف Excel"""
        print(f"📊 معالجة ملف Excel: {self.excel_file}")

        if not Path(self.excel_file).exists():
            print(f"❌ ملف Excel غير موجود: {self.excel_file}")
            return False

        try:
            # قراءة ملف Excel
            df = pd.read_excel(self.excel_file)
            print(f"✅ تم قراءة {len(df)} صف من ملف Excel")

            # طباعة أسماء الأعمدة للتحقق
            print(f"📋 أعمدة الملف: {list(df.columns)}")

            # فحص الملفات الصوتية
            audio_files = self.scan_audio_files()

            # معالجة البيانات
            categories = {}

            for index, row in df.iterrows():
                try:
                    # استخراج البيانات من الصف
                    lesson_number = row.get('lesson_number', row.get('Lesson', row.get('lesson', index + 1)))
                    armenian_word = str(row.get('armenian', row.get('Armenian', row.get('أرمني', '')))).strip()
                    phonetic = str(row.get('transliteration', '')).strip()
                    arabic_meaning = str(row.get('arabic', row.get('Arabic', row.get('عربي', '')))).strip()



                    # تخطي الصفوف الفارغة
                    if not armenian_word or armenian_word == 'nan':
                        continue

                    # تنظيف البيانات
                    if phonetic == 'nan' or pd.isna(row.get('transliteration')):
                        phonetic = ''
                    if arabic_meaning == 'nan':
                        arabic_meaning = ''

                    # توليد النطق العربي
                    arabic_phonetic = self.generate_arabic_phonetic(phonetic)

                    # تحديد الفئة
                    category = self.determine_category(lesson_number, armenian_word, arabic_meaning)

                    # البحث عن ملف صوتي
                    audio_info = self.find_audio_for_word(armenian_word, phonetic, audio_files)

                    # إنشاء بيانات الكلمة
                    word_data = {
                        'armenian': armenian_word,
                        'phonetic': phonetic,
                        'arabic': arabic_meaning,
                        'arabic_phonetic': arabic_phonetic,
                        'lesson_number': int(lesson_number) if str(lesson_number).isdigit() else 1,
                        'audio_file': audio_info['filename'] if audio_info else None,
                        'audio_path': audio_info['relative_path'] if audio_info else None,
                        'has_real_audio': audio_info is not None,
                        'meaning': f"كلمة من الدرس {lesson_number}",
                        'category': category
                    }

                    # إضافة إلى الفئة
                    if category not in categories:
                        categories[category] = {
                            'icon': self.get_category_icon(category),
                            'description': f'كلمات متعلقة بـ {category}',
                            'words': []
                        }

                    categories[category]['words'].append(word_data)
                    self.total_words += 1

                except Exception as e:
                    print(f"⚠️ خطأ في الصف {index}: {e}")
                    continue

            self.vocabulary_data = categories
            self.total_lessons = len(set(word['lesson_number'] for category in categories.values() for word in category['words']))

            print(f"✅ تم معالجة {self.total_words} كلمة في {len(categories)} فئة من {self.total_lessons} درس")
            return True

        except Exception as e:
            print(f"❌ خطأ في معالجة ملف Excel: {e}")
            return False

    def save_processed_data(self):
        """حفظ البيانات المعالجة"""
        try:
            # حفظ JSON للتطبيق
            with open('armenian_vocabulary_from_excel.json', 'w', encoding='utf-8') as f:
                json.dump(self.vocabulary_data, f, ensure_ascii=False, indent=2)

            # حفظ JavaScript للتطبيق
            js_content = f"""// مفردات أرمينية من ملف Excel
// Armenian Vocabulary from Excel File

window.armenianVocabulary = {json.dumps(self.vocabulary_data, ensure_ascii=False, indent=2)};

// إحصائيات
window.vocabularyStats = {{
"""

            for category_name, category_data in self.vocabulary_data.items():
                word_count = len(category_data['words'])
                js_content += f'  "{category_name}": {word_count},\n'

            js_content += f"""  "total": {self.total_words},
  "lessons": {self.total_lessons},
  "categories": {len(self.vocabulary_data)}
}};

console.log('📊 تم تحميل', {self.total_words}, 'كلمة أرمينية من', {self.total_lessons}, 'درس في', {len(self.vocabulary_data)}, 'فئة');
console.log('🔊 مع ملفات صوتية حقيقية من مجلد armenian_audio');
"""

            with open('armenian_vocabulary_from_excel.js', 'w', encoding='utf-8') as f:
                f.write(js_content)

            # حفظ معلومات الصوتيات
            audio_manifest = {
                'source': 'excel_file_and_audio_folder',
                'excel_file': self.excel_file,
                'audio_directory': str(self.audio_dir),
                'total_words': self.total_words,
                'total_lessons': self.total_lessons,
                'total_categories': len(self.vocabulary_data),
                'processing_date': datetime.now().isoformat(),
                'instructions': {
                    'ar': 'ضع مجلد armenian_audio مع التطبيق للحصول على الصوت الحقيقي',
                    'en': 'Place armenian_audio folder with the app for real audio'
                }
            }

            with open('excel_audio_manifest.json', 'w', encoding='utf-8') as f:
                json.dump(audio_manifest, f, ensure_ascii=False, indent=2)

            print(f"✅ تم حفظ البيانات:")
            print(f"   📄 armenian_vocabulary_from_excel.json")
            print(f"   📄 armenian_vocabulary_from_excel.js")
            print(f"   📋 excel_audio_manifest.json")

        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")

    def run_processing(self):
        """تشغيل المعالجة"""
        print("🇦🇲 بدء معالجة ملف Excel والصوتيات الأرمينية")
        print("=" * 60)

        start_time = time.time()

        # معالجة ملف Excel
        if self.process_excel_file():
            # حفظ البيانات المعالجة
            self.save_processed_data()

            # إحصائيات نهائية
            end_time = time.time()
            duration = end_time - start_time

            print(f"\n🎉 تم الانتهاء من المعالجة!")
            print(f"📊 النتائج النهائية:")
            print(f"   📝 إجمالي الكلمات: {self.total_words}")
            print(f"   📚 عدد الدروس: {self.total_lessons}")
            print(f"   📁 عدد الفئات: {len(self.vocabulary_data)}")
            print(f"   🔊 مع ملفات صوتية حقيقية")
            print(f"   ⏱️ الوقت المستغرق: {duration:.1f} ثانية")

            print(f"\n📋 الفئات المنشأة:")
            for category_name, category_data in self.vocabulary_data.items():
                word_count = len(category_data['words'])
                icon = category_data['icon']
                print(f"   {icon} {category_name}: {word_count} كلمة")

            print(f"\n📁 الملفات المحفوظة:")
            print(f"   📄 armenian_vocabulary_from_excel.json")
            print(f"   📄 armenian_vocabulary_from_excel.js")
            print(f"   📋 excel_audio_manifest.json")

        else:
            print("❌ فشل في معالجة ملف Excel")

def main():
    processor = ExcelAudioProcessor()
    processor.run_processing()

if __name__ == "__main__":
    main()
