@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🇦🇲 استخراج المفردات الأرمينية
echo    Armenian Vocabulary Extraction
echo ========================================
echo.

REM التحقق من وجود Python
echo 🔍 التحقق من Python...
py --version >nul 2>&1
if errorlevel 1 (
    python --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Python غير مثبت على النظام
        echo.
        echo 💡 حلول مقترحة:
        echo    1. انقر نقراً مزدوجاً على: install_python_auto.bat
        echo    2. أو قم بتثبيت Python من: https://python.org
        echo    3. تأكد من تحديد "Add Python to PATH" أثناء التثبيت
        echo.
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=python
        echo ✅ تم العثور على Python (python)
    )
) else (
    set PYTHON_CMD=py
    echo ✅ تم العثور على Python (py)
)

echo.

REM تثبيت المكتبات الأساسية
echo 📦 تثبيت المكتبات المطلوبة...
echo    جاري تثبيت: requests, beautifulsoup4, selenium...

%PYTHON_CMD% -m pip install --quiet requests beautifulsoup4 selenium webdriver-manager
if errorlevel 1 (
    echo ⚠️ فشل التثبيت العادي، محاولة بصلاحيات المستخدم...
    %PYTHON_CMD% -m pip install --user --quiet requests beautifulsoup4 selenium webdriver-manager
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        echo 💡 جرب تشغيل Command Prompt كمدير وأعد المحاولة
        pause
        exit /b 1
    )
)

echo ✅ تم تثبيت المكتبات بنجاح
echo.

REM التحقق من وجود ملفات الاستخراج
echo 🔍 التحقق من ملفات الاستخراج...

if exist advanced_scraper.py (
    echo ✅ تم العثور على المستخرج المتقدم
    set SCRAPER_FILE=advanced_scraper.py
) else if exist simple_scraper.py (
    echo ✅ تم العثور على المستخرج المبسط
    set SCRAPER_FILE=simple_scraper.py
) else (
    echo ❌ لم يتم العثور على ملفات الاستخراج
    echo 💡 تأكد من وجود أحد الملفات التالية:
    echo    - advanced_scraper.py
    echo    - simple_scraper.py
    pause
    exit /b 1
)

echo.

REM بدء الاستخراج
echo 🚀 بدء استخراج المفردات الأرمينية...
echo 🔤 سيتم إضافة النطق العربي لكل كلمة
echo ⏳ هذا قد يستغرق 30-60 دقيقة حسب عدد الدروس...
echo.
echo أمثلة على النطق العربي:
echo   Mek → ميك (واحد)
echo   Barev → باريف (مرحبا)
echo   Bari luys → باري لويس (صباح الخير)
echo.

set /p confirm="هل تريد المتابعة؟ (y/n): "
if /i "%confirm%" neq "y" goto :end

echo.
echo 🔄 بدء الاستخراج...
%PYTHON_CMD% %SCRAPER_FILE%

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء الاستخراج
    echo.
    echo 💡 أسباب محتملة وحلول:
    echo    1. مشكلة في الاتصال بالإنترنت
    echo       → تحقق من اتصال الإنترنت وأعد المحاولة
    echo.
    echo    2. الموقع غير متاح مؤقتاً
    echo       → انتظر قليلاً وأعد المحاولة
    echo.
    echo    3. مشكلة في المكتبات
    echo       → شغل: pip install --upgrade selenium requests beautifulsoup4
    echo.
    echo    4. مشكلة في ChromeDriver
    echo       → سيتم تحميله تلقائياً في المحاولة التالية
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم الانتهاء من الاستخراج بنجاح!
echo.

REM التحقق من الملفات المُنتجة
echo 📁 التحقق من الملفات المُنتجة...

if exist vocabulary_data_complete.json (
    echo ✅ vocabulary_data_complete.json
) else (
    echo ❌ vocabulary_data_complete.json غير موجود
)

if exist vocabulary_data_complete.js (
    echo ✅ vocabulary_data_complete.js
) else (
    echo ❌ vocabulary_data_complete.js غير موجود
)

if exist armenian_vocabulary_complete.txt (
    echo ✅ armenian_vocabulary_complete.txt
) else (
    echo ❌ armenian_vocabulary_complete.txt غير موجود
)

echo.

REM عرض الإحصائيات
if exist vocabulary_data_complete.json (
    echo 📊 عرض الإحصائيات...
    %PYTHON_CMD% -c "import json; data=json.load(open('vocabulary_data_complete.json', 'r', encoding='utf-8')); print(f'📚 عدد الدروس: {len(data)}'); print(f'📝 إجمالي الكلمات: {sum(len(words) for words in data.values())}')"
)

echo.

REM فتح التطبيق
echo 🌐 فتح التطبيق...
if exist index.html (
    start index.html
    echo ✅ تم فتح التطبيق الرئيسي
) else (
    echo ⚠️ ملف index.html غير موجود
)

if exist demo_extracted_data.html (
    timeout /t 2 /nobreak >nul
    start demo_extracted_data.html
    echo ✅ تم فتح عرض البيانات المستخرجة
)

echo.
echo 🎉 تم الانتهاء من جميع العمليات!
echo.
echo 💡 للحصول على الصوتيات:
echo    انقر نقراً مزدوجاً على: extract_audio_only.bat
echo.

:end
pause
