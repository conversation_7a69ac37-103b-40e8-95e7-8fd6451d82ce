#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص سريع للمشاكل
Quick Problem Diagnosis
"""

import sys
import os
import subprocess
import json
from pathlib import Path

def print_section(title):
    """طباعة عنوان قسم"""
    print(f"\n{'='*50}")
    print(f"🔍 {title}")
    print('='*50)

def check_python():
    """فحص Python"""
    print_section("فحص Python")
    print(f"✅ Python Version: {sys.version}")
    print(f"✅ Python Path: {sys.executable}")
    print(f"✅ Current Directory: {os.getcwd()}")

def check_files():
    """فحص الملفات المطلوبة"""
    print_section("فحص الملفات")
    
    required_files = [
        'quick_start.py',
        'extract_data.py', 
        'armenian_app_manager.py',
        'advanced_scraper.py',
        'simple_scraper.py',
        'index.html',
        'demo_extracted_data.html'
    ]
    
    existing_files = []
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file} ({size:,} bytes)")
            existing_files.append(file)
        else:
            print(f"❌ {file} - مفقود")
            missing_files.append(file)
    
    return existing_files, missing_files

def check_packages():
    """فحص المكتبات"""
    print_section("فحص المكتبات")
    
    packages = ['requests', 'beautifulsoup4', 'selenium', 'webdriver_manager']
    
    for package in packages:
        try:
            if package == 'beautifulsoup4':
                import bs4
                print(f"✅ {package} (bs4)")
            elif package == 'webdriver_manager':
                import webdriver_manager
                print(f"✅ {package}")
            else:
                __import__(package)
                print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - غير مثبت")

def check_data():
    """فحص البيانات المستخرجة"""
    print_section("فحص البيانات المستخرجة")
    
    json_file = 'vocabulary_data_complete.json'
    if os.path.exists(json_file):
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            lessons = len(data)
            words = sum(len(w) for w in data.values())
            
            print(f"✅ البيانات موجودة:")
            print(f"   📚 {lessons} درس")
            print(f"   📝 {words} كلمة")
            
            # عرض عينة من البيانات
            if data:
                first_lesson = list(data.keys())[0]
                first_words = data[first_lesson][:3]
                print(f"\n📋 عينة من البيانات:")
                for word in first_words:
                    print(f"   🔤 {word.get('armenian', 'N/A')} → {word.get('arabic_phonetic', 'N/A')}")
            
            return True
        except Exception as e:
            print(f"❌ خطأ في قراءة البيانات: {e}")
            return False
    else:
        print("❌ لا توجد بيانات مستخرجة")
        return False

def test_internet():
    """اختبار الإنترنت"""
    print_section("فحص الإنترنت")
    
    try:
        import requests
        response = requests.get('https://www.google.com', timeout=10)
        if response.status_code == 200:
            print("✅ الإنترنت يعمل")
            return True
        else:
            print(f"⚠️ مشكلة في الإنترنت: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ لا يوجد اتصال إنترنت: {e}")
        return False

def test_simple_extraction():
    """اختبار استخراج بسيط"""
    print_section("اختبار الاستخراج البسيط")
    
    try:
        # اختبار تحويل النطق العربي
        from test_arabic_phonetic import generate_arabic_phonetic
        
        test_words = ['Mek', 'Barev', 'Chors']
        print("🔤 اختبار النطق العربي:")
        
        for word in test_words:
            arabic = generate_arabic_phonetic(word)
            print(f"   {word} → {arabic}")
        
        print("✅ تحويل النطق العربي يعمل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النطق: {e}")
        return False

def suggest_solutions(existing_files, missing_files, has_data, internet_ok):
    """اقتراح الحلول"""
    print_section("اقتراحات الحلول")
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n💡 الحل: تأكد من وجود جميع الملفات في نفس المجلد")
    
    if not has_data:
        print("❌ لا توجد بيانات مستخرجة")
        if internet_ok and 'extract_data.py' in existing_files:
            print("💡 الحل: شغل python extract_data.py")
        elif not internet_ok:
            print("💡 الحل: تحقق من اتصال الإنترنت أولاً")
        else:
            print("💡 الحل: تأكد من وجود ملفات الاستخراج")
    
    if has_data and 'index.html' in existing_files:
        print("✅ كل شيء جاهز!")
        print("💡 يمكنك فتح index.html أو demo_extracted_data.html")

def main():
    """الدالة الرئيسية"""
    print("🔍 تشخيص سريع لمشاكل تطبيق تعلم الأرمينية")
    print("="*60)
    
    # فحص شامل
    check_python()
    existing_files, missing_files = check_files()
    check_packages()
    has_data = check_data()
    internet_ok = test_internet()
    test_simple_extraction()
    
    # اقتراح الحلول
    suggest_solutions(existing_files, missing_files, has_data, internet_ok)
    
    print(f"\n{'='*60}")
    print("✅ انتهى التشخيص")
    print("\n💡 إذا استمرت المشاكل، أرسل نتائج هذا التشخيص")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n❌ خطأ في التشخيص: {e}")
    
    input("\nاضغط Enter للخروج...")
