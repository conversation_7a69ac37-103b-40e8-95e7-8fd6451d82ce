@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🐍 تثبيت Python تلقائياً
echo    Automatic Python Installation
echo ========================================
echo.

echo 🔍 فحص Python الحالي...
python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ Python مثبت مسبقاً!
    python --version
    goto :already_installed
)

py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ Python مثبت مسبقاً!
    py --version
    goto :already_installed
)

echo ❌ Python غير مثبت
echo.

echo 💡 خيارات التثبيت:
echo    1. فتح Microsoft Store لتثبيت Python
echo    2. فتح الموقع الرسمي لتحميل Python
echo    3. إلغاء
echo.

set /p choice="اختر رقم (1-3): "

if "%choice%"=="1" (
    echo 🏪 فتح Microsoft Store...
    start ms-windows-store://pdp/?ProductId=9NRWMJP3717K
    echo.
    echo 📋 خطوات التثبيت من Microsoft Store:
    echo    1. انقر "Get" أو "Install"
    echo    2. انتظر انتهاء التثبيت
    echo    3. أعد تشغيل Command Prompt
    echo    4. شغل هذا الملف مرة أخرى للتحقق
    goto :end
)

if "%choice%"=="2" (
    echo 🌐 فتح الموقع الرسمي...
    start https://python.org/downloads
    echo.
    echo 📋 خطوات التثبيت من الموقع:
    echo    1. انقر "Download Python 3.12.x"
    echo    2. شغل الملف المحمل
    echo    3. ✅ تأكد من تحديد "Add Python to PATH"
    echo    4. انقر "Install Now"
    echo    5. أعد تشغيل الكمبيوتر
    echo    6. شغل هذا الملف مرة أخرى للتحقق
    goto :end
)

if "%choice%"=="3" (
    echo 🛑 تم إلغاء التثبيت
    goto :end
)

echo ❌ خيار غير صحيح
goto :end

:already_installed
echo.
echo 🎉 Python جاهز للاستخدام!
echo.
echo 🚀 الخطوات التالية:
echo    1. افتح Command Prompt جديد
echo    2. انتقل لمجلد المشروع
echo    3. شغل: python quick_start.py
echo.
echo 💡 أو انقر نقراً مزدوجاً على: run_python_app.bat
echo.

:end
pause
