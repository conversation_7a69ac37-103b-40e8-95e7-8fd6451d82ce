#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنظيم المفردات الأرمينية في فئات
Organize Armenian Vocabulary into Categories
"""

import json
import re
from collections import defaultdict

def categorize_vocabulary():
    """تصنيف المفردات حسب الفئات"""
    
    # قراءة البيانات الحالية
    with open('vocabulary_data_complete.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # فئات المفردات
    categories = {
        'التحيات': {
            'icon': '👋',
            'description': 'كلمات الترحيب والتحية',
            'keywords': ['مرحبا', 'صباح', 'مساء', 'ليلة', 'وداع', 'أهلا'],
            'words': []
        },
        'الأرقام': {
            'icon': '🔢',
            'description': 'الأرقام من 1 إلى 100',
            'keywords': ['واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة', 'عشرة'],
            'words': []
        },
        'العائلة': {
            'icon': '👨‍👩‍👧‍👦',
            'description': 'أفراد العائلة والأقارب',
            'keywords': ['أم', 'أب', 'ابن', 'ابنة', 'أخ', 'أخت', 'جد', 'جدة', 'عم', 'خال'],
            'words': []
        },
        'الألوان': {
            'icon': '🎨',
            'description': 'الألوان الأساسية والثانوية',
            'keywords': ['أحمر', 'أخضر', 'أزرق', 'أصفر', 'أسود', 'أبيض', 'بني', 'وردي', 'بنفسجي'],
            'words': []
        },
        'الطعام والشراب': {
            'icon': '🍽️',
            'description': 'الأطعمة والمشروبات',
            'keywords': ['خبز', 'ماء', 'حليب', 'لحم', 'سمك', 'فاكهة', 'خضار', 'أرز', 'شاي', 'قهوة'],
            'words': []
        },
        'الجسم': {
            'icon': '👤',
            'description': 'أجزاء الجسم',
            'keywords': ['رأس', 'عين', 'أنف', 'فم', 'يد', 'قدم', 'أذن', 'شعر', 'وجه'],
            'words': []
        },
        'الملابس': {
            'icon': '👕',
            'description': 'الملابس والإكسسوارات',
            'keywords': ['قميص', 'بنطال', 'فستان', 'حذاء', 'قبعة', 'جوارب', 'معطف'],
            'words': []
        },
        'البيت': {
            'icon': '🏠',
            'description': 'أجزاء البيت والأثاث',
            'keywords': ['بيت', 'غرفة', 'مطبخ', 'حمام', 'سرير', 'كرسي', 'طاولة', 'نافذة', 'باب'],
            'words': []
        },
        'الطبيعة': {
            'icon': '🌳',
            'description': 'الطبيعة والطقس',
            'keywords': ['شمس', 'قمر', 'نجم', 'مطر', 'ثلج', 'ريح', 'شجرة', 'زهرة', 'جبل', 'بحر'],
            'words': []
        },
        'الحيوانات': {
            'icon': '🐾',
            'description': 'الحيوانات الأليفة والبرية',
            'keywords': ['كلب', 'قطة', 'حصان', 'بقرة', 'خروف', 'طائر', 'سمكة', 'أسد', 'فيل'],
            'words': []
        },
        'الوقت': {
            'icon': '⏰',
            'description': 'الوقت والتاريخ',
            'keywords': ['ساعة', 'دقيقة', 'يوم', 'أسبوع', 'شهر', 'سنة', 'صباح', 'ظهر', 'مساء', 'ليل'],
            'words': []
        },
        'المشاعر': {
            'icon': '😊',
            'description': 'المشاعر والأحاسيس',
            'keywords': ['سعيد', 'حزين', 'غاضب', 'خائف', 'متعب', 'جائع', 'عطشان'],
            'words': []
        },
        'الأفعال': {
            'icon': '🏃',
            'description': 'الأفعال الأساسية',
            'keywords': ['يأكل', 'يشرب', 'ينام', 'يمشي', 'يجري', 'يقرأ', 'يكتب', 'يتكلم'],
            'words': []
        },
        'أخرى': {
            'icon': '📝',
            'description': 'كلمات متنوعة أخرى',
            'keywords': [],
            'words': []
        }
    }
    
    # تصنيف الكلمات
    all_words = []
    for lesson_data in data.values():
        all_words.extend(lesson_data)
    
    # إزالة المكررات
    unique_words = {}
    for word in all_words:
        key = word['armenian'] + word['phonetic']
        if key not in unique_words:
            unique_words[key] = word
    
    # تصنيف الكلمات
    for word in unique_words.values():
        categorized = False
        arabic_meaning = word['arabic'].lower()
        
        for category_name, category_info in categories.items():
            if category_name == 'أخرى':
                continue
                
            for keyword in category_info['keywords']:
                if keyword in arabic_meaning:
                    category_info['words'].append(word)
                    categorized = True
                    break
            
            if categorized:
                break
        
        # إذا لم يتم تصنيف الكلمة، ضعها في "أخرى"
        if not categorized:
            categories['أخرى']['words'].append(word)
    
    # إضافة كلمات إضافية لكل فئة لجعلها أكثر ثراءً
    additional_words = {
        'التحيات': [
            {'armenian': 'Բարի գիշեր', 'phonetic': 'Bari gisher', 'arabic': 'تصبح على خير', 'arabic_phonetic': 'باري غيشير'},
            {'armenian': 'Բարի երեկո', 'phonetic': 'Bari yereko', 'arabic': 'مساء الخير', 'arabic_phonetic': 'باري ييريكو'},
            {'armenian': 'Ցտեսություն', 'phonetic': 'Tsdesutyun', 'arabic': 'وداعاً', 'arabic_phonetic': 'تسديسوتيون'},
        ],
        'الأرقام': [
            {'armenian': 'Չորս', 'phonetic': 'Chors', 'arabic': 'أربعة', 'arabic_phonetic': 'تشورس'},
            {'armenian': 'Հինգ', 'phonetic': 'Hing', 'arabic': 'خمسة', 'arabic_phonetic': 'هينغ'},
            {'armenian': 'Վեց', 'phonetic': 'Vets', 'arabic': 'ستة', 'arabic_phonetic': 'فيتس'},
            {'armenian': 'Յոթ', 'phonetic': 'Yot', 'arabic': 'سبعة', 'arabic_phonetic': 'يوت'},
            {'armenian': 'Ութ', 'phonetic': 'Ut', 'arabic': 'ثمانية', 'arabic_phonetic': 'اوت'},
            {'armenian': 'Ինը', 'phonetic': 'Iny', 'arabic': 'تسعة', 'arabic_phonetic': 'يني'},
            {'armenian': 'Տաս', 'phonetic': 'Das', 'arabic': 'عشرة', 'arabic_phonetic': 'داس'},
        ],
        'العائلة': [
            {'armenian': 'Եղբայր', 'phonetic': 'Yeghbayr', 'arabic': 'أخ', 'arabic_phonetic': 'ييغبايير'},
            {'armenian': 'Քույր', 'phonetic': 'Khoyr', 'arabic': 'أخت', 'arabic_phonetic': 'خويير'},
            {'armenian': 'Որդի', 'phonetic': 'Vordi', 'arabic': 'ابن', 'arabic_phonetic': 'فوردي'},
            {'armenian': 'Աղջիկ', 'phonetic': 'Aghjik', 'arabic': 'ابنة', 'arabic_phonetic': 'اغجيك'},
        ],
        'الألوان': [
            {'armenian': 'Կապույտ', 'phonetic': 'Kapuyt', 'arabic': 'أزرق', 'arabic_phonetic': 'كابويت'},
            {'armenian': 'Դեղին', 'phonetic': 'Deghin', 'arabic': 'أصفر', 'arabic_phonetic': 'ديغيين'},
            {'armenian': 'Սև', 'phonetic': 'Sev', 'arabic': 'أسود', 'arabic_phonetic': 'سيف'},
            {'armenian': 'Սպիտակ', 'phonetic': 'Spitak', 'arabic': 'أبيض', 'arabic_phonetic': 'سبيتاك'},
        ]
    }
    
    # إضافة الكلمات الإضافية
    for category_name, words in additional_words.items():
        if category_name in categories:
            for word in words:
                # إضافة معلومات إضافية
                word.update({
                    'meaning': f'كلمة من فئة {category_name}',
                    'lesson': f'فئة {category_name}',
                    'audio': re.sub(r'[^a-zA-Z0-9]', '-', word['phonetic'].lower())
                })
                categories[category_name]['words'].append(word)
    
    # إزالة الفئات الفارغة
    categories = {k: v for k, v in categories.items() if v['words']}
    
    # حفظ البيانات المصنفة
    with open('vocabulary_categories.json', 'w', encoding='utf-8') as f:
        json.dump(categories, f, ensure_ascii=False, indent=2)
    
    # إنشاء ملف JavaScript
    js_content = f"""// المفردات الأرمينية مصنفة حسب الفئات
// Armenian Vocabulary Organized by Categories

window.vocabularyCategories = {json.dumps(categories, ensure_ascii=False, indent=2)};

// إحصائيات سريعة
window.categoryStats = {{
"""
    
    total_words = 0
    for category_name, category_info in categories.items():
        word_count = len(category_info['words'])
        total_words += word_count
        js_content += f'  "{category_name}": {word_count},\n'
    
    js_content += f"""  "total": {total_words}
}};

console.log('📊 تم تحميل', {total_words}, 'كلمة في', {len(categories)}, 'فئة');
"""
    
    with open('vocabulary_categories.js', 'w', encoding='utf-8') as f:
        f.write(js_content)
    
    # طباعة الإحصائيات
    print("📊 تم تصنيف المفردات بنجاح!")
    print("=" * 50)
    
    for category_name, category_info in categories.items():
        word_count = len(category_info['words'])
        print(f"{category_info['icon']} {category_name}: {word_count} كلمة")
    
    print(f"\n✅ إجمالي الكلمات: {total_words}")
    print(f"📁 عدد الفئات: {len(categories)}")
    print(f"💾 تم حفظ البيانات في:")
    print(f"   - vocabulary_categories.json")
    print(f"   - vocabulary_categories.js")

if __name__ == "__main__":
    categorize_vocabulary()
