@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    تثبيت Python التلقائي
echo    Automatic Python Installation
echo ========================================
echo.
echo 🐍 سيتم تثبيت Python تلقائياً من Microsoft Store
echo ⏳ هذا قد يستغرق بضع دقائق...
echo.

REM محاولة فتح Python من Microsoft Store
echo 📥 فتح Microsoft Store لتثبيت Python...
start ms-windows-store://pdp/?productid=9NRWMJP3717K

echo.
echo 💡 تعليمات التثبيت:
echo    1. انقر على "Get" أو "تثبيت" في المتجر
echo    2. انتظر حتى انتهاء التثبيت
echo    3. أغلق المتجر وارجع لهذه النافذة
echo    4. اضغط أي مفتاح للمتابعة
echo.
pause

REM التحقق من تثبيت Python
echo 🔍 التحقق من تثبيت Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت بعد
    echo 💡 جرب التثبيت اليدوي:
    echo    1. اذهب إلى https://www.python.org/downloads/
    echo    2. حمل أحدث إصدار
    echo    3. تأكد من تحديد "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

echo ✅ تم تثبيت Python بنجاح!
python --version

echo.
echo 📦 تثبيت المكتبات المطلوبة...
pip install selenium beautifulsoup4 requests webdriver-manager gtts pygame

if errorlevel 1 (
    echo ❌ فشل في تثبيت بعض المكتبات
    echo 💡 جرب تشغيل الأمر التالي يدوياً:
    echo    pip install --user selenium beautifulsoup4 requests webdriver-manager gtts pygame
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم تثبيت جميع المكتبات بنجاح!
echo.
echo 🚀 يمكنك الآن تشغيل الاستخراج الشامل:
echo    انقر نقراً مزدوجاً على run_complete_extraction.bat
echo.
pause
