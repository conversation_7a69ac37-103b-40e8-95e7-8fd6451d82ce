# 🚀 ابدأ من هنا - تطبيق تعلم الأرمينية

## 👋 **مرحباً بك!**

هذا تطبيق متقدم لتعلم اللغة الأرمينية يحتوي على **125 درس كامل** مع **النطق العربي** و**الصوتيات**.

---

## ⚡ **البدء السريع**

### 🔥 **إذا كنت مستعجلاً:**
انقر نقراً مزدوجاً على: **`run_extraction_with_check.bat`**

سيتحقق من Python تلقائياً ويبدأ الاستخراج!

---

## 📋 **الخطوات التفصيلية**

### 1️⃣ **تثبيت Python (إذا لم يكن مثبتاً)**
- انقر نقراً مزدوجاً على: **`install_python_auto.bat`**
- أو اقرأ: **`INSTALL_PYTHON.md`**

### 2️⃣ **استخراج جميع الـ 125 درس**
- انقر نقراً مزدوجاً على: **`run_extraction_with_check.bat`**
- انتظر 45-60 دقيقة للانتهاء

### 3️⃣ **تشغيل التطبيق**
- انقر نقراً مزدوجاً على: **`run_app.bat`**
- أو افتح: **`index.html`**

---

## ✨ **ما ستحصل عليه**

### 🔤 **النطق العربي الفريد**
```
Meg → ميك
Barev → باريف
Bari luys → باري لويس
```

### 📊 **بطاقات تفاعلية**
```
┌─────────────────────────────────┐
│  النص الأرمني: Մեկ              │
│  النطق اللاتيني: (Meg)         │
│  النطق العربي: ميك              │ ← جديد!
│  المعنى العربي: 1               │
│  الشرح: الرقم واحد...           │
└─────────────────────────────────┘
```

### 🎵 **صوتيات متقدمة**
- ملفات صوتية حقيقية من الموقع
- نطق احتياطي بتقنية TTS
- تشغيل ذكي مع بدائل متعددة

---

## 📊 **الإحصائيات المتوقعة**

- **📚 عدد الدروس**: 125 درس كامل
- **📝 المفردات**: 2000-3000 كلمة
- **🔤 النطق العربي**: متوفر لكل كلمة
- **🎵 الصوتيات**: ملفات مستخرجة + TTS
- **⏱️ وقت الاستخراج**: 45-60 دقيقة

---

## 🆘 **حل المشاكل**

### ❌ **"Python was not found"**
- شغل: **`install_python_auto.bat`**
- أو اقرأ: **`INSTALL_PYTHON.md`**

### ❌ **"Scripts is disabled"**
- شغل PowerShell كمدير
- أدخل: `Set-ExecutionPolicy RemoteSigned -Scope CurrentUser`

### ❌ **بطء في الاستخراج**
- تأكد من اتصال الإنترنت
- أغلق برامج مكافحة الفيروسات مؤقتاً
- انتظر... العملية تحتاج وقت!

---

## 🎯 **الملفات المهمة**

### 🚀 **للتشغيل**
- **`run_extraction_with_check.bat`** - الاستخراج مع فحص Python
- **`run_app.bat`** - تشغيل التطبيق
- **`extract_audio_only.bat`** - استخراج الصوتيات فقط

### 📖 **للمساعدة**
- **`INSTALL_PYTHON.md`** - دليل تثبيت Python
- **`README.md`** - الوثائق الكاملة
- **`PROJECT_SUMMARY_125_LESSONS.md`** - ملخص المشروع

### 🧪 **للاختبار**
- **`test_arabic_phonetic_demo.bat`** - اختبار النطق العربي
- **`test_new_features.html`** - عرض الميزات الجديدة

---

## 🎉 **نصائح للنجاح**

1. **🔌 تأكد من اتصال الإنترنت** - مطلوب للاستخراج
2. **⏰ كن صبوراً** - الاستخراج يحتاج 45-60 دقيقة
3. **🔄 لا تغلق النافذة** - دع العملية تكمل
4. **💾 احفظ نسخة احتياطية** - من الملفات المُنتجة

---

## 🚀 **ابدأ الآن!**

انقر نقراً مزدوجاً على: **`run_extraction_with_check.bat`**

**🎯 ستحصل على أفضل تطبيق لتعلم الأرمينية مع النطق العربي!**
