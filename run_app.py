#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل تطبيق تعلم الأرمينية
Run Armenian Learning App
"""

import os
import webbrowser
import json
from pathlib import Path

def main():
    print("🇦🇲 تشغيل تطبيق تعلم اللغة الأرمينية")
    print("=" * 45)
    
    app_dir = Path(__file__).parent
    
    # فحص البيانات
    json_file = app_dir / 'vocabulary_data_complete.json'
    if json_file.exists():
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            lessons = len(data)
            words = sum(len(w) for w in data.values())
            
            print(f"📊 البيانات المتاحة:")
            print(f"   📚 {lessons} درس")
            print(f"   📝 {words} كلمة")
            print()
            
        except Exception as e:
            print(f"⚠️ خطأ في قراءة البيانات: {e}")
    else:
        print("⚠️ لا توجد بيانات مستخرجة")
        print("💡 لاستخراج البيانات: python smart_extractor.py")
        print()
    
    # قائمة الملفات للفتح
    files_to_open = [
        ('demo_extracted_data.html', '🎉 عرض البيانات المستخرجة'),
        ('index.html', '📱 التطبيق الرئيسي')
    ]
    
    opened_files = 0
    
    for filename, description in files_to_open:
        file_path = app_dir / filename
        if file_path.exists():
            try:
                file_url = file_path.as_uri()
                webbrowser.open(file_url)
                print(f"✅ تم فتح {description}")
                opened_files += 1
                
                # توقف قصير بين الفتحات
                import time
                time.sleep(1)
                
            except Exception as e:
                print(f"❌ فشل فتح {filename}: {e}")
        else:
            print(f"❌ {filename} غير موجود")
    
    if opened_files > 0:
        print(f"\n🎉 تم فتح {opened_files} ملف في المتصفح!")
        print()
        print("🎯 ميزات التطبيق:")
        print("   🔍 البحث بالنطق العربي")
        print("   🔊 تشغيل الصوتيات") 
        print("   📊 تصفح الدروس")
        print("   💾 تصدير المفردات")
        print()
        print("🔤 جرب البحث عن: ميك، باريف، تشورس")
    else:
        print("❌ لم يتم فتح أي ملف")
        print()
        print("💡 تأكد من وجود الملفات:")
        print("   - index.html")
        print("   - demo_extracted_data.html")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    input("\nاضغط Enter للخروج...")
