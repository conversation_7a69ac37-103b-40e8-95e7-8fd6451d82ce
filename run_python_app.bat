@echo off
chcp 65001 >nul
cd /d "%~dp0"
echo.
echo ========================================
echo    🇦🇲 تطبيق تعلم اللغة الأرمينية
echo    Armenian Learning App
echo ========================================
echo.

REM فحص Python
echo 🔍 فحص Python...
python --version >nul 2>&1
if not errorlevel 1 (
    set PYTHON_CMD=python
    python --version
    goto :python_found
)

py --version >nul 2>&1
if not errorlevel 1 (
    set PYTHON_CMD=py
    py --version
    goto :python_found
)

echo ❌ Python غير مثبت!
echo.
echo 💡 لتثبيت Python:
echo    انقر نقراً مزدوجاً على: install_python_easy.bat
echo.
pause
exit /b 1

:python_found
echo ✅ Python متوفر
echo.

REM فحص الملفات
if exist quick_start.py (
    echo 🚀 تشغيل التطبيق...
    %PYTHON_CMD% quick_start.py
) else if exist extract_data.py (
    echo 📥 بدء استخراج البيانات...
    %PYTHON_CMD% extract_data.py
) else if exist armenian_app_manager.py (
    echo 🔧 تشغيل مدير التطبيق...
    %PYTHON_CMD% armenian_app_manager.py
) else (
    echo ❌ لا توجد ملفات Python للتشغيل
    echo 💡 تأكد من وجود الملفات في نفس المجلد
)

echo.
pause
