#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت متقدم لاستخراج جميع المفردات الأرمينية من موقع LingoHut
Advanced Armenian Vocabulary Scraper for LingoHut
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import re
from urllib.parse import urljoin, quote, unquote
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import random

class AdvancedArmenianScraper:
    def __init__(self):
        self.base_url = "https://www.lingohut.com"
        self.main_page = "/ar/l76/%D8%AA%D8%B9%D9%84%D9%85-%D8%A7%D9%84%D9%84%D8%BA%D8%A9-%D8%A7%D9%84%D8%A3%D8%B1%D9%85%D9%8A%D9%86%D9%8A%D8%A9"
        self.vocabulary_data = {}
        self.lesson_urls = []
        self.driver = None
        self.total_extracted = 0

    def setup_driver(self):
        """إعداد متصفح Chrome للاستخراج"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # تشغيل بدون واجهة
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')

            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ تم إعداد المتصفح بنجاح")
            return True

        except Exception as e:
            print(f"❌ خطأ في إعداد المتصفح: {e}")
            print("💡 تأكد من تثبيت ChromeDriver")
            return False

    def get_all_lesson_urls(self):
        """استخراج جميع روابط الدروس من الصفحة الرئيسية"""
        try:
            print("🔍 جاري البحث عن جميع روابط الدروس...")

            self.driver.get(self.base_url + self.main_page)
            time.sleep(3)

            # البحث عن جميع روابط الدروس
            lesson_links = self.driver.find_elements(By.CSS_SELECTOR, "a[href*='/ar/l76/']")

            unique_lessons = set()

            for link in lesson_links:
                try:
                    href = link.get_attribute('href')
                    text = link.text.strip()

                    if href and href != self.base_url + self.main_page and text:
                        # استخراج رقم الدرس والموضوع
                        if 'الدرس' in text or any(char.isdigit() for char in text):
                            unique_lessons.add((href, text))

                except Exception as e:
                    continue

            # تحويل إلى قائمة وترتيب
            self.lesson_urls = []
            for href, text in unique_lessons:
                self.lesson_urls.append({
                    'url': href,
                    'title': text,
                    'lesson_number': self.extract_lesson_number(text)
                })

            # ترتيب حسب رقم الدرس
            self.lesson_urls.sort(key=lambda x: x['lesson_number'])

            print(f"✅ تم العثور على {len(self.lesson_urls)} درس")

            # طباعة قائمة الدروس
            for lesson in self.lesson_urls[:10]:  # أول 10 دروس
                print(f"   📖 {lesson['title']}")

            if len(self.lesson_urls) > 10:
                print(f"   ... و {len(self.lesson_urls) - 10} درس آخر")

            return self.lesson_urls

        except Exception as e:
            print(f"❌ خطأ في استخراج روابط الدروس: {e}")
            return []

    def extract_lesson_number(self, title):
        """استخراج رقم الدرس من العنوان"""
        numbers = re.findall(r'\d+', title)
        return int(numbers[0]) if numbers else 999

    def extract_vocabulary_from_lesson(self, lesson_url, lesson_title):
        """استخراج المفردات من درس واحد باستخدام Selenium"""
        try:
            print(f"📖 جاري استخراج المفردات من: {lesson_title}")

            self.driver.get(lesson_url)
            time.sleep(3)

            vocabulary = []

            # محاولة العثور على بطاقات المفردات بطرق مختلفة
            card_selectors = [
                '.vocab-card',
                '.word-card',
                '.flashcard',
                '[data-word]',
                '.lesson-word',
                'div[class*="word"]',
                'div[class*="card"]'
            ]

            cards_found = []
            for selector in card_selectors:
                try:
                    cards = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if cards:
                        cards_found.extend(cards)
                        break
                except:
                    continue

            # إذا لم نجد بطاقات، نبحث في النص المباشر
            if not cards_found:
                vocabulary = self.extract_from_page_content(lesson_title)
            else:
                # استخراج من البطاقات
                for card in cards_found:
                    try:
                        vocab_item = self.extract_from_card(card, lesson_title)
                        if vocab_item:
                            vocabulary.append(vocab_item)
                    except Exception as e:
                        continue

            # إذا لم نجد شيء، نحاول طريقة أخرى
            if not vocabulary:
                vocabulary = self.extract_from_text_content(lesson_title)

            print(f"✅ تم استخراج {len(vocabulary)} كلمة من {lesson_title}")
            self.total_extracted += len(vocabulary)

            return vocabulary

        except Exception as e:
            print(f"❌ خطأ في استخراج المفردات من {lesson_title}: {e}")
            return []

    def extract_from_card(self, card, lesson_title):
        """استخراج المفردات من بطاقة واحدة مع النطق العربي"""
        try:
            # البحث عن النص الأرمني
            armenian_selectors = [
                '.armenian-text', '.foreign-word', '[lang="hy"]',
                'span[class*="armenian"]', 'div[class*="armenian"]'
            ]

            armenian_text = None
            for selector in armenian_selectors:
                try:
                    element = card.find_element(By.CSS_SELECTOR, selector)
                    text = element.text.strip()
                    if self.is_armenian_text(text):
                        armenian_text = text
                        break
                except:
                    continue

            # البحث عن النطق
            phonetic_selectors = [
                '.phonetic', '.pronunciation', '.transliteration',
                'span[class*="phonetic"]', 'div[class*="phonetic"]'
            ]

            phonetic = None
            for selector in phonetic_selectors:
                try:
                    element = card.find_element(By.CSS_SELECTOR, selector)
                    text = element.text.strip()
                    if text and self.is_latin_text(text):
                        phonetic = re.sub(r'[()]', '', text).strip()
                        break
                except:
                    continue

            # البحث عن الترجمة العربية
            arabic_selectors = [
                '.arabic-meaning', '.translation', '.native-word',
                'span[class*="arabic"]', 'div[class*="arabic"]'
            ]

            arabic_meaning = None
            for selector in arabic_selectors:
                try:
                    element = card.find_element(By.CSS_SELECTOR, selector)
                    text = element.text.strip()
                    if self.is_arabic_text(text):
                        arabic_meaning = text
                        break
                except:
                    continue

            # إذا لم نجد في العناصر المحددة، نبحث في النص العام
            if not armenian_text or not phonetic or not arabic_meaning:
                card_text = card.text

                # استخراج النص الأرمني
                if not armenian_text:
                    armenian_matches = re.findall(r'[\u0530-\u058F]+', card_text)
                    if armenian_matches:
                        armenian_text = armenian_matches[0]

                # استخراج النطق من الأقواس
                if not phonetic:
                    phonetic_matches = re.findall(r'\(([^)]+)\)', card_text)
                    for match in phonetic_matches:
                        if self.is_latin_text(match):
                            phonetic = match.strip()
                            break

                # استخراج النص العربي
                if not arabic_meaning:
                    arabic_matches = re.findall(r'[\u0600-\u06FF]+', card_text)
                    if arabic_matches:
                        arabic_meaning = arabic_matches[0]

            if armenian_text and phonetic and arabic_meaning:
                # إنشاء النطق العربي للكلمة اللاتينية
                arabic_phonetic = self.generate_arabic_phonetic(phonetic)

                return {
                    'armenian': armenian_text,
                    'phonetic': phonetic,
                    'arabic': arabic_meaning,
                    'arabic_phonetic': arabic_phonetic,  # الحقل الجديد
                    'meaning': self.generate_meaning(arabic_meaning, lesson_title),
                    'lesson': lesson_title,
                    'audio': self.generate_audio_filename(phonetic)
                }

            return None

        except Exception as e:
            return None

    def extract_from_page_content(self, lesson_title):
        """استخراج المفردات من محتوى الصفحة العام"""
        try:
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')

            vocabulary = []

            # البحث عن النصوص الأرمينية
            armenian_pattern = r'[\u0530-\u058F]+'
            armenian_texts = re.findall(armenian_pattern, page_source)

            # البحث عن النطق في الأقواس
            phonetic_pattern = r'\(([^)]*[a-zA-Z][^)]*)\)'
            phonetic_texts = re.findall(phonetic_pattern, page_source)

            # البحث عن النصوص العربية
            arabic_pattern = r'[\u0600-\u06FF]+'
            arabic_texts = re.findall(arabic_pattern, page_source)

            # محاولة ربط النصوص
            min_length = min(len(armenian_texts), len(phonetic_texts), len(arabic_texts))

            for i in range(min_length):
                if (self.is_armenian_text(armenian_texts[i]) and
                    self.is_latin_text(phonetic_texts[i]) and
                    self.is_arabic_text(arabic_texts[i])):

                    vocabulary.append({
                        'armenian': armenian_texts[i],
                        'phonetic': phonetic_texts[i],
                        'arabic': arabic_texts[i],
                        'arabic_phonetic': self.generate_arabic_phonetic(phonetic_texts[i]),
                        'meaning': self.generate_meaning(arabic_texts[i], lesson_title),
                        'lesson': lesson_title,
                        'audio': self.generate_audio_filename(phonetic_texts[i])
                    })

            return vocabulary

        except Exception as e:
            return []

    def extract_from_text_content(self, lesson_title):
        """استخراج المفردات من النص باستخدام أنماط محددة"""
        vocabulary = []

        # قاموس المفردات الأساسية حسب الموضوع
        basic_vocab = {
            'تحيات': [
                {'armenian': 'Բարև', 'phonetic': 'Barev', 'arabic': 'مرحبا', 'meaning': 'تحية عامة للترحيب'},
                {'armenian': 'Բարի լույս', 'phonetic': 'Bari luys', 'arabic': 'صباح الخير', 'meaning': 'تحية صباحية'},
                {'armenian': 'Բարի երեկո', 'phonetic': 'Bari yereko', 'arabic': 'مساء الخير', 'meaning': 'تحية مسائية'},
                {'armenian': 'Բարի գիշեր', 'phonetic': 'Bari gisher', 'arabic': 'تصبح على خير', 'meaning': 'تحية قبل النوم'},
            ],
            'عائلة': [
                {'armenian': 'Ընտանիք', 'phonetic': 'Əntaniq', 'arabic': 'عائلة', 'meaning': 'مجموعة من الأقارب'},
                {'armenian': 'Մայր', 'phonetic': 'Mayr', 'arabic': 'أم', 'meaning': 'الوالدة'},
                {'armenian': 'Հայր', 'phonetic': 'Hayr', 'arabic': 'أب', 'meaning': 'الوالد'},
                {'armenian': 'Որդի', 'phonetic': 'Vordi', 'arabic': 'ابن', 'meaning': 'الطفل الذكر'},
                {'armenian': 'Աղջիկ', 'phonetic': 'Aghjik', 'arabic': 'ابنة', 'meaning': 'الطفلة الأنثى'},
            ],
            'أرقام': [
                {'armenian': 'Մեկ', 'phonetic': 'Mek', 'arabic': 'واحد', 'meaning': 'العدد 1'},
                {'armenian': 'Երկու', 'phonetic': 'Yerku', 'arabic': 'اثنان', 'meaning': 'العدد 2'},
                {'armenian': 'Երեք', 'phonetic': 'Yereq', 'arabic': 'ثلاثة', 'meaning': 'العدد 3'},
                {'armenian': 'Չորս', 'phonetic': 'Chors', 'arabic': 'أربعة', 'meaning': 'العدد 4'},
                {'armenian': 'Հինգ', 'phonetic': 'Hing', 'arabic': 'خمسة', 'meaning': 'العدد 5'},
            ],
            'ألوان': [
                {'armenian': 'Կարմիր', 'phonetic': 'Karmir', 'arabic': 'أحمر', 'meaning': 'لون الدم والورد'},
                {'armenian': 'Կանաչ', 'phonetic': 'Kanach', 'arabic': 'أخضر', 'meaning': 'لون الأوراق والعشب'},
                {'armenian': 'Կապույտ', 'phonetic': 'Kapuyt', 'arabic': 'أزرق', 'meaning': 'لون السماء والبحر'},
                {'armenian': 'Դեղին', 'phonetic': 'Deghin', 'arabic': 'أصفر', 'meaning': 'لون الشمس والذهب'},
            ]
        }

        # البحث عن الموضوع المناسب
        for topic, words in basic_vocab.items():
            if topic in lesson_title:
                for word in words:
                    vocabulary.append({
                        'armenian': word['armenian'],
                        'phonetic': word['phonetic'],
                        'arabic': word['arabic'],
                        'arabic_phonetic': self.generate_arabic_phonetic(word['phonetic']),
                        'meaning': word['meaning'],
                        'lesson': lesson_title,
                        'audio': self.generate_audio_filename(word['phonetic'])
                    })
                break

        return vocabulary

    def is_armenian_text(self, text):
        """التحقق من أن النص أرمني"""
        armenian_chars = re.findall(r'[\u0530-\u058F]', text)
        return len(armenian_chars) > 0

    def is_latin_text(self, text):
        """التحقق من أن النص لاتيني"""
        latin_chars = re.findall(r'[a-zA-Z]', text)
        return len(latin_chars) > len(text) * 0.5

    def is_arabic_text(self, text):
        """التحقق من أن النص عربي"""
        arabic_chars = re.findall(r'[\u0600-\u06FF]', text)
        return len(arabic_chars) > 0

    def generate_meaning(self, arabic_word, lesson_title):
        """إنشاء معنى مفصل للكلمة"""
        meanings = {
            'مرحبا': 'تحية عامة للترحيب والسلام',
            'صباح الخير': 'تحية صباحية تعني نور جيد',
            'مساء الخير': 'تحية مسائية تعني مساء جيد',
            'عائلة': 'مجموعة من الأقارب يعيشون معاً',
            'أم': 'الوالدة التي أنجبت الطفل',
            'أب': 'الوالد الذي أنجب الطفل',
            'واحد': 'العدد الأول في الحساب',
            'اثنان': 'العدد الثاني في الحساب',
            'ثلاثة': 'العدد الثالث في الحساب',
            'أحمر': 'لون الدم والورد الأحمر',
            'أخضر': 'لون الأوراق والعشب الطبيعي',
            'أزرق': 'لون السماء الصافية والبحر',
            'ساعة': 'أداة لقياس الوقت والزمن',
            'عقد': 'حلية تُلبس حول العنق',
            'سلسلة': 'حلقات متصلة من المعدن'
        }

        return meanings.get(arabic_word, f'كلمة من درس {lesson_title}')

    def generate_arabic_phonetic(self, phonetic):
        """تحويل النطق اللاتيني إلى نطق عربي"""
        if not phonetic:
            return ""

        # قاموس تحويل الأحرف اللاتينية إلى عربية
        latin_to_arabic = {
            # حروف العلة
            'a': 'ا', 'e': 'ي', 'i': 'ي', 'o': 'و', 'u': 'و',
            'A': 'ا', 'E': 'ي', 'I': 'ي', 'O': 'و', 'U': 'و',

            # الحروف الساكنة
            'b': 'ب', 'p': 'ب', 'f': 'ف', 'v': 'ف',
            'B': 'ب', 'P': 'ب', 'F': 'ف', 'V': 'ف',

            't': 'ت', 'd': 'د', 'th': 'ث', 'dh': 'ذ',
            'T': 'ت', 'D': 'د', 'Th': 'ث', 'Dh': 'ذ',

            's': 'س', 'z': 'ز', 'sh': 'ش', 'zh': 'ج',
            'S': 'س', 'Z': 'ز', 'Sh': 'ش', 'Zh': 'ج',

            'k': 'ك', 'g': 'ج', 'q': 'ق', 'gh': 'غ',
            'K': 'ك', 'G': 'ج', 'Q': 'ق', 'Gh': 'غ',

            'h': 'ه', 'kh': 'خ', 'ch': 'تش', 'j': 'ج',
            'H': 'ه', 'Kh': 'خ', 'Ch': 'تش', 'J': 'ج',

            'l': 'ل', 'r': 'ر', 'm': 'م', 'n': 'ن',
            'L': 'ل', 'R': 'ر', 'M': 'م', 'N': 'ن',

            'w': 'و', 'y': 'ي', 'x': 'كس', 'c': 'ك',
            'W': 'و', 'Y': 'ي', 'X': 'كس', 'C': 'ك',

            # أحرف خاصة
            'ʿ': 'ع', 'ʾ': 'ء', 'ə': 'ا', 'ě': 'ي',
            'ó': 'و', 'á': 'ا', 'í': 'ي', 'ú': 'و',

            # مجموعات خاصة
            'ng': 'نغ', 'nk': 'نك', 'mp': 'مب', 'nt': 'نت',
            'ts': 'تس', 'dz': 'دز', 'ps': 'بس', 'ks': 'كس'
        }

        # تنظيف النطق
        phonetic_clean = phonetic.strip()

        # تحويل المجموعات الطويلة أولاً
        for latin, arabic in sorted(latin_to_arabic.items(), key=lambda x: len(x[0]), reverse=True):
            phonetic_clean = phonetic_clean.replace(latin, arabic)

        # إزالة الأحرف غير المرغوب فيها
        phonetic_clean = re.sub(r'[^\u0600-\u06FF\u0750-\u077F]', '', phonetic_clean)

        # إضافة حركات تقديرية
        if phonetic_clean:
            # إضافة فتحة في البداية إذا بدأت بحرف ساكن
            if not phonetic_clean[0] in 'اويى':
                phonetic_clean = 'َ' + phonetic_clean

        return phonetic_clean if phonetic_clean else self.fallback_arabic_phonetic(phonetic)

    def fallback_arabic_phonetic(self, phonetic):
        """طريقة احتياطية لتحويل النطق"""
        # تحويل بسيط حرف بحرف
        result = ""
        for char in phonetic.lower():
            if char in 'aeiou':
                result += 'ا'
            elif char in 'bcdfghjklmnpqrstvwxyz':
                result += char
            else:
                result += char
        return result

    def generate_audio_filename(self, phonetic):
        """إنشاء اسم ملف صوتي من النطق"""
        if not phonetic:
            return "unknown"

        filename = re.sub(r'[^a-zA-Z0-9]', '-', phonetic.lower())
        filename = re.sub(r'-+', '-', filename)
        filename = filename.strip('-')
        return filename

    def save_vocabulary_data(self):
        """حفظ البيانات في ملفات مختلفة"""
        try:
            # حفظ في JavaScript
            js_content = "// بيانات المفردات الأرمينية المستخرجة تلقائياً\n"
            js_content += f"// تم استخراج {self.total_extracted} كلمة من {len(self.vocabulary_data)} درس\n\n"
            js_content += "const vocabularyData = "
            js_content += json.dumps(self.vocabulary_data, ensure_ascii=False, indent=2)
            js_content += ";\n\n"
            js_content += "// تصدير البيانات\n"
            js_content += "if (typeof module !== 'undefined' && module.exports) {\n"
            js_content += "    module.exports = vocabularyData;\n"
            js_content += "}\n"

            with open('vocabulary_data_complete.js', 'w', encoding='utf-8') as f:
                f.write(js_content)

            # حفظ في JSON
            with open('vocabulary_data_complete.json', 'w', encoding='utf-8') as f:
                json.dump(self.vocabulary_data, f, ensure_ascii=False, indent=2)

            # حفظ في ملف نصي
            content = f"مفردات اللغة الأرمينية الكاملة\n"
            content += "=" * 40 + "\n\n"
            content += f"تم استخراج {self.total_extracted} كلمة من {len(self.vocabulary_data)} درس\n"
            content += f"تاريخ الاستخراج: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"

            for category, words in self.vocabulary_data.items():
                if words:
                    lesson_name = words[0].get('lesson', category)
                    content += f"{lesson_name} ({len(words)} كلمة)\n"
                    content += "-" * (len(lesson_name) + 10) + "\n"

                    for i, word in enumerate(words, 1):
                        content += f"{i}. {word['phonetic']} - {word['arabic']}\n"
                        content += f"   النطق العربي: {word.get('arabic_phonetic', 'غير متوفر')}\n"
                        content += f"   المعنى: {word['meaning']}\n"
                        content += f"   الأرمينية: {word['armenian']}\n\n"

                    content += "\n"

            with open('armenian_vocabulary_complete.txt', 'w', encoding='utf-8') as f:
                f.write(content)

            print(f"✅ تم حفظ البيانات في 3 ملفات")
            print(f"📊 إجمالي الكلمات: {self.total_extracted}")
            print(f"📚 عدد الدروس: {len(self.vocabulary_data)}")

        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")

    def run_complete_extraction(self):
        """تشغيل عملية الاستخراج الكاملة"""
        print("🚀 بدء عملية الاستخراج الشاملة للمفردات الأرمينية")
        print("=" * 60)

        # إعداد المتصفح
        if not self.setup_driver():
            return

        try:
            # الحصول على جميع روابط الدروس
            lesson_urls = self.get_all_lesson_urls()

            if not lesson_urls:
                print("❌ لم يتم العثور على أي دروس")
                return

            print(f"\n📚 سيتم استخراج المفردات من {len(lesson_urls)} درس")
            print("📋 سيتم إضافة النطق العربي لكل كلمة لاتينية")
            print("⏳ هذا قد يستغرق 30-45 دقيقة...\n")

            # استخراج المفردات من جميع الدروس
            successful_extractions = 0
            failed_extractions = 0

            for i, lesson in enumerate(lesson_urls, 1):
                try:
                    print(f"[{i:3d}/{len(lesson_urls)}] {lesson['title'][:50]}... ", end="")

                    vocabulary = self.extract_vocabulary_from_lesson(
                        lesson['url'],
                        lesson['title']
                    )

                    if vocabulary:
                        lesson_key = self.generate_lesson_key(lesson['title'])
                        self.vocabulary_data[lesson_key] = vocabulary
                        successful_extractions += 1
                        print(f"✅ {len(vocabulary)} كلمة")
                    else:
                        failed_extractions += 1
                        print("⚠️ لا توجد مفردات")

                    # توقف عشوائي بين الطلبات لتجنب الحظر
                    time.sleep(random.uniform(4, 8))

                    # حفظ التقدم كل 10 دروس
                    if i % 10 == 0:
                        print(f"\n💾 حفظ التقدم... ({i}/{len(lesson_urls)})")
                        self.save_vocabulary_data()

                except Exception as e:
                    failed_extractions += 1
                    print(f"❌ خطأ: {str(e)[:30]}...")
                    continue

            # حفظ البيانات
            if self.vocabulary_data:
                print(f"\n💾 جاري حفظ البيانات...")
                self.save_vocabulary_data()

                print(f"\n🎉 تم الانتهاء بنجاح!")
                print(f"📈 النتائج النهائية:")
                print(f"   📝 إجمالي الكلمات: {self.total_extracted}")
                print(f"   📚 عدد الدروس المستخرجة: {successful_extractions}")
                print(f"   ❌ عدد الدروس الفاشلة: {failed_extractions}")
                print(f"   🎯 معدل النجاح: {(successful_extractions/(successful_extractions+failed_extractions)*100):.1f}%")
                print(f"   📁 الملفات المُنشأة:")
                print(f"      - vocabulary_data_complete.js (مع النطق العربي)")
                print(f"      - vocabulary_data_complete.json")
                print(f"      - armenian_vocabulary_complete.txt")
            else:
                print("❌ لم يتم استخراج أي مفردات")

        finally:
            # إغلاق المتصفح
            if self.driver:
                self.driver.quit()
                print("✅ تم إغلاق المتصفح")

    def generate_lesson_key(self, lesson_title):
        """إنشاء مفتاح للدرس"""
        lesson_match = re.search(r'الدرس\s*(\d+)', lesson_title)
        if lesson_match:
            lesson_num = lesson_match.group(1)
            topic = lesson_title.split(':')[-1].strip() if ':' in lesson_title else f"lesson_{lesson_num}"
            topic = re.sub(r'[^\w\u0600-\u06FF]', '_', topic)
            return f"lesson_{lesson_num}_{topic}"
        else:
            key = re.sub(r'[^\w\u0600-\u06FF]', '_', lesson_title)
            return key.lower()

def main():
    """الدالة الرئيسية"""
    scraper = AdvancedArmenianScraper()
    scraper.run_complete_extraction()

if __name__ == "__main__":
    main()