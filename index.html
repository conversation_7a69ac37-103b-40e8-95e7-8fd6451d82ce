<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🇦🇲 تطبيق تعلم اللغة الأرمينية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <meta name="description" content="تعلم المفردات الأرمينية مع النطق الصحيح والترجمة العربية">
    <meta name="keywords" content="أرمينية, تعلم, مفردات, نطق, ترجمة, عربي">
    
    <!-- إعدادات PWA -->
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="تعلم الأرمينية">
    
    <!-- أيقونات -->
    <link rel="icon" type="image/png" sizes="32x32" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🇦🇲</text></svg>">
</head>
<body>
    <div class="container">
        <!-- الرأس -->
        <header>
            <h1>🇦🇲 تطبيق تعلم اللغة الأرمينية</h1>
            <p>تعلم المفردات الأرمينية مع النطق الصحيح والمعاني المفصلة</p>
        </header>

        <!-- أدوات التحكم -->
        <div class="controls">
            <select id="lessonSelect">
                <option value="all">جميع الدروس</option>
                <option value="jewelry">مجوهرات</option>
                <option value="greetings">تحيات</option>
                <option value="family">العائلة</option>
                <option value="numbers">الأرقام</option>
                <option value="colors">الألوان</option>
            </select>

            <div class="search-box">
                <input type="text" id="searchInput" placeholder="ابحث عن كلمة...">
                <button id="searchBtn">🔍</button>
            </div>
        </div>

        <!-- الإحصائيات -->
        <div class="stats">
            <div class="stat-card">
                <span class="stat-number" id="totalWords">17</span>
                <span class="stat-label">إجمالي الكلمات</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="totalLessons">5</span>
                <span class="stat-label">عدد الدروس</span>
            </div>
        </div>

        <!-- شبكة المفردات -->
        <div class="vocabulary-grid" id="vocabularyGrid">
            <!-- سيتم ملؤها بواسطة JavaScript -->
        </div>

        <!-- مشغل الصوت العائم -->
        <div class="floating-player" id="audioPlayer" style="display: none;">
            <div class="player-content">
                <span id="currentWord">كلمة حالية</span>
                <div class="player-controls">
                    <button id="repeatBtn" title="إعادة">🔄</button>
                    <button id="playBtn" title="تشغيل">▶️</button>
                    <button id="closePlayer" title="إغلاق">❌</button>
                </div>
            </div>
        </div>

        <!-- تعليمات الاستخدام -->
        <div class="instructions" style="background: rgba(255,255,255,0.95); padding: 20px; border-radius: 15px; margin-top: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
            <h3 style="color: #4a5568; margin-bottom: 15px;">📖 كيفية الاستخدام:</h3>
            <ul style="color: #718096; line-height: 1.6;">
                <li><strong>🔊 تشغيل الصوت:</strong> انقر على أي بطاقة أو زر التشغيل</li>
                <li><strong>🔍 البحث:</strong> ابحث بالعربية أو النطق اللاتيني</li>
                <li><strong>📚 اختيار الدرس:</strong> استخدم القائمة المنسدلة</li>
                <li><strong>📥 التصدير:</strong> انقر على زر "تصدير المفردات" في الأعلى</li>
                <li><strong>💡 المعاني:</strong> كل بطاقة تحتوي على النطق والمعنى المفصل</li>
            </ul>
        </div>

        <!-- معلومات إضافية -->
        <div class="info-section" style="background: rgba(255,255,255,0.95); padding: 20px; border-radius: 15px; margin-top: 20px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
            <h3 style="color: #4a5568; margin-bottom: 15px;">ℹ️ معلومات التطبيق:</h3>
            <div style="color: #718096; line-height: 1.6;">
                <p><strong>🎯 الهدف:</strong> تعلم المفردات الأرمينية الأساسية مع النطق الصحيح</p>
                <p><strong>🔤 النطق:</strong> مكتوب بالأحرف اللاتينية لسهولة التعلم</p>
                <p><strong>📱 التوافق:</strong> يعمل على جميع الأجهزة والمتصفحات</p>
                <p><strong>🔄 التحديث:</strong> يمكن إضافة المزيد من الدروس والمفردات</p>
            </div>
        </div>

        <!-- تذييل -->
        <footer style="text-align: center; margin-top: 40px; padding: 20px; color: rgba(255,255,255,0.8);">
            <p>🇦🇲 تم إنشاء هذا التطبيق بحب لمساعدة متعلمي اللغة الأرمينية العرب ❤️</p>
            <p style="font-size: 0.9rem; margin-top: 10px;">
                📧 للاقتراحات والتحسينات: تواصل معنا
            </p>
        </footer>
    </div>

    <!-- تحميل JavaScript -->
    <script src="script.js"></script>
    
    <!-- إعدادات إضافية للصوت -->
    <script>
        // تحسين تجربة الصوت في المتصفحات المختلفة
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من دعم Web Speech API
            if ('speechSynthesis' in window) {
                console.log('✅ Web Speech API مدعوم');
            } else {
                console.log('⚠️ Web Speech API غير مدعوم');
            }
            
            // التحقق من دعم Audio API
            try {
                const audio = new Audio();
                console.log('✅ Audio API مدعوم');
            } catch (e) {
                console.log('⚠️ Audio API غير مدعوم');
            }
            
            // إضافة مستمع للنقر لتفعيل الصوت (مطلوب في Chrome)
            document.addEventListener('click', function enableAudio() {
                const audio = new Audio();
                audio.play().then(() => {
                    audio.pause();
                    console.log('✅ تم تفعيل الصوت');
                }).catch(() => {
                    console.log('⚠️ تعذر تفعيل الصوت');
                });
                
                // إزالة المستمع بعد أول نقرة
                document.removeEventListener('click', enableAudio);
            }, { once: true });
        });
        
        // إضافة دعم لاختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            // مسافة = تشغيل/إيقاف الصوت الحالي
            if (e.code === 'Space' && e.target.tagName !== 'INPUT') {
                e.preventDefault();
                const playBtn = document.getElementById('playBtn');
                if (playBtn && document.getElementById('audioPlayer').style.display !== 'none') {
                    playBtn.click();
                }
            }
            
            // Escape = إغلاق مشغل الصوت
            if (e.code === 'Escape') {
                const closeBtn = document.getElementById('closePlayer');
                if (closeBtn && document.getElementById('audioPlayer').style.display !== 'none') {
                    closeBtn.click();
                }
            }
            
            // Ctrl+F = التركيز على مربع البحث
            if (e.ctrlKey && e.code === 'KeyF') {
                e.preventDefault();
                document.getElementById('searchInput').focus();
            }
        });
        
        // إضافة تأثيرات بصرية للتفاعل
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التمرير السلس
            document.documentElement.style.scrollBehavior = 'smooth';
            
            // إضافة تأثير التحميل
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease-in-out';
            
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>
</body>
</html>