<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النطق العربي - تطبيق تعلم الأرمينية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #4a5568;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }

        .test-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .armenian-text {
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
            text-align: center;
            margin-bottom: 15px;
            font-family: 'Arial Unicode MS', 'Lucida Grande', sans-serif;
        }

        .phonetic {
            font-size: 1.2rem;
            color: #666;
            font-style: italic;
            text-align: center;
            margin: 10px 0;
            background: rgba(102, 126, 234, 0.1);
            padding: 8px 15px;
            border-radius: 20px;
            display: inline-block;
            width: 100%;
        }

        .arabic-phonetic {
            font-size: 1.2rem;
            color: #2c5aa0;
            font-weight: 600;
            margin: 10px 0;
            background: linear-gradient(135deg, #e3f2fd, #f8f9fa);
            padding: 10px 15px;
            border-radius: 20px;
            border: 2px solid #bbdefb;
            text-align: center;
            box-shadow: 0 3px 6px rgba(44, 90, 160, 0.1);
        }

        .arabic-meaning {
            font-size: 1.4rem;
            color: #2d3748;
            font-weight: 600;
            text-align: center;
            margin: 15px 0;
        }

        .word-meaning {
            font-size: 1rem;
            color: #718096;
            text-align: center;
            line-height: 1.5;
            font-style: italic;
            border-top: 1px solid #e2e8f0;
            padding-top: 15px;
        }

        .play-button {
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 20px auto;
        }

        .play-button:hover {
            background: #5a67d8;
            transform: scale(1.1);
        }

        .info-box {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .feature-highlight {
            background: #e6fffa;
            border: 2px solid #38b2ac;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }

        .feature-highlight h3 {
            color: #2c7a7b;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔤 اختبار النطق العربي الجديد</h1>
        
        <div class="info-box">
            <h2>✨ الميزة الجديدة</h2>
            <p>الآن كل بطاقة تحتوي على 4 حقول:</p>
            <p><strong>النص الأرمني + النطق اللاتيني + النطق العربي + المعنى العربي</strong></p>
        </div>

        <div class="feature-highlight">
            <h3>🎯 مثال على التحسين:</h3>
            <p>بدلاً من: <code>Meg</code> فقط</p>
            <p>الآن: <code>Meg</code> + <strong>ميك</strong> (النطق العربي)</p>
        </div>

        <!-- بطاقة تجريبية 1 -->
        <div class="test-card">
            <div class="armenian-text">Մեկ</div>
            <div class="phonetic">(Meg)</div>
            <div class="arabic-phonetic">ميك</div>
            <div class="arabic-meaning">1</div>
            <div class="word-meaning">الرقم واحد في اللغة الأرمينية</div>
            <button class="play-button" onclick="testAudio('meg')">▶️</button>
        </div>

        <!-- بطاقة تجريبية 2 -->
        <div class="test-card">
            <div class="armenian-text">Բարև</div>
            <div class="phonetic">(Barev)</div>
            <div class="arabic-phonetic">باريف</div>
            <div class="arabic-meaning">مرحبا</div>
            <div class="word-meaning">تحية عامة للترحيب في اللغة الأرمينية</div>
            <button class="play-button" onclick="testAudio('barev')">▶️</button>
        </div>

        <!-- بطاقة تجريبية 3 -->
        <div class="test-card">
            <div class="armenian-text">Բարի լույս</div>
            <div class="phonetic">(Bari luys)</div>
            <div class="arabic-phonetic">باري لويس</div>
            <div class="arabic-meaning">صباح الخير</div>
            <div class="word-meaning">تحية صباحية - حرفياً: نور جيد</div>
            <button class="play-button" onclick="testAudio('bari-luys')">▶️</button>
        </div>

        <!-- بطاقة تجريبية 4 -->
        <div class="test-card">
            <div class="armenian-text">ժամացույց</div>
            <div class="phonetic">(Žamacówyc)</div>
            <div class="arabic-phonetic">جاماكوويك</div>
            <div class="arabic-meaning">ساعة</div>
            <div class="word-meaning">أداة لقياس الوقت</div>
            <button class="play-button" onclick="testAudio('zhamacowyc')">▶️</button>
        </div>

        <div class="feature-highlight">
            <h3>🚀 للحصول على جميع الـ 125 درس:</h3>
            <p>1. انقر نقراً مزدوجاً على <code>run_complete_extraction.bat</code></p>
            <p>2. انتظر 30-45 دقيقة للاستخراج الكامل</p>
            <p>3. ستحصل على جميع المفردات مع النطق العربي</p>
        </div>
    </div>

    <script>
        function testAudio(audioFile) {
            // محاولة تشغيل الصوت
            try {
                const audio = new Audio(`audio/${audioFile}.mp3`);
                audio.addEventListener('error', () => {
                    // استخدام تحويل النص إلى كلام كبديل
                    if ('speechSynthesis' in window) {
                        const utterance = new SpeechSynthesisUtterance(audioFile);
                        utterance.lang = 'en-US';
                        utterance.rate = 0.8;
                        speechSynthesis.speak(utterance);
                    } else {
                        alert('تم النقر على: ' + audioFile);
                    }
                });
                audio.play();
            } catch (error) {
                alert('تم النقر على: ' + audioFile);
            }
        }

        // رسالة ترحيب
        window.addEventListener('load', () => {
            setTimeout(() => {
                alert('🎉 مرحباً بك في اختبار النطق العربي الجديد!\n\n✨ الآن كل كلمة لها نطق عربي\n🔤 مثل: Meg → ميك');
            }, 1000);
        });
    </script>
</body>
</html>
