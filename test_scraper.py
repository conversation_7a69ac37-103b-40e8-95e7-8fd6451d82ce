#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للاستخراج
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import re

def test_basic_extraction():
    """اختبار استخراج أساسي بدون Selenium"""
    print("🚀 بدء اختبار الاستخراج الأساسي...")

    base_url = "https://www.lingohut.com"
    main_page = "/ar/l76/%D8%AA%D8%B9%D9%84%D9%85-%D8%A7%D9%84%D9%84%D8%BA%D8%A9-%D8%A7%D9%84%D8%A3%D8%B1%D9%85%D9%8A%D9%86%D9%8A%D8%A9"

    try:
        print("🔍 جاري الاتصال بالموقع...")

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(base_url + main_page, headers=headers, timeout=30)
        response.raise_for_status()

        print(f"✅ تم الاتصال بنجاح! كود الاستجابة: {response.status_code}")

        soup = BeautifulSoup(response.content, 'html.parser')

        # البحث عن روابط الدروس (الرقم الصحيح للدروس الأرمينية)
        lesson_links = soup.find_all('a', href=re.compile(r'/ar/v477'))

        print(f"📚 تم العثور على {len(lesson_links)} رابط درس")

        # عرض جميع الدروس
        lessons_found = []
        for i, link in enumerate(lesson_links):
            href = link.get('href')
            text = link.get_text(strip=True)
            if text and href != main_page and 'درس' in text:
                lessons_found.append({'url': href, 'title': text})
                if i < 10:  # عرض أول 10 فقط
                    print(f"   📖 {i+1}. {text}")

        if len(lessons_found) > 10:
            print(f"   ... و {len(lessons_found) - 10} درس آخر")

        print(f"\n📊 إجمالي الدروس المكتشفة: {len(lessons_found)}")

        # اختبار درس واحد
        if lessons_found:
            print(f"\n🔍 اختبار استخراج من الدرس الأول...")
            test_lesson = lessons_found[0]

            lesson_url = test_lesson['url']
            if not lesson_url.startswith('http'):
                lesson_url = base_url + lesson_url
            lesson_response = requests.get(lesson_url, headers=headers, timeout=30)
            lesson_soup = BeautifulSoup(lesson_response.content, 'html.parser')

            # البحث عن النصوص الأرمينية
            armenian_pattern = r'[\u0530-\u058F]+'
            armenian_texts = re.findall(armenian_pattern, lesson_response.text)

            print(f"   📝 نصوص أرمينية مكتشفة: {len(armenian_texts)}")
            for i, text in enumerate(armenian_texts[:5]):
                print(f"      {i+1}. {text}")

            # البحث عن النطق
            phonetic_pattern = r'\(([^)]*[a-zA-Z][^)]*)\)'
            phonetic_texts = re.findall(phonetic_pattern, lesson_response.text)

            print(f"   🔤 نطق مكتشف: {len(phonetic_texts)}")
            for i, text in enumerate(phonetic_texts[:5]):
                print(f"      {i+1}. {text}")

        print(f"\n✅ اختبار الاستخراج الأساسي مكتمل!")
        return True

    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def generate_arabic_phonetic(phonetic):
    """تحويل النطق اللاتيني إلى عربي"""
    if not phonetic:
        return ""

    # قاموس تحويل مبسط
    latin_to_arabic = {
        'a': 'ا', 'e': 'ي', 'i': 'ي', 'o': 'و', 'u': 'و',
        'b': 'ب', 'p': 'ب', 'f': 'ف', 'v': 'ف',
        't': 'ت', 'd': 'د', 's': 'س', 'z': 'ز',
        'k': 'ك', 'g': 'ج', 'h': 'ه', 'l': 'ل',
        'r': 'ر', 'm': 'م', 'n': 'ن', 'w': 'و', 'y': 'ي'
    }

    result = ""
    for char in phonetic.lower():
        result += latin_to_arabic.get(char, char)

    return result

def test_arabic_phonetic():
    """اختبار تحويل النطق العربي"""
    print("\n🔤 اختبار تحويل النطق العربي...")

    test_cases = [
        "Meg", "Barev", "Bari luys", "Yerku", "Chors"
    ]

    for phonetic in test_cases:
        arabic = generate_arabic_phonetic(phonetic)
        print(f"   {phonetic} → {arabic}")

    print("✅ اختبار النطق العربي مكتمل!")

if __name__ == "__main__":
    print("🧪 بدء الاختبارات...")
    print("=" * 50)

    # اختبار الاستخراج الأساسي
    if test_basic_extraction():
        # اختبار النطق العربي
        test_arabic_phonetic()

        print(f"\n🎉 جميع الاختبارات نجحت!")
        print(f"💡 يمكن الآن تشغيل الاستخراج الكامل")
    else:
        print(f"\n❌ فشل في الاختبار الأساسي")
        print(f"💡 تحقق من اتصال الإنترنت")
