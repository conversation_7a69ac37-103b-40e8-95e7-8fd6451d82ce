#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فتح تطبيق تعلم الأرمينية المصنف بالفئات
Open Armenian Learning App with Categories
"""

import os
import webbrowser
import json
from pathlib import Path

def main():
    print("🇦🇲 فتح تطبيق تعلم الأرمينية المصنف بالفئات")
    print("=" * 55)
    
    app_dir = Path(__file__).parent
    
    # فحص ملف الفئات
    categories_file = app_dir / 'vocabulary_categories.json'
    if categories_file.exists():
        try:
            with open(categories_file, 'r', encoding='utf-8') as f:
                categories = json.load(f)
            
            total_categories = len(categories)
            total_words = sum(len(cat['words']) for cat in categories.values())
            
            print(f"✅ البيانات المصنفة متوفرة:")
            print(f"   📁 {total_categories} فئة")
            print(f"   📝 {total_words} كلمة")
            print()
            
            # عرض الفئات
            print("📋 الفئات المتاحة:")
            for category_name, category_data in categories.items():
                word_count = len(category_data['words'])
                icon = category_data.get('icon', '📝')
                print(f"   {icon} {category_name}: {word_count} كلمة")
            
            print()
            
        except Exception as e:
            print(f"⚠️ خطأ في قراءة البيانات: {e}")
    else:
        print("❌ ملف الفئات غير موجود")
        print("💡 شغل: python organize_categories.py")
        return
    
    # فتح التطبيق
    app_file = app_dir / 'app_categories.html'
    if app_file.exists():
        try:
            file_url = app_file.as_uri()
            webbrowser.open(file_url)
            print("🚀 تم فتح التطبيق المصنف بالفئات!")
            print()
            print("🎯 ميزات التطبيق الجديد:")
            print("   📁 تصنيف ذكي للمفردات")
            print("   🔊 تشغيل صوتي للنطق")
            print("   🔍 بحث متقدم في جميع الفئات")
            print("   🎨 واجهة منظمة وجميلة")
            print("   📱 تصميم متجاوب")
            print()
            print("🔤 الفئات المتاحة:")
            print("   👋 التحيات - 🔢 الأرقام - 👨‍👩‍👧‍👦 العائلة")
            print("   🎨 الألوان - 🍽️ الطعام والشراب")
            print()
            print("🔊 انقر على زر 🔊 لسماع النطق!")
            
        except Exception as e:
            print(f"❌ فشل فتح التطبيق: {e}")
    else:
        print("❌ ملف التطبيق غير موجود")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
