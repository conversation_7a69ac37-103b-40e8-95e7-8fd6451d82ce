#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فتح تطبيق تعلم الأرمينية
Open Armenian Learning App
"""

import json
import webbrowser
import time
from pathlib import Path

def main():
    """فتح التطبيق في المتصفح"""
    print("🇦🇲 فتح تطبيق تعلم اللغة الأرمينية")
    print("=" * 40)
    
    app_dir = Path(__file__).parent
    
    # فحص البيانات المستخرجة
    json_file = app_dir / 'vocabulary_data_complete.json'
    if json_file.exists():
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            lessons = len(data)
            words = sum(len(w) for w in data.values())
            words_with_arabic = sum(
                1 for words_list in data.values() 
                for word in words_list 
                if word.get('arabic_phonetic')
            )
            
            print(f"📊 البيانات المتاحة:")
            print(f"   📚 {lessons} درس")
            print(f"   📝 {words} كلمة")
            print(f"   🔤 {words_with_arabic} كلمة بنطق عربي")
            print()
            
        except Exception as e:
            print(f"⚠️ خطأ في قراءة البيانات: {e}")
    else:
        print("⚠️ لا توجد بيانات مستخرجة")
        print("💡 لاستخراج البيانات: python quick_start.py")
        print()
    
    # فتح الملفات
    files_opened = 0
    
    # عرض البيانات المستخرجة
    demo_file = app_dir / 'demo_extracted_data.html'
    if demo_file.exists():
        print("🎉 فتح عرض البيانات المستخرجة...")
        webbrowser.open(demo_file.as_uri())
        files_opened += 1
        time.sleep(1)
    
    # التطبيق الرئيسي
    index_file = app_dir / 'index.html'
    if index_file.exists():
        print("📱 فتح التطبيق الرئيسي...")
        webbrowser.open(index_file.as_uri())
        files_opened += 1
    
    if files_opened > 0:
        print(f"\n✅ تم فتح {files_opened} ملف في المتصفح")
        print()
        print("🎯 ميزات التطبيق:")
        print("   🔍 البحث بالنطق العربي (جرب: ميك، باريف)")
        print("   🔊 تشغيل الصوتيات")
        print("   📊 تصفح الدروس")
        print("   💾 تصدير المفردات")
    else:
        print("❌ لا توجد ملفات HTML للفتح")
        print("💡 تأكد من وجود index.html أو demo_extracted_data.html")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    input("\nاضغط Enter للخروج...")
