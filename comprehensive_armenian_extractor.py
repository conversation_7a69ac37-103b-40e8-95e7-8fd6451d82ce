#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مستخرج شامل لجميع محتوى تعلم الأرمينية من LingoHut
Comprehensive Armenian Content Extractor from LingoHut
"""

import requests
from bs4 import BeautifulSoup
import json
import re
import time
from datetime import datetime
import random
from urllib.parse import urljoin, quote
import os

class ComprehensiveArmenianExtractor:
    def __init__(self):
        self.base_url = "https://www.lingohut.com"
        self.main_page = "/ar/l76/%D8%AA%D8%B9%D9%84%D9%85-%D8%A7%D9%84%D9%84%D8%BA%D8%A9-%D8%A7%D9%84%D8%A3%D8%B1%D9%85%D9%8A%D9%86%D9%8A%D8%A9"
        
        # هياكل البيانات الشاملة
        self.lessons = {}
        self.vocabulary = {}
        self.explanations = {}
        self.flashcards = {}
        self.tests = {}
        self.grammar = {}
        self.phrases = {}
        self.categories = {}
        
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
        self.session.headers.update(self.headers)

    def generate_arabic_phonetic(self, phonetic):
        """تحويل النطق اللاتيني إلى نطق عربي محسن"""
        if not phonetic:
            return ""

        text = phonetic.strip()
        
        # معالجة الحالات الخاصة
        special_cases = {
            'Meg': 'ميك', 'Mek': 'ميك',
            'Chors': 'تشورس', 'Yerku': 'ييركو',
            'Bari luys': 'باري لويس',
            'Bari yereko': 'باري ييريكو',
            'Bari gisher': 'باري غيشير',
            'Mayr': 'مايير', 'Hayr': 'هايير',
            'Yeghbayr': 'ييغبايير', 'Khoyr': 'خويير',
            'Barev': 'باريف', 'Hing': 'هينغ',
            'Shnorhakalutyun': 'شنورهاكالوتيون'
        }
        
        if text in special_cases:
            return special_cases[text]
        
        # قاموس تحويل شامل
        replacements = {
            'shn': 'شن', 'tch': 'تش', 'ch': 'تش', 'sh': 'ش', 'th': 'ث', 'gh': 'غ',
            'kh': 'خ', 'zh': 'ج', 'ng': 'نغ', 'nk': 'نك', 'ts': 'تس',
            'a': 'ا', 'e': 'ي', 'i': 'ي', 'o': 'و', 'u': 'و',
            'á': 'ا', 'é': 'ي', 'í': 'ي', 'ó': 'و', 'ú': 'و',
            'b': 'ب', 'p': 'ب', 'f': 'ف', 'v': 'ف',
            't': 'ت', 'd': 'د', 's': 'س', 'z': 'ز',
            'k': 'ك', 'g': 'ج', 'h': 'ه', 'j': 'ج',
            'l': 'ل', 'r': 'ر', 'm': 'م', 'n': 'ن',
            'w': 'و', 'y': 'ي', 'c': 'ك', 'q': 'ق'
        }
        
        # معالجة المسافات
        if ' ' in text:
            parts = text.split(' ')
            converted_parts = []
            for part in parts:
                result = part
                for latin, arabic in sorted(replacements.items(), key=lambda x: len(x[0]), reverse=True):
                    result = result.replace(latin, arabic)
                cleaned = ''.join(char for char in result if '\u0600' <= char <= '\u06FF' or char == ' ')
                converted_parts.append(cleaned if cleaned else part)
            return ' '.join(converted_parts)
        else:
            result = text
            for latin, arabic in sorted(replacements.items(), key=lambda x: len(x[0]), reverse=True):
                result = result.replace(latin, arabic)
            cleaned = ''.join(char for char in result if '\u0600' <= char <= '\u06FF')
            return cleaned if cleaned else text

    def discover_all_lesson_urls(self):
        """اكتشاف جميع روابط الدروس والمحتوى"""
        print("🔍 اكتشاف جميع روابط المحتوى...")
        
        try:
            response = self.session.get(self.base_url + self.main_page, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # البحث عن جميع الروابط المتعلقة بالأرمينية
            all_links = soup.find_all('a', href=True)
            
            lesson_urls = set()
            content_urls = {
                'lessons': set(),
                'vocabulary': set(),
                'flashcards': set(),
                'tests': set(),
                'grammar': set(),
                'phrases': set()
            }
            
            for link in all_links:
                href = link.get('href')
                text = link.get_text(strip=True)
                
                if href and text:
                    full_url = urljoin(self.base_url, href)
                    
                    # تصنيف الروابط حسب النوع
                    if any(pattern in href for pattern in ['/v477', '/lesson', '/درس']):
                        content_urls['lessons'].add(full_url)
                    elif any(pattern in href for pattern in ['/vocabulary', '/مفردات']):
                        content_urls['vocabulary'].add(full_url)
                    elif any(pattern in href for pattern in ['/flashcard', '/بطاقات']):
                        content_urls['flashcards'].add(full_url)
                    elif any(pattern in href for pattern in ['/test', '/quiz', '/اختبار']):
                        content_urls['tests'].add(full_url)
                    elif any(pattern in href for pattern in ['/grammar', '/قواعد']):
                        content_urls['grammar'].add(full_url)
                    elif any(pattern in href for pattern in ['/phrase', '/عبارات']):
                        content_urls['phrases'].add(full_url)
                    
                    # إضافة جميع الروابط المحتملة للدروس
                    if 'armenian' in href.lower() or 'أرمين' in text:
                        lesson_urls.add(full_url)
            
            # إنشاء روابط إضافية للدروس (1-125)
            for i in range(1, 126):
                lesson_url = f"{self.base_url}/ar/v477/{i}"
                content_urls['lessons'].add(lesson_url)
                
                # روابط إضافية للمحتوى
                vocab_url = f"{self.base_url}/ar/vocabulary/armenian/{i}"
                content_urls['vocabulary'].add(vocab_url)
                
                test_url = f"{self.base_url}/ar/test/armenian/{i}"
                content_urls['tests'].add(test_url)
            
            print(f"✅ تم اكتشاف:")
            for content_type, urls in content_urls.items():
                print(f"   📚 {content_type}: {len(urls)} رابط")
            
            return content_urls
            
        except Exception as e:
            print(f"❌ خطأ في اكتشاف الروابط: {e}")
            return {}

    def extract_lesson_content(self, lesson_url, lesson_number):
        """استخراج محتوى درس شامل"""
        try:
            print(f"📖 معالجة الدرس {lesson_number}...")
            
            time.sleep(random.uniform(1, 3))
            response = self.session.get(lesson_url, timeout=30)
            
            if response.status_code != 200:
                print(f"   ❌ خطأ HTTP: {response.status_code}")
                return None
            
            soup = BeautifulSoup(response.content, 'html.parser')
            content = response.text
            
            lesson_data = {
                'lesson_number': lesson_number,
                'title': f"تعلم اللغة الأرمينية - الدرس {lesson_number}",
                'url': lesson_url,
                'vocabulary': [],
                'explanations': [],
                'examples': [],
                'audio_files': [],
                'images': [],
                'exercises': []
            }
            
            # استخراج المفردات
            armenian_texts = re.findall(r'[\u0530-\u058F]+', content)
            phonetic_matches = re.findall(r'\(([A-Za-z][^)]{1,30})\)', content)
            arabic_texts = re.findall(r'[\u0600-\u06FF]+(?:\s+[\u0600-\u06FF]+)*', content)
            
            # تنظيف النطق
            phonetic_texts = []
            for p in phonetic_matches:
                if (not any(x in p.lower() for x in ['px', 'deg', 'width', 'height', 'color', 'font', 'http']) 
                    and len(p) > 1 and len(p) < 35):
                    phonetic_texts.append(p)
            
            # تجميع المفردات
            max_items = min(len(armenian_texts), len(phonetic_texts), len(arabic_texts))
            
            for i in range(max_items):
                armenian = armenian_texts[i] if i < len(armenian_texts) else ""
                phonetic = phonetic_texts[i] if i < len(phonetic_texts) else ""
                arabic = arabic_texts[i] if i < len(arabic_texts) else ""
                
                if armenian and phonetic and arabic:
                    arabic_phonetic = self.generate_arabic_phonetic(phonetic)
                    
                    word_data = {
                        'armenian': armenian,
                        'phonetic': phonetic,
                        'arabic': arabic,
                        'arabic_phonetic': arabic_phonetic,
                        'lesson_number': lesson_number,
                        'audio': re.sub(r'[^a-zA-Z0-9]', '-', phonetic.lower()),
                        'category': self.determine_category(arabic)
                    }
                    
                    lesson_data['vocabulary'].append(word_data)
            
            # استخراج الشروحات
            explanations = soup.find_all(['p', 'div'], class_=re.compile(r'explanation|description|note'))
            for exp in explanations:
                text = exp.get_text(strip=True)
                if text and len(text) > 10:
                    lesson_data['explanations'].append(text)
            
            # استخراج الأمثلة
            examples = soup.find_all(['div', 'span'], class_=re.compile(r'example|sample'))
            for ex in examples:
                text = ex.get_text(strip=True)
                if text and len(text) > 5:
                    lesson_data['examples'].append(text)
            
            # استخراج روابط الصوت
            audio_links = soup.find_all('a', href=re.compile(r'\.(mp3|wav|ogg)'))
            for audio in audio_links:
                lesson_data['audio_files'].append(audio.get('href'))
            
            # استخراج الصور
            images = soup.find_all('img', src=True)
            for img in images:
                src = img.get('src')
                if src and any(ext in src for ext in ['.jpg', '.png', '.gif', '.webp']):
                    lesson_data['images'].append(urljoin(lesson_url, src))
            
            print(f"   ✅ {len(lesson_data['vocabulary'])} كلمة، {len(lesson_data['explanations'])} شرح")
            return lesson_data
            
        except Exception as e:
            print(f"   ❌ خطأ في الدرس {lesson_number}: {e}")
            return None

    def extract_flashcards(self, flashcard_url):
        """استخراج البطاقات التعليمية"""
        try:
            response = self.session.get(flashcard_url, timeout=30)
            if response.status_code != 200:
                return []
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            flashcards = []
            card_elements = soup.find_all(['div', 'section'], class_=re.compile(r'card|flashcard'))
            
            for card in card_elements:
                front_text = ""
                back_text = ""
                
                # البحث عن النص الأمامي والخلفي للبطاقة
                front = card.find(['div', 'span'], class_=re.compile(r'front|question'))
                back = card.find(['div', 'span'], class_=re.compile(r'back|answer'))
                
                if front:
                    front_text = front.get_text(strip=True)
                if back:
                    back_text = back.get_text(strip=True)
                
                if front_text and back_text:
                    flashcards.append({
                        'front': front_text,
                        'back': back_text,
                        'type': 'flashcard'
                    })
            
            return flashcards
            
        except Exception as e:
            print(f"❌ خطأ في استخراج البطاقات: {e}")
            return []

    def extract_tests(self, test_url):
        """استخراج الاختبارات والتمارين"""
        try:
            response = self.session.get(test_url, timeout=30)
            if response.status_code != 200:
                return []
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            tests = []
            
            # البحث عن أسئلة الاختيار من متعدد
            questions = soup.find_all(['div', 'section'], class_=re.compile(r'question|quiz'))
            
            for q in questions:
                question_text = ""
                options = []
                correct_answer = ""
                
                # استخراج نص السؤال
                q_text = q.find(['h3', 'h4', 'p'], class_=re.compile(r'question'))
                if q_text:
                    question_text = q_text.get_text(strip=True)
                
                # استخراج الخيارات
                option_elements = q.find_all(['li', 'div'], class_=re.compile(r'option|choice'))
                for opt in option_elements:
                    opt_text = opt.get_text(strip=True)
                    if opt_text:
                        options.append(opt_text)
                        
                        # البحث عن الإجابة الصحيحة
                        if 'correct' in opt.get('class', []):
                            correct_answer = opt_text
                
                if question_text and options:
                    tests.append({
                        'question': question_text,
                        'options': options,
                        'correct_answer': correct_answer,
                        'type': 'multiple_choice'
                    })
            
            return tests
            
        except Exception as e:
            print(f"❌ خطأ في استخراج الاختبارات: {e}")
            return []

    def determine_category(self, arabic_text):
        """تحديد فئة الكلمة بناءً على المعنى العربي"""
        if not arabic_text:
            return "متنوعة"
        
        text = arabic_text.lower()
        
        categories = {
            'التحيات': ['مرحبا', 'صباح', 'مساء', 'ليلة', 'وداع', 'شكرا'],
            'الأرقام': ['واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة', 'عشرة'],
            'العائلة': ['أم', 'أب', 'ابن', 'ابنة', 'أخ', 'أخت', 'جد', 'جدة'],
            'الألوان': ['أحمر', 'أخضر', 'أزرق', 'أصفر', 'أسود', 'أبيض'],
            'الطعام': ['خبز', 'ماء', 'حليب', 'لحم', 'سمك', 'طعام'],
            'الجسم': ['رأس', 'عين', 'يد', 'قدم', 'وجه'],
            'الوقت': ['يوم', 'ليل', 'ساعة', 'وقت'],
            'الطبيعة': ['شمس', 'قمر', 'مطر', 'شجرة']
        }
        
        for category, keywords in categories.items():
            if any(keyword in text for keyword in keywords):
                return category
        
        return "متنوعة"

    def save_comprehensive_data(self):
        """حفظ جميع البيانات الشاملة"""
        try:
            # إنشاء مجلد للبيانات
            os.makedirs('armenian_complete_data', exist_ok=True)
            
            # حفظ البيانات الرئيسية
            all_data = {
                'lessons': self.lessons,
                'vocabulary': self.vocabulary,
                'explanations': self.explanations,
                'flashcards': self.flashcards,
                'tests': self.tests,
                'grammar': self.grammar,
                'phrases': self.phrases,
                'categories': self.categories,
                'extraction_date': datetime.now().isoformat(),
                'total_lessons': len(self.lessons),
                'total_vocabulary': sum(len(lesson.get('vocabulary', [])) for lesson in self.lessons.values()),
                'total_flashcards': len(self.flashcards),
                'total_tests': len(self.tests)
            }
            
            # حفظ JSON شامل
            with open('armenian_complete_data/complete_armenian_data.json', 'w', encoding='utf-8') as f:
                json.dump(all_data, f, ensure_ascii=False, indent=2)
            
            # حفظ المفردات منفصلة
            all_vocabulary = []
            for lesson in self.lessons.values():
                all_vocabulary.extend(lesson.get('vocabulary', []))
            
            with open('armenian_complete_data/all_vocabulary.json', 'w', encoding='utf-8') as f:
                json.dump(all_vocabulary, f, ensure_ascii=False, indent=2)
            
            # حفظ JavaScript للتطبيق
            js_content = f"""// بيانات شاملة لتعلم الأرمينية
// Comprehensive Armenian Learning Data

window.armenianCompleteData = {json.dumps(all_data, ensure_ascii=False, indent=2)};

console.log('📊 تم تحميل البيانات الشاملة:');
console.log('📚 دروس:', {len(self.lessons)});
console.log('📝 كلمات:', {sum(len(lesson.get('vocabulary', [])) for lesson in self.lessons.values())});
console.log('🃏 بطاقات:', {len(self.flashcards)});
console.log('📋 اختبارات:', {len(self.tests)});
"""
            
            with open('armenian_complete_data/armenian_complete.js', 'w', encoding='utf-8') as f:
                f.write(js_content)
            
            print(f"✅ تم حفظ البيانات الشاملة في مجلد armenian_complete_data")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")

    def run_comprehensive_extraction(self):
        """تشغيل الاستخراج الشامل"""
        print("🚀 بدء الاستخراج الشامل لمحتوى تعلم الأرمينية")
        print("=" * 70)
        
        start_time = datetime.now()
        
        # اكتشاف جميع الروابط
        content_urls = self.discover_all_lesson_urls()
        
        if not content_urls:
            print("❌ لم يتم العثور على أي محتوى")
            return
        
        # استخراج الدروس
        print(f"\n📚 استخراج الدروس...")
        lesson_count = 0
        for lesson_url in list(content_urls.get('lessons', []))[:125]:  # أول 125 درس
            lesson_number = lesson_count + 1
            lesson_data = self.extract_lesson_content(lesson_url, lesson_number)
            
            if lesson_data:
                self.lessons[f"lesson_{lesson_number:03d}"] = lesson_data
                lesson_count += 1
            
            # حفظ التقدم كل 25 درس
            if lesson_count % 25 == 0:
                print(f"💾 تم حفظ التقدم: {lesson_count} درس")
                self.save_comprehensive_data()
        
        # استخراج البطاقات التعليمية
        print(f"\n🃏 استخراج البطاقات التعليمية...")
        for flashcard_url in list(content_urls.get('flashcards', []))[:50]:
            cards = self.extract_flashcards(flashcard_url)
            self.flashcards.update({f"card_{len(self.flashcards)+i}": card for i, card in enumerate(cards)})
        
        # استخراج الاختبارات
        print(f"\n📋 استخراج الاختبارات...")
        for test_url in list(content_urls.get('tests', []))[:50]:
            tests = self.extract_tests(test_url)
            self.tests.update({f"test_{len(self.tests)+i}": test for i, test in enumerate(tests)})
        
        # حفظ البيانات النهائية
        self.save_comprehensive_data()
        
        # إحصائيات نهائية
        end_time = datetime.now()
        duration = end_time - start_time
        
        total_vocabulary = sum(len(lesson.get('vocabulary', [])) for lesson in self.lessons.values())
        
        print("\n" + "=" * 70)
        print("🎉 تم الانتهاء من الاستخراج الشامل!")
        print(f"📊 النتائج النهائية:")
        print(f"   📚 دروس مستخرجة: {len(self.lessons)}")
        print(f"   📝 إجمالي المفردات: {total_vocabulary}")
        print(f"   🃏 بطاقات تعليمية: {len(self.flashcards)}")
        print(f"   📋 اختبارات: {len(self.tests)}")
        print(f"   ⏱️ الوقت المستغرق: {duration}")
        print(f"   📁 البيانات محفوظة في: armenian_complete_data/")

def main():
    """الدالة الرئيسية"""
    extractor = ComprehensiveArmenianExtractor()
    extractor.run_comprehensive_extraction()

if __name__ == "__main__":
    main()
