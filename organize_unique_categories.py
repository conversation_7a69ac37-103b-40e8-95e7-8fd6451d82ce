#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنظيم المفردات الأرمينية بدون تكرار
Organize Armenian Vocabulary WITHOUT Duplicates
"""

import json
import re
from collections import defaultdict

def categorize_unique_vocabulary():
    """تصنيف المفردات بدون تكرار"""
    
    # قراءة البيانات الحالية
    with open('vocabulary_data_complete.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # فئات المفردات
    categories = {
        'التحيات والمجاملات': {
            'icon': '👋',
            'description': 'كلمات الترحيب والتحية والمجاملات',
            'keywords': ['مرحبا', 'صباح', 'مساء', 'ليلة', 'وداع', 'أهلا', 'سلام', 'تحية', 'شكرا'],
            'words': []
        },
        'الأرقام والحساب': {
            'icon': '🔢',
            'description': 'الأرقام والعد والحساب',
            'keywords': ['واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة', 'عشرة'],
            'words': []
        },
        'العائلة والأقارب': {
            'icon': '👨‍👩‍👧‍👦',
            'description': 'أفراد العائلة والأقارب والعلاقات الأسرية',
            'keywords': ['أم', 'أب', 'ابن', 'ابنة', 'أخ', 'أخت', 'جد', 'جدة', 'عم', 'خال', 'عائلة'],
            'words': []
        },
        'الألوان': {
            'icon': '🎨',
            'description': 'الألوان الأساسية والثانوية',
            'keywords': ['أحمر', 'أخضر', 'أزرق', 'أصفر', 'أسود', 'أبيض', 'بني', 'وردي', 'بنفسجي'],
            'words': []
        },
        'الطعام والشراب': {
            'icon': '🍽️',
            'description': 'الأطعمة والمشروبات والوجبات',
            'keywords': ['خبز', 'ماء', 'حليب', 'لحم', 'سمك', 'فاكهة', 'خضار', 'أرز', 'شاي', 'قهوة', 'طعام'],
            'words': []
        },
        'كلمات متنوعة': {
            'icon': '📝',
            'description': 'كلمات متنوعة وعامة',
            'keywords': [],
            'words': []
        }
    }
    
    # جمع جميع الكلمات من جميع الدروس
    all_words = []
    for lesson_key, lesson_words in data.items():
        for word in lesson_words:
            all_words.append(word)
    
    print(f"📊 إجمالي الكلمات قبل إزالة التكرار: {len(all_words)}")
    
    # إزالة التكرار بناءً على النص الأرمني والنطق
    unique_words = {}
    for word in all_words:
        # إنشاء مفتاح فريد للكلمة
        key = f"{word['armenian']}_{word['phonetic']}"
        
        if key not in unique_words:
            unique_words[key] = word
        else:
            # إذا كانت الكلمة موجودة، نحتفظ بالأفضل (الذي له معنى أوضح)
            existing = unique_words[key]
            if len(word.get('arabic', '')) > len(existing.get('arabic', '')):
                unique_words[key] = word
    
    unique_words_list = list(unique_words.values())
    print(f"📊 الكلمات الفريدة بعد إزالة التكرار: {len(unique_words_list)}")
    
    # تصنيف الكلمات الفريدة
    categorized_count = 0
    
    for word in unique_words_list:
        categorized = False
        arabic_meaning = word['arabic'].lower()
        
        # البحث في الفئات
        for category_name, category_info in categories.items():
            if category_name == 'كلمات متنوعة':
                continue
                
            for keyword in category_info['keywords']:
                if keyword in arabic_meaning:
                    category_info['words'].append(word)
                    categorized = True
                    categorized_count += 1
                    break
            
            if categorized:
                break
        
        # إذا لم يتم تصنيف الكلمة، ضعها في "كلمات متنوعة"
        if not categorized:
            categories['كلمات متنوعة']['words'].append(word)
    
    # إضافة كلمات إضافية فريدة لإثراء الفئات
    additional_words = {
        'التحيات والمجاملات': [
            {'armenian': 'Բարի գիշեր', 'phonetic': 'Bari gisher', 'arabic': 'تصبح على خير', 'arabic_phonetic': 'باري غيشير'},
            {'armenian': 'Բարի երեկո', 'phonetic': 'Bari yereko', 'arabic': 'مساء الخير', 'arabic_phonetic': 'باري ييريكو'},
            {'armenian': 'Ցտեսություն', 'phonetic': 'Tsdesutyun', 'arabic': 'وداعاً', 'arabic_phonetic': 'تسديسوتيون'},
            {'armenian': 'Շնորհակալություն', 'phonetic': 'Shnorhakalutyun', 'arabic': 'شكراً', 'arabic_phonetic': 'شنورهاكالوتيون'},
            {'armenian': 'Ներողություն', 'phonetic': 'Neroghutyan', 'arabic': 'عذراً', 'arabic_phonetic': 'نيروغوتيان'},
        ],
        'الأرقام والحساب': [
            {'armenian': 'Չորս', 'phonetic': 'Chors', 'arabic': 'أربعة', 'arabic_phonetic': 'تشورس'},
            {'armenian': 'Հինգ', 'phonetic': 'Hing', 'arabic': 'خمسة', 'arabic_phonetic': 'هينغ'},
            {'armenian': 'Վեց', 'phonetic': 'Vets', 'arabic': 'ستة', 'arabic_phonetic': 'فيتس'},
            {'armenian': 'Յոթ', 'phonetic': 'Yot', 'arabic': 'سبعة', 'arabic_phonetic': 'يوت'},
            {'armenian': 'Ութ', 'phonetic': 'Ut', 'arabic': 'ثمانية', 'arabic_phonetic': 'اوت'},
            {'armenian': 'Ինը', 'phonetic': 'Iny', 'arabic': 'تسعة', 'arabic_phonetic': 'يني'},
            {'armenian': 'Տաս', 'phonetic': 'Das', 'arabic': 'عشرة', 'arabic_phonetic': 'داس'},
            {'armenian': 'Տասնմեկ', 'phonetic': 'Dasnmek', 'arabic': 'أحد عشر', 'arabic_phonetic': 'داسنميك'},
            {'armenian': 'Տասներկու', 'phonetic': 'Dasnerku', 'arabic': 'اثنا عشر', 'arabic_phonetic': 'داسنيركو'},
            {'armenian': 'Քսան', 'phonetic': 'Ksan', 'arabic': 'عشرون', 'arabic_phonetic': 'كسان'},
        ],
        'العائلة والأقارب': [
            {'armenian': 'Եղբայր', 'phonetic': 'Yeghbayr', 'arabic': 'أخ', 'arabic_phonetic': 'ييغبايير'},
            {'armenian': 'Քույր', 'phonetic': 'Khoyr', 'arabic': 'أخت', 'arabic_phonetic': 'خويير'},
            {'armenian': 'Որդի', 'phonetic': 'Vordi', 'arabic': 'ابن', 'arabic_phonetic': 'فوردي'},
            {'armenian': 'Աղջիկ', 'phonetic': 'Aghjik', 'arabic': 'ابنة', 'arabic_phonetic': 'اغجيك'},
            {'armenian': 'Պապ', 'phonetic': 'Pap', 'arabic': 'جد', 'arabic_phonetic': 'باب'},
            {'armenian': 'Տատ', 'phonetic': 'Tat', 'arabic': 'جدة', 'arabic_phonetic': 'تات'},
            {'armenian': 'Ամուսին', 'phonetic': 'Amusin', 'arabic': 'زوج', 'arabic_phonetic': 'اموسين'},
            {'armenian': 'Կին', 'phonetic': 'Kin', 'arabic': 'زوجة', 'arabic_phonetic': 'كين'},
        ],
        'الألوان': [
            {'armenian': 'Կապույտ', 'phonetic': 'Kapuyt', 'arabic': 'أزرق', 'arabic_phonetic': 'كابويت'},
            {'armenian': 'Դեղին', 'phonetic': 'Deghin', 'arabic': 'أصفر', 'arabic_phonetic': 'ديغيين'},
            {'armenian': 'Սև', 'phonetic': 'Sev', 'arabic': 'أسود', 'arabic_phonetic': 'سيف'},
            {'armenian': 'Սպիտակ', 'phonetic': 'Spitak', 'arabic': 'أبيض', 'arabic_phonetic': 'سبيتاك'},
            {'armenian': 'Մանուշակագույն', 'phonetic': 'Manushakaguyn', 'arabic': 'بنفسجي', 'arabic_phonetic': 'مانوشاكاغوين'},
            {'armenian': 'Վարդագույն', 'phonetic': 'Vardaguyn', 'arabic': 'وردي', 'arabic_phonetic': 'فارداغوين'},
        ],
        'الطعام والشراب': [
            {'armenian': 'Կաթ', 'phonetic': 'Kat', 'arabic': 'حليب', 'arabic_phonetic': 'كات'},
            {'armenian': 'Միս', 'phonetic': 'Mis', 'arabic': 'لحم', 'arabic_phonetic': 'ميس'},
            {'armenian': 'Ձուկ', 'phonetic': 'Dzuk', 'arabic': 'سمك', 'arabic_phonetic': 'دزوك'},
            {'armenian': 'Բանջարեղեն', 'phonetic': 'Banjareghen', 'arabic': 'خضار', 'arabic_phonetic': 'بانجاريغين'},
            {'armenian': 'Մրգեր', 'phonetic': 'Mrger', 'arabic': 'فواكه', 'arabic_phonetic': 'مرغير'},
            {'armenian': 'Թեյ', 'phonetic': 'Tey', 'arabic': 'شاي', 'arabic_phonetic': 'تيي'},
            {'armenian': 'Սուրճ', 'phonetic': 'Surch', 'arabic': 'قهوة', 'arabic_phonetic': 'سورتش'},
        ]
    }
    
    # إضافة الكلمات الإضافية (فقط إذا لم تكن موجودة)
    for category_name, words in additional_words.items():
        if category_name in categories:
            for word in words:
                # التحقق من عدم وجود الكلمة مسبقاً
                key = f"{word['armenian']}_{word['phonetic']}"
                if key not in unique_words:
                    # إضافة معلومات إضافية
                    word.update({
                        'meaning': f'كلمة من فئة {category_name}',
                        'lesson': f'فئة {category_name}',
                        'audio': re.sub(r'[^a-zA-Z0-9]', '-', word['phonetic'].lower())
                    })
                    categories[category_name]['words'].append(word)
    
    # إزالة الفئات الفارغة
    categories = {k: v for k, v in categories.items() if v['words']}
    
    # حفظ البيانات المصنفة بدون تكرار
    with open('vocabulary_categories_unique.json', 'w', encoding='utf-8') as f:
        json.dump(categories, f, ensure_ascii=False, indent=2)
    
    # إنشاء ملف JavaScript
    js_content = f"""// المفردات الأرمينية الفريدة مصنفة حسب الفئات
// Unique Armenian Vocabulary Organized by Categories

window.vocabularyCategories = {json.dumps(categories, ensure_ascii=False, indent=2)};

// إحصائيات دقيقة
window.categoryStats = {{
"""
    
    total_words = 0
    for category_name, category_info in categories.items():
        word_count = len(category_info['words'])
        total_words += word_count
        js_content += f'  "{category_name}": {word_count},\n'
    
    js_content += f"""  "total": {total_words}
}};

console.log('📊 تم تحميل', {total_words}, 'كلمة فريدة في', {len(categories)}, 'فئة');
"""
    
    with open('vocabulary_categories_unique.js', 'w', encoding='utf-8') as f:
        f.write(js_content)
    
    # طباعة الإحصائيات
    print("\n📊 تم تصنيف المفردات الفريدة بنجاح!")
    print("=" * 60)
    
    for category_name, category_info in categories.items():
        word_count = len(category_info['words'])
        print(f"{category_info['icon']} {category_name}: {word_count} كلمة فريدة")
    
    print(f"\n✅ إجمالي الكلمات الفريدة: {total_words}")
    print(f"📁 عدد الفئات: {len(categories)}")
    print(f"🎯 تم إزالة التكرار بنجاح")
    print(f"💾 تم حفظ البيانات في:")
    print(f"   - vocabulary_categories_unique.json")
    print(f"   - vocabulary_categories_unique.js")
    
    # عرض عينة من كل فئة
    print(f"\n📋 عينة من الكلمات:")
    for category_name, category_info in categories.items():
        if category_info['words']:
            sample_word = category_info['words'][0]
            print(f"   {category_info['icon']} {sample_word['armenian']} ({sample_word['phonetic']}) = {sample_word['arabic']}")

if __name__ == "__main__":
    categorize_unique_vocabulary()
