@echo off
chcp 65001 >nul
cd /d "%~dp0"
echo.
echo ========================================
echo    🧪 اختبار شامل للنظام
echo    Complete System Test
echo ========================================
echo.

echo 📍 المجلد الحالي: %CD%
echo.

echo 🔍 فحص Python...
py --version >nul 2>&1
if errorlevel 1 (
    python --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Python غير مثبت
        goto :python_missing
    ) else (
        echo ✅ Python متوفر (python)
        set PYTHON_CMD=python
    )
) else (
    echo ✅ Python متوفر (py)
    set PYTHON_CMD=py
)

echo.
echo 📦 فحص المكتبات...
%PYTHON_CMD% -c "import requests; print('✅ requests')" 2>nul || echo ❌ requests مفقود
%PYTHON_CMD% -c "import bs4; print('✅ beautifulsoup4')" 2>nul || echo ❌ beautifulsoup4 مفقود
%PYTHON_CMD% -c "import selenium; print('✅ selenium')" 2>nul || echo ❌ selenium مفقود

echo.
echo 📁 فحص الملفات...
if exist index.html (echo ✅ index.html) else (echo ❌ index.html مفقود)
if exist script.js (echo ✅ script.js) else (echo ❌ script.js مفقود)
if exist style.css (echo ✅ style.css) else (echo ❌ style.css مفقود)
if exist advanced_scraper.py (echo ✅ advanced_scraper.py) else (echo ❌ advanced_scraper.py مفقود)
if exist simple_scraper.py (echo ✅ simple_scraper.py) else (echo ❌ simple_scraper.py مفقود)

echo.
echo 🌐 فحص الإنترنت...
%PYTHON_CMD% -c "import requests; r=requests.get('https://google.com', timeout=5); print('✅ الإنترنت يعمل')" 2>nul || echo ❌ مشكلة في الإنترنت

echo.
echo 📊 فحص البيانات المستخرجة...
if exist vocabulary_data_complete.json (
    echo ✅ البيانات موجودة
    %PYTHON_CMD% -c "import json; data=json.load(open('vocabulary_data_complete.json', 'r', encoding='utf-8')); print(f'📚 {len(data)} درس، {sum(len(words) for words in data.values())} كلمة')" 2>nul
) else (
    echo ❌ لا توجد بيانات مستخرجة
)

echo.
echo 🎯 النتيجة:
if exist vocabulary_data_complete.json (
    echo ✅ النظام جاهز! يمكنك فتح التطبيق
    echo 💡 لفتح التطبيق: انقر على open_app_only.bat
) else (
    echo ⚠️ تحتاج لاستخراج البيانات أولاً
    echo 💡 للاستخراج: انقر على run_extraction_simple.bat
)

echo.
echo 🚀 خيارات التشغيل:
echo    1. open_app_only.bat - فتح التطبيق فقط
echo    2. run_extraction_simple.bat - استخراج البيانات
echo    3. demo_extracted_data.html - عرض البيانات مباشرة
echo.

goto :end

:python_missing
echo.
echo ❌ Python غير مثبت!
echo.
echo 💡 لتثبيت Python:
echo    1. اذهب إلى: https://python.org/downloads
echo    2. حمل أحدث إصدار
echo    3. تأكد من تحديد "Add Python to PATH"
echo    4. أعد تشغيل الكمبيوتر
echo.

:end
pause
