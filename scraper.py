#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لاستخراج المفردات الأرمينية من موقع LingoHut
Armenian Vocabulary Scraper for LingoHut
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import re
from urllib.parse import urljoin, quote
import os

class ArmenianVocabularyScraper:
    def __init__(self):
        self.base_url = "https://www.lingohut.com"
        self.main_page = "/ar/l76/%D8%AA%D8%B9%D9%84%D9%85-%D8%A7%D9%84%D9%84%D8%BA%D8%A9-%D8%A7%D9%84%D8%A3%D8%B1%D9%85%D9%8A%D9%86%D9%8A%D8%A9"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        self.vocabulary_data = {}
        self.lesson_urls = []

    def get_lesson_urls(self):
        """استخراج روابط جميع الدروس من الصفحة الرئيسية"""
        try:
            print("🔍 جاري البحث عن روابط الدروس...")
            response = self.session.get(self.base_url + self.main_page)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # البحث عن روابط الدروس
            lesson_links = soup.find_all('a', href=True)
            
            for link in lesson_links:
                href = link.get('href')
                if href and '/ar/l76/' in href and href != self.main_page:
                    full_url = urljoin(self.base_url, href)
                    lesson_title = link.get_text(strip=True)
                    
                    if lesson_title and 'الدرس' in lesson_title:
                        self.lesson_urls.append({
                            'url': full_url,
                            'title': lesson_title,
                            'href': href
                        })
            
            print(f"✅ تم العثور على {len(self.lesson_urls)} درس")
            return self.lesson_urls
            
        except Exception as e:
            print(f"❌ خطأ في استخراج روابط الدروس: {e}")
            return []

    def extract_vocabulary_from_lesson(self, lesson_url, lesson_title):
        """استخراج المفردات من درس واحد"""
        try:
            print(f"📖 جاري استخراج المفردات من: {lesson_title}")
            response = self.session.get(lesson_url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            vocabulary = []
            
            # البحث عن بطاقات المفردات
            # هذا يحتاج تحديث حسب هيكل الموقع الفعلي
            vocab_cards = soup.find_all('div', class_=['vocab-card', 'word-card', 'flashcard'])
            
            if not vocab_cards:
                # محاولة البحث بطرق أخرى
                vocab_cards = soup.find_all('div', attrs={'data-word': True})
            
            for card in vocab_cards:
                try:
                    # استخراج النص الأرمني
                    armenian_text = self.extract_armenian_text(card)
                    
                    # استخراج النطق اللاتيني
                    phonetic = self.extract_phonetic(card)
                    
                    # استخراج الترجمة العربية
                    arabic_meaning = self.extract_arabic_meaning(card)
                    
                    if armenian_text and phonetic and arabic_meaning:
                        vocab_item = {
                            'armenian': armenian_text,
                            'phonetic': phonetic,
                            'arabic': arabic_meaning,
                            'lesson': lesson_title,
                            'audio': self.generate_audio_filename(phonetic)
                        }
                        vocabulary.append(vocab_item)
                        
                except Exception as e:
                    print(f"⚠️ خطأ في استخراج بطاقة مفردات: {e}")
                    continue
            
            print(f"✅ تم استخراج {len(vocabulary)} كلمة من {lesson_title}")
            return vocabulary
            
        except Exception as e:
            print(f"❌ خطأ في استخراج المفردات من {lesson_title}: {e}")
            return []

    def extract_armenian_text(self, card):
        """استخراج النص الأرمني من البطاقة"""
        # البحث عن النص الأرمني بطرق مختلفة
        armenian_selectors = [
            '.armenian-text',
            '.word-armenian',
            '.foreign-word',
            '[lang="hy"]',
            '.word-text'
        ]
        
        for selector in armenian_selectors:
            element = card.select_one(selector)
            if element:
                text = element.get_text(strip=True)
                if self.is_armenian_text(text):
                    return text
        
        # البحث في جميع النصوص
        all_text = card.get_text()
        armenian_pattern = r'[\u0530-\u058F\u2010-\u2015]+'
        matches = re.findall(armenian_pattern, all_text)
        if matches:
            return matches[0]
        
        return None

    def extract_phonetic(self, card):
        """استخراج النطق اللاتيني من البطاقة"""
        phonetic_selectors = [
            '.phonetic',
            '.pronunciation',
            '.word-phonetic',
            '.transliteration'
        ]
        
        for selector in phonetic_selectors:
            element = card.select_one(selector)
            if element:
                text = element.get_text(strip=True)
                # إزالة الأقواس إن وجدت
                text = re.sub(r'[()]', '', text).strip()
                if text and self.is_latin_text(text):
                    return text
        
        # البحث عن النص بين أقواس
        all_text = card.get_text()
        phonetic_pattern = r'\(([^)]+)\)'
        matches = re.findall(phonetic_pattern, all_text)
        for match in matches:
            if self.is_latin_text(match):
                return match.strip()
        
        return None

    def extract_arabic_meaning(self, card):
        """استخراج الترجمة العربية من البطاقة"""
        arabic_selectors = [
            '.arabic-meaning',
            '.translation',
            '.word-meaning',
            '.native-word'
        ]
        
        for selector in arabic_selectors:
            element = card.select_one(selector)
            if element:
                text = element.get_text(strip=True)
                if self.is_arabic_text(text):
                    return text
        
        # البحث في جميع النصوص
        all_text = card.get_text()
        arabic_pattern = r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+'
        matches = re.findall(arabic_pattern, all_text)
        if matches:
            return matches[0]
        
        return None

    def is_armenian_text(self, text):
        """التحقق من أن النص أرمني"""
        armenian_chars = re.findall(r'[\u0530-\u058F]', text)
        return len(armenian_chars) > 0

    def is_latin_text(self, text):
        """التحقق من أن النص لاتيني"""
        latin_chars = re.findall(r'[a-zA-Z]', text)
        return len(latin_chars) > len(text) * 0.5

    def is_arabic_text(self, text):
        """التحقق من أن النص عربي"""
        arabic_chars = re.findall(r'[\u0600-\u06FF]', text)
        return len(arabic_chars) > 0

    def generate_audio_filename(self, phonetic):
        """إنشاء اسم ملف صوتي من النطق"""
        if not phonetic:
            return "unknown"
        
        # تنظيف النص وتحويله لاسم ملف
        filename = re.sub(r'[^a-zA-Z0-9]', '-', phonetic.lower())
        filename = re.sub(r'-+', '-', filename)
        filename = filename.strip('-')
        return filename

    def save_to_javascript(self, output_file='vocabulary_data.js'):
        """حفظ البيانات في ملف JavaScript"""
        try:
            js_content = "// بيانات المفردات الأرمينية المستخرجة تلقائياً\n"
            js_content += "// Armenian Vocabulary Data - Auto Extracted\n\n"
            js_content += "const vocabularyData = "
            js_content += json.dumps(self.vocabulary_data, ensure_ascii=False, indent=2)
            js_content += ";\n\n"
            js_content += "// تصدير البيانات للاستخدام في التطبيق\n"
            js_content += "if (typeof module !== 'undefined' && module.exports) {\n"
            js_content += "    module.exports = vocabularyData;\n"
            js_content += "}\n"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(js_content)
            
            print(f"✅ تم حفظ البيانات في {output_file}")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")

    def save_to_json(self, output_file='vocabulary_data.json'):
        """حفظ البيانات في ملف JSON"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.vocabulary_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ تم حفظ البيانات في {output_file}")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")

    def save_to_text(self, output_file='armenian_vocabulary_complete.txt'):
        """حفظ البيانات في ملف نصي"""
        try:
            content = "مفردات اللغة الأرمينية - Armenian Vocabulary\n"
            content += "=" * 50 + "\n\n"
            content += f"تم استخراج البيانات تلقائياً من موقع LingoHut\n"
            content += f"تاريخ الاستخراج: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            
            total_words = 0
            for category, words in self.vocabulary_data.items():
                if words:
                    lesson_name = words[0].get('lesson', category)
                    content += f"{lesson_name}\n"
                    content += "-" * len(lesson_name) + "\n"
                    
                    for i, word in enumerate(words, 1):
                        content += f"{i}. {word['phonetic']} - {word['arabic']}\n"
                        total_words += 1
                    
                    content += "\n"
            
            content += f"\nإجمالي الكلمات: {total_words}\n"
            content += f"عدد الدروس: {len(self.vocabulary_data)}\n"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ تم حفظ البيانات في {output_file}")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")

    def run_scraper(self):
        """تشغيل عملية الاستخراج الكاملة"""
        print("🚀 بدء عملية استخراج المفردات الأرمينية...")
        print("=" * 50)
        
        # الحصول على روابط الدروس
        lesson_urls = self.get_lesson_urls()
        
        if not lesson_urls:
            print("❌ لم يتم العثور على أي دروس")
            return
        
        # استخراج المفردات من كل درس
        for lesson in lesson_urls[:5]:  # البدء بأول 5 دروس للاختبار
            try:
                vocabulary = self.extract_vocabulary_from_lesson(
                    lesson['url'], 
                    lesson['title']
                )
                
                if vocabulary:
                    # إنشاء مفتاح للدرس
                    lesson_key = self.generate_lesson_key(lesson['title'])
                    self.vocabulary_data[lesson_key] = vocabulary
                
                # توقف قصير بين الطلبات
                time.sleep(2)
                
            except Exception as e:
                print(f"❌ خطأ في معالجة الدرس {lesson['title']}: {e}")
                continue
        
        # حفظ البيانات
        if self.vocabulary_data:
            print("\n💾 جاري حفظ البيانات...")
            self.save_to_javascript('vocabulary_data.js')
            self.save_to_json('vocabulary_data.json')
            self.save_to_text('armenian_vocabulary_complete.txt')
            
            total_words = sum(len(words) for words in self.vocabulary_data.values())
            print(f"\n🎉 تم الانتهاء! تم استخراج {total_words} كلمة من {len(self.vocabulary_data)} درس")
        else:
            print("❌ لم يتم استخراج أي مفردات")

    def generate_lesson_key(self, lesson_title):
        """إنشاء مفتاح للدرس"""
        # استخراج رقم الدرس والموضوع
        lesson_match = re.search(r'الدرس\s*(\d+)', lesson_title)
        if lesson_match:
            lesson_num = lesson_match.group(1)
            topic = lesson_title.split(':')[-1].strip() if ':' in lesson_title else f"lesson_{lesson_num}"
            return f"lesson_{lesson_num}_{topic}"
        else:
            # إنشاء مفتاح من العنوان
            key = re.sub(r'[^a-zA-Z0-9\u0600-\u06FF]', '_', lesson_title)
            return key.lower()

def main():
    """الدالة الرئيسية"""
    scraper = ArmenianVocabularyScraper()
    scraper.run_scraper()

if __name__ == "__main__":
    main()