#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تشخيص النظام
System Diagnosis Tool
"""

import sys
import subprocess
import os
import importlib.util

def check_python():
    """فحص Python"""
    print("🐍 فحص Python:")
    print(f"   الإصدار: {sys.version}")
    print(f"   المسار: {sys.executable}")
    print(f"   المنصة: {sys.platform}")
    print()

def check_pip():
    """فحص pip"""
    print("📦 فحص pip:")
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"   ✅ pip متوفر: {result.stdout.strip()}")
        else:
            print(f"   ❌ مشكلة في pip: {result.stderr}")
    except Exception as e:
        print(f"   ❌ خطأ في فحص pip: {e}")
    print()

def check_required_packages():
    """فحص المكتبات المطلوبة"""
    print("📚 فحص المكتبات المطلوبة:")
    
    required_packages = [
        'requests',
        'beautifulsoup4',
        'selenium',
        'webdriver_manager'
    ]
    
    for package in required_packages:
        try:
            if package == 'beautifulsoup4':
                import bs4
                print(f"   ✅ {package} (bs4) مثبت")
            elif package == 'webdriver_manager':
                import webdriver_manager
                print(f"   ✅ {package} مثبت")
            else:
                __import__(package)
                print(f"   ✅ {package} مثبت")
        except ImportError:
            print(f"   ❌ {package} غير مثبت")
    print()

def check_internet():
    """فحص الاتصال بالإنترنت"""
    print("🌐 فحص الاتصال بالإنترنت:")
    try:
        import requests
        response = requests.get('https://www.google.com', timeout=10)
        if response.status_code == 200:
            print("   ✅ الاتصال بالإنترنت يعمل")
        else:
            print(f"   ⚠️ مشكلة في الاتصال: كود {response.status_code}")
    except Exception as e:
        print(f"   ❌ لا يوجد اتصال بالإنترنت: {e}")
    print()

def check_target_website():
    """فحص الموقع المستهدف"""
    print("🎯 فحص الموقع المستهدف:")
    try:
        import requests
        url = "https://www.lingohut.com/ar/l76/%D8%AA%D8%B9%D9%84%D9%85-%D8%A7%D9%84%D9%84%D8%BA%D8%A9-%D8%A7%D9%84%D8%A3%D8%B1%D9%85%D9%8A%D9%86%D9%8A%D8%A9"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=15)
        if response.status_code == 200:
            print("   ✅ الموقع متاح ويمكن الوصول إليه")
            
            # فحص محتوى الصفحة
            if 'أرمينية' in response.text or 'Armenian' in response.text:
                print("   ✅ المحتوى صحيح")
            else:
                print("   ⚠️ المحتوى قد يكون مختلف")
        else:
            print(f"   ❌ مشكلة في الوصول للموقع: كود {response.status_code}")
    except Exception as e:
        print(f"   ❌ خطأ في الوصول للموقع: {e}")
    print()

def check_files():
    """فحص الملفات المطلوبة"""
    print("📁 فحص الملفات:")
    
    required_files = [
        'advanced_scraper.py',
        'simple_scraper.py',
        'index.html',
        'script.js',
        'style.css'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"   ✅ {file} ({size:,} بايت)")
        else:
            print(f"   ❌ {file} غير موجود")
    
    print()

def check_chrome():
    """فحص Chrome/Chromium"""
    print("🌐 فحص متصفح Chrome:")
    try:
        from selenium import webdriver
        from webdriver_manager.chrome import ChromeDriverManager
        
        print("   ✅ selenium متوفر")
        print("   ✅ webdriver_manager متوفر")
        
        # محاولة إنشاء driver
        try:
            options = webdriver.ChromeOptions()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            
            driver = webdriver.Chrome(
                service=webdriver.chrome.service.Service(ChromeDriverManager().install()),
                options=options
            )
            driver.quit()
            print("   ✅ ChromeDriver يعمل بشكل صحيح")
        except Exception as e:
            print(f"   ❌ مشكلة في ChromeDriver: {e}")
            
    except ImportError as e:
        print(f"   ❌ مكتبات Selenium غير متوفرة: {e}")
    print()

def install_missing_packages():
    """تثبيت المكتبات المفقودة"""
    print("🔧 تثبيت المكتبات المفقودة:")
    
    packages = ['requests', 'beautifulsoup4', 'selenium', 'webdriver-manager']
    
    for package in packages:
        try:
            print(f"   📦 تثبيت {package}...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', package, '--quiet'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print(f"   ✅ تم تثبيت {package}")
            else:
                print(f"   ❌ فشل تثبيت {package}: {result.stderr}")
        except Exception as e:
            print(f"   ❌ خطأ في تثبيت {package}: {e}")
    print()

def main():
    """الدالة الرئيسية"""
    print("🔍 أداة تشخيص النظام لتطبيق تعلم الأرمينية")
    print("=" * 60)
    print()
    
    # فحص النظام
    check_python()
    check_pip()
    check_required_packages()
    check_internet()
    check_target_website()
    check_files()
    check_chrome()
    
    # اقتراح الحلول
    print("💡 اقتراحات:")
    print("   1. إذا كانت المكتبات مفقودة، شغل: pip install requests beautifulsoup4 selenium webdriver-manager")
    print("   2. إذا كان الإنترنت لا يعمل، تحقق من الاتصال")
    print("   3. إذا كان الموقع غير متاح، جرب لاحقاً")
    print("   4. إذا كان Chrome مفقود، ثبت Google Chrome")
    print()
    
    # خيار التثبيت التلقائي
    try:
        choice = input("هل تريد محاولة تثبيت المكتبات المفقودة تلقائياً؟ (y/n): ")
        if choice.lower() == 'y':
            install_missing_packages()
            print("✅ تم الانتهاء من محاولة التثبيت")
    except KeyboardInterrupt:
        print("\n🛑 تم إلغاء العملية")

if __name__ == "__main__":
    main()
