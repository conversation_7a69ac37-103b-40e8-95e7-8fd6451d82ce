#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مستخرج متقدم للحصول على جميع الكلمات الأرمينية من LingoHut
Advanced Armenian Words Scraper from LingoHut
"""

import requests
from bs4 import BeautifulSoup
import json
import re
import time
from datetime import datetime
import random
from urllib.parse import urljoin
import os

class AdvancedArmenianScraper:
    def __init__(self):
        self.base_url = "https://www.lingohut.com"
        self.session = requests.Session()
        
        # Headers متقدمة
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.lingohut.com/',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Upgrade-Insecure-Requests': '1'
        }
        self.session.headers.update(self.headers)
        
        # بيانات شاملة
        self.all_vocabulary = []
        self.categories_data = {}
        self.lessons_data = {}
        self.total_extracted = 0

    def generate_arabic_phonetic(self, phonetic):
        """تحويل النطق اللاتيني إلى نطق عربي"""
        if not phonetic:
            return ""

        text = phonetic.strip()
        
        # حالات خاصة
        special_cases = {
            'Meg': 'ميك', 'Mek': 'ميك', 'Chors': 'تشورس',
            'Yerku': 'ييركو', 'Yereq': 'ييريك', 'Hing': 'هينغ',
            'Vets': 'فيتس', 'Yot': 'يوت', 'Ut': 'اوت', 'Iny': 'يني', 'Das': 'داس',
            'Bari luys': 'باري لويس', 'Bari yereko': 'باري ييريكو',
            'Bari gisher': 'باري غيشير', 'Barev': 'باريف',
            'Mayr': 'مايير', 'Hayr': 'هايير', 'Yeghbayr': 'ييغبايير',
            'Khoyr': 'خويير', 'Shnorhakalutyun': 'شنورهاكالوتيون'
        }
        
        if text in special_cases:
            return special_cases[text]
        
        # تحويل عام
        replacements = {
            'shn': 'شن', 'tch': 'تش', 'ch': 'تش', 'sh': 'ش', 'th': 'ث',
            'gh': 'غ', 'kh': 'خ', 'zh': 'ج', 'ng': 'نغ', 'nk': 'نك',
            'a': 'ا', 'e': 'ي', 'i': 'ي', 'o': 'و', 'u': 'و',
            'b': 'ب', 'p': 'ب', 'f': 'ف', 'v': 'ف', 't': 'ت', 'd': 'د',
            's': 'س', 'z': 'ز', 'k': 'ك', 'g': 'ج', 'h': 'ه', 'j': 'ج',
            'l': 'ل', 'r': 'ر', 'm': 'م', 'n': 'ن', 'w': 'و', 'y': 'ي'
        }
        
        result = text
        for latin, arabic in sorted(replacements.items(), key=lambda x: len(x[0]), reverse=True):
            result = result.replace(latin, arabic)
        
        cleaned = ''.join(char for char in result if '\u0600' <= char <= '\u06FF' or char == ' ')
        return cleaned if cleaned else text

    def extract_from_multiple_sources(self):
        """استخراج من مصادر متعددة"""
        print("🔍 البحث في مصادر متعددة...")
        
        # قائمة شاملة من الروابط المحتملة
        source_urls = [
            "/ar/l76/%D8%AA%D8%B9%D9%84%D9%85-%D8%A7%D9%84%D9%84%D8%BA%D8%A9-%D8%A7%D9%84%D8%A3%D8%B1%D9%85%D9%8A%D9%86%D9%8A%D8%A9",
            "/ar/learn-armenian",
            "/learn-armenian",
            "/armenian-lessons",
            "/armenian-vocabulary"
        ]
        
        # روابط الدروس المباشرة
        for i in range(1, 126):
            source_urls.extend([
                f"/ar/v477/{i}",
                f"/ar/lesson/armenian/{i}",
                f"/lesson/armenian/{i}",
                f"/armenian/lesson/{i}"
            ])
        
        all_words = set()  # استخدام set لتجنب التكرار
        
        for i, url in enumerate(source_urls):
            try:
                print(f"📖 [{i+1}/{len(source_urls)}] معالجة: {url}")
                
                full_url = self.base_url + url
                response = self.session.get(full_url, timeout=20)
                
                if response.status_code == 200:
                    words = self.extract_words_from_page(response.text, url)
                    if words:
                        all_words.update(words)
                        print(f"   ✅ استخرج {len(words)} كلمة")
                    else:
                        print(f"   ⚠️ لا توجد كلمات")
                else:
                    print(f"   ❌ خطأ: {response.status_code}")
                
                # توقف عشوائي
                time.sleep(random.uniform(0.5, 2))
                
                # حفظ التقدم كل 50 رابط
                if (i + 1) % 50 == 0:
                    self.save_progress(list(all_words))
                    print(f"💾 تم حفظ التقدم: {len(all_words)} كلمة فريدة")
                
            except Exception as e:
                print(f"   ❌ خطأ في {url}: {e}")
                continue
        
        return list(all_words)

    def extract_words_from_page(self, html_content, source_url):
        """استخراج الكلمات من صفحة واحدة"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            words = []
            
            # استخراج النصوص الأرمينية
            armenian_pattern = r'[\u0530-\u058F]+'
            armenian_texts = re.findall(armenian_pattern, html_content)
            
            # استخراج النطق في الأقواس
            phonetic_pattern = r'\(([A-Za-z][^)]{1,40})\)'
            phonetic_matches = re.findall(phonetic_pattern, html_content)
            
            # تنظيف النطق
            phonetic_texts = []
            for p in phonetic_matches:
                if (not any(x in p.lower() for x in ['px', 'deg', 'width', 'height', 'color', 'font', 'http', 'www']) 
                    and len(p) > 1 and len(p) < 40 and not p.isdigit()):
                    phonetic_texts.append(p)
            
            # استخراج النصوص العربية
            arabic_pattern = r'[\u0600-\u06FF]+(?:\s+[\u0600-\u06FF]+)*'
            arabic_texts = re.findall(arabic_pattern, html_content)
            
            # تنظيف النصوص العربية
            arabic_clean = []
            for ar in arabic_texts:
                ar = ar.strip()
                if len(ar) > 1 and len(ar) < 50 and not ar.isdigit():
                    arabic_clean.append(ar)
            
            # تجميع البيانات
            max_items = min(len(armenian_texts), len(phonetic_texts))
            
            for i in range(max_items):
                armenian = armenian_texts[i]
                phonetic = phonetic_texts[i] if i < len(phonetic_texts) else ""
                arabic = arabic_clean[i] if i < len(arabic_clean) else "غير متوفر"
                
                if armenian and phonetic:
                    # إنشاء مفتاح فريد
                    unique_key = f"{armenian}_{phonetic}"
                    
                    arabic_phonetic = self.generate_arabic_phonetic(phonetic)
                    category = self.determine_category(arabic)
                    
                    word_tuple = (
                        armenian,
                        phonetic,
                        arabic,
                        arabic_phonetic,
                        category,
                        source_url
                    )
                    
                    words.append(word_tuple)
            
            # استخراج إضافي من العناصر المهيكلة
            structured_words = self.extract_structured_content(soup, source_url)
            words.extend(structured_words)
            
            return words
            
        except Exception as e:
            print(f"❌ خطأ في استخراج الكلمات: {e}")
            return []

    def extract_structured_content(self, soup, source_url):
        """استخراج المحتوى المهيكل من العناصر HTML"""
        words = []
        
        try:
            # البحث في الجداول
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        armenian_cell = cells[0].get_text(strip=True)
                        arabic_cell = cells[-1].get_text(strip=True)
                        
                        # البحث عن النطق في الخلايا الوسطى
                        phonetic_cell = ""
                        if len(cells) >= 3:
                            phonetic_cell = cells[1].get_text(strip=True)
                        
                        if re.match(r'[\u0530-\u058F]+', armenian_cell) and arabic_cell:
                            if not phonetic_cell:
                                phonetic_cell = armenian_cell  # استخدام النص الأرمني كنطق مؤقت
                            
                            arabic_phonetic = self.generate_arabic_phonetic(phonetic_cell)
                            category = self.determine_category(arabic_cell)
                            
                            word_tuple = (
                                armenian_cell,
                                phonetic_cell,
                                arabic_cell,
                                arabic_phonetic,
                                category,
                                source_url
                            )
                            words.append(word_tuple)
            
            # البحث في القوائم
            lists = soup.find_all(['ul', 'ol'])
            for lst in lists:
                items = lst.find_all('li')
                for item in items:
                    text = item.get_text(strip=True)
                    
                    # البحث عن نمط: أرمني (نطق) = عربي
                    pattern = r'([\u0530-\u058F]+)\s*\(([^)]+)\)\s*[=:]\s*([\u0600-\u06FF\s]+)'
                    match = re.search(pattern, text)
                    
                    if match:
                        armenian, phonetic, arabic = match.groups()
                        arabic_phonetic = self.generate_arabic_phonetic(phonetic)
                        category = self.determine_category(arabic)
                        
                        word_tuple = (
                            armenian.strip(),
                            phonetic.strip(),
                            arabic.strip(),
                            arabic_phonetic,
                            category,
                            source_url
                        )
                        words.append(word_tuple)
            
        except Exception as e:
            print(f"❌ خطأ في استخراج المحتوى المهيكل: {e}")
        
        return words

    def determine_category(self, arabic_text):
        """تحديد فئة الكلمة"""
        if not arabic_text:
            return "متنوعة"
        
        text = arabic_text.lower()
        
        categories = {
            'التحيات والمجاملات': ['مرحبا', 'صباح', 'مساء', 'ليلة', 'وداع', 'شكرا', 'عفوا', 'أهلا', 'سلام'],
            'الأرقام والحساب': ['واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة', 'عشرة', 'عشرون', 'مئة'],
            'العائلة والأقارب': ['أم', 'أب', 'ابن', 'ابنة', 'أخ', 'أخت', 'جد', 'جدة', 'عم', 'خال', 'زوج', 'زوجة', 'عائلة'],
            'الألوان': ['أحمر', 'أخضر', 'أزرق', 'أصفر', 'أسود', 'أبيض', 'بني', 'وردي', 'بنفسجي', 'رمادي'],
            'الطعام والشراب': ['خبز', 'ماء', 'حليب', 'لحم', 'سمك', 'فاكهة', 'خضار', 'أرز', 'شاي', 'قهوة', 'طعام', 'شراب'],
            'الجسم وأجزاؤه': ['رأس', 'عين', 'أنف', 'فم', 'يد', 'قدم', 'أذن', 'شعر', 'وجه', 'جسم', 'ذراع', 'ساق'],
            'الملابس': ['قميص', 'بنطال', 'فستان', 'حذاء', 'قبعة', 'جوارب', 'معطف', 'ملابس'],
            'البيت والمنزل': ['بيت', 'غرفة', 'مطبخ', 'حمام', 'سرير', 'كرسي', 'طاولة', 'نافذة', 'باب', 'منزل'],
            'الطبيعة والطقس': ['شمس', 'قمر', 'نجم', 'مطر', 'ثلج', 'ريح', 'شجرة', 'زهرة', 'جبل', 'بحر', 'طبيعة'],
            'الحيوانات': ['كلب', 'قطة', 'حصان', 'بقرة', 'خروف', 'طائر', 'سمكة', 'حيوان'],
            'الوقت والزمن': ['ساعة', 'دقيقة', 'يوم', 'أسبوع', 'شهر', 'سنة', 'صباح', 'ظهر', 'مساء', 'ليل', 'وقت'],
            'المشاعر': ['سعيد', 'حزين', 'غاضب', 'خائف', 'متعب', 'جائع', 'عطشان', 'حب', 'فرح'],
            'الأفعال': ['يأكل', 'يشرب', 'ينام', 'يمشي', 'يجري', 'يقرأ', 'يكتب', 'يتكلم', 'يذهب', 'يأتي'],
            'التعليم': ['كتاب', 'قلم', 'مدرسة', 'معلم', 'طالب', 'درس', 'تعلم', 'دراسة'],
            'النقل': ['سيارة', 'حافلة', 'قطار', 'طائرة', 'دراجة', 'سفينة'],
            'المهن': ['طبيب', 'معلم', 'مهندس', 'عامل', 'طباخ', 'سائق']
        }
        
        for category, keywords in categories.items():
            if any(keyword in text for keyword in keywords):
                return category
        
        return "متنوعة"

    def save_progress(self, words_list):
        """حفظ التقدم"""
        try:
            # تحويل tuples إلى dictionaries
            words_dict = []
            for word_tuple in words_list:
                if len(word_tuple) >= 6:
                    armenian, phonetic, arabic, arabic_phonetic, category, source = word_tuple
                    words_dict.append({
                        'armenian': armenian,
                        'phonetic': phonetic,
                        'arabic': arabic,
                        'arabic_phonetic': arabic_phonetic,
                        'category': category,
                        'source': source,
                        'audio': re.sub(r'[^a-zA-Z0-9]', '-', phonetic.lower())
                    })
            
            # حفظ JSON
            with open('armenian_words_progress.json', 'w', encoding='utf-8') as f:
                json.dump(words_dict, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            print(f"❌ خطأ في حفظ التقدم: {e}")

    def organize_by_categories(self, words_list):
        """تنظيم الكلمات حسب الفئات"""
        categories = {}
        
        for word_tuple in words_list:
            if len(word_tuple) >= 5:
                armenian, phonetic, arabic, arabic_phonetic, category = word_tuple[:5]
                
                if category not in categories:
                    categories[category] = {
                        'icon': self.get_category_icon(category),
                        'description': f'كلمات متعلقة بـ {category}',
                        'words': []
                    }
                
                word_dict = {
                    'armenian': armenian,
                    'phonetic': phonetic,
                    'arabic': arabic,
                    'arabic_phonetic': arabic_phonetic,
                    'audio': re.sub(r'[^a-zA-Z0-9]', '-', phonetic.lower())
                }
                
                categories[category]['words'].append(word_dict)
        
        return categories

    def get_category_icon(self, category):
        """الحصول على أيقونة الفئة"""
        icons = {
            'التحيات والمجاملات': '👋',
            'الأرقام والحساب': '🔢',
            'العائلة والأقارب': '👨‍👩‍👧‍👦',
            'الألوان': '🎨',
            'الطعام والشراب': '🍽️',
            'الجسم وأجزاؤه': '👤',
            'الملابس': '👕',
            'البيت والمنزل': '🏠',
            'الطبيعة والطقس': '🌳',
            'الحيوانات': '🐾',
            'الوقت والزمن': '⏰',
            'المشاعر': '😊',
            'الأفعال': '🏃',
            'التعليم': '📚',
            'النقل': '🚗',
            'المهن': '👷',
            'متنوعة': '📝'
        }
        return icons.get(category, '📝')

    def save_final_data(self, words_list):
        """حفظ البيانات النهائية"""
        try:
            # تنظيم حسب الفئات
            categories = self.organize_by_categories(words_list)
            
            # حفظ البيانات المصنفة
            with open('armenian_vocabulary_complete.json', 'w', encoding='utf-8') as f:
                json.dump(categories, f, ensure_ascii=False, indent=2)
            
            # حفظ JavaScript
            js_content = f"""// مفردات أرمينية شاملة مصنفة
// Comprehensive Armenian Vocabulary by Categories

window.armenianVocabulary = {json.dumps(categories, ensure_ascii=False, indent=2)};

// إحصائيات
window.vocabularyStats = {{
"""
            
            total_words = 0
            for category_name, category_data in categories.items():
                word_count = len(category_data['words'])
                total_words += word_count
                js_content += f'  "{category_name}": {word_count},\n'
            
            js_content += f"""  "total": {total_words}
}};

console.log('📊 تم تحميل', {total_words}, 'كلمة أرمينية في', {len(categories)}, 'فئة');
"""
            
            with open('armenian_vocabulary_complete.js', 'w', encoding='utf-8') as f:
                f.write(js_content)
            
            print(f"✅ تم حفظ {total_words} كلمة في {len(categories)} فئة")
            
            # طباعة الإحصائيات
            print("\n📊 إحصائيات الفئات:")
            for category_name, category_data in categories.items():
                word_count = len(category_data['words'])
                icon = category_data['icon']
                print(f"   {icon} {category_name}: {word_count} كلمة")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات النهائية: {e}")

    def run_advanced_extraction(self):
        """تشغيل الاستخراج المتقدم"""
        print("🚀 بدء الاستخراج المتقدم للمفردات الأرمينية")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # استخراج من مصادر متعددة
        all_words = self.extract_from_multiple_sources()
        
        if not all_words:
            print("❌ لم يتم استخراج أي كلمات")
            return
        
        print(f"\n✅ تم استخراج {len(all_words)} كلمة فريدة")
        
        # حفظ البيانات النهائية
        self.save_final_data(all_words)
        
        # إحصائيات نهائية
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\n🎉 تم الانتهاء من الاستخراج المتقدم!")
        print(f"⏱️ الوقت المستغرق: {duration}")
        print(f"📁 الملفات المحفوظة:")
        print(f"   - armenian_vocabulary_complete.json")
        print(f"   - armenian_vocabulary_complete.js")

def main():
    """الدالة الرئيسية"""
    scraper = AdvancedArmenianScraper()
    scraper.run_advanced_extraction()

if __name__ == "__main__":
    main()
