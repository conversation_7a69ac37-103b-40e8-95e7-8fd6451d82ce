#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مستخرج الصوتيات الأرمينية المحسن
Enhanced Armenian Audio Extractor
"""

import requests
from bs4 import BeautifulSoup
import json
import re
import time
import os
from pathlib import Path
from urllib.parse import urljoin
import random

class ArmenianAudioExtractor:
    def __init__(self):
        self.base_url = "https://www.lingohut.com"
        self.audio_dir = Path("armenian_audio")
        self.audio_dir.mkdir(exist_ok=True)
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        self.audio_files = {}
        self.downloaded_count = 0

    def find_audio_urls(self):
        """البحث عن روابط الملفات الصوتية"""
        print("🔍 البحث عن الملفات الصوتية...")
        
        audio_urls = []
        
        # قائمة الصفحات للبحث
        pages = [
            "/ar/l76/%D8%AA%D8%B9%D9%84%D9%85-%D8%A7%D9%84%D9%84%D8%BA%D8%A9-%D8%A7%D9%84%D8%A3%D8%B1%D9%85%D9%8A%D9%86%D9%8A%D8%A9"
        ]
        
        # إضافة صفحات الدروس
        for i in range(1, 26):  # أول 25 درس
            pages.append(f"/ar/v477/{i}")
        
        for page in pages:
            try:
                print(f"📖 فحص: {page}")
                response = self.session.get(self.base_url + page, timeout=20)
                
                if response.status_code == 200:
                    urls = self.extract_audio_from_page(response.text, page)
                    audio_urls.extend(urls)
                    if urls:
                        print(f"   ✅ وجد {len(urls)} ملف صوتي")
                
                time.sleep(random.uniform(1, 3))
                
            except Exception as e:
                print(f"   ❌ خطأ: {e}")
        
        return list(set(audio_urls))  # إزالة المكررات

    def extract_audio_from_page(self, html, page_url):
        """استخراج روابط الصوت من صفحة"""
        audio_urls = []
        
        try:
            # البحث عن روابط الصوت بـ regex
            patterns = [
                r'https?://[^"\s]+\.(?:mp3|wav|ogg)',
                r'/[^"\s]+\.(?:mp3|wav|ogg)',
                r'audio/[^"\s]+\.(?:mp3|wav|ogg)'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, html, re.IGNORECASE)
                for match in matches:
                    if not match.startswith('http'):
                        match = urljoin(self.base_url + page_url, match)
                    audio_urls.append(match)
            
            # البحث في HTML elements
            soup = BeautifulSoup(html, 'html.parser')
            
            # عناصر audio
            for audio in soup.find_all(['audio', 'source']):
                src = audio.get('src')
                if src and any(ext in src for ext in ['.mp3', '.wav', '.ogg']):
                    if not src.startswith('http'):
                        src = urljoin(self.base_url + page_url, src)
                    audio_urls.append(src)
            
            # data attributes
            for elem in soup.find_all(attrs={'data-audio': True}):
                audio_url = elem.get('data-audio')
                if audio_url:
                    if not audio_url.startswith('http'):
                        audio_url = urljoin(self.base_url + page_url, audio_url)
                    audio_urls.append(audio_url)
        
        except Exception as e:
            print(f"❌ خطأ في استخراج الصوت: {e}")
        
        return audio_urls

    def download_audio(self, url, filename=None):
        """تحميل ملف صوتي"""
        try:
            if not filename:
                filename = f"audio_{len(self.audio_files) + 1}.mp3"
            
            print(f"⬇️ تحميل: {filename}")
            
            response = self.session.get(url, timeout=30)
            if response.status_code == 200:
                file_path = self.audio_dir / filename
                
                with open(file_path, 'wb') as f:
                    f.write(response.content)
                
                self.audio_files[filename] = {
                    'url': url,
                    'size': len(response.content),
                    'path': str(file_path)
                }
                
                self.downloaded_count += 1
                print(f"   ✅ تم حفظ: {filename}")
                return True
            
        except Exception as e:
            print(f"   ❌ فشل: {e}")
        
        return False

    def create_audio_generator(self):
        """إنشاء مولد الصوت للمفردات"""
        print("🎵 إنشاء مولد الصوت...")
        
        # قراءة المفردات
        vocab_file = Path('armenian_vocabulary_final.json')
        if not vocab_file.exists():
            print("❌ ملف المفردات غير موجود")
            return
        
        with open(vocab_file, 'r', encoding='utf-8') as f:
            vocabulary = json.load(f)
        
        # إنشاء HTML مبسط
        html_content = f'''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>🔊 مولد الصوت الأرمني</title>
    <style>
        body {{ font-family: Arial; padding: 20px; background: #f0f0f0; }}
        .container {{ max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }}
        .word {{ margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }}
        .btn {{ background: #4CAF50; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }}
        .btn:hover {{ background: #45a049; }}
        .progress {{ background: #ddd; border-radius: 10px; margin: 20px 0; }}
        .progress-bar {{ background: #4CAF50; height: 20px; border-radius: 10px; width: 0%; transition: width 0.3s; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🔊 مولد الصوت الأرمني</h1>
        <p>توليد ملفات صوتية لـ 110 كلمة أرمينية</p>
        
        <button class="btn" onclick="generateAll()">🎵 توليد جميع الأصوات</button>
        <button class="btn" onclick="downloadData()">📥 تحميل البيانات</button>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div id="status">جاهز للبدء...</div>
        <div id="wordsList"></div>
    </div>

    <script>
        const vocabulary = {json.dumps(vocabulary, ensure_ascii=False)};
        let allWords = [];
        let audioData = {{}};
        
        // تحضير الكلمات
        function prepareWords() {{
            allWords = [];
            for (const [category, data] of Object.entries(vocabulary)) {{
                data.words.forEach(word => {{
                    allWords.push({{
                        ...word,
                        category,
                        audioFile: word.phonetic.replace(/[^a-zA-Z0-9]/g, '_') + '.mp3'
                    }});
                }});
            }}
            displayWords();
        }}
        
        // عرض الكلمات
        function displayWords() {{
            const container = document.getElementById('wordsList');
            container.innerHTML = allWords.map((word, i) => `
                <div class="word">
                    <strong>${{word.armenian}}</strong> (${{word.phonetic}}) - ${{word.arabic}}
                    <button class="btn" onclick="playWord(${{i}})">🔊 تشغيل</button>
                    <span id="status-${{i}}"></span>
                </div>
            `).join('');
        }}
        
        // تشغيل كلمة
        function playWord(index) {{
            const word = allWords[index];
            const utterance = new SpeechSynthesisUtterance(word.phonetic);
            utterance.lang = 'en-US';
            utterance.rate = 0.7;
            utterance.pitch = 1;
            
            utterance.onend = () => {{
                document.getElementById(`status-${{index}}`).textContent = '✅';
                audioData[word.audioFile] = {{
                    word: word,
                    generated: true,
                    timestamp: new Date().toISOString()
                }};
            }};
            
            speechSynthesis.speak(utterance);
        }}
        
        // توليد جميع الأصوات
        async function generateAll() {{
            const statusEl = document.getElementById('status');
            const progressBar = document.getElementById('progressBar');
            
            for (let i = 0; i < allWords.length; i++) {{
                statusEl.textContent = `توليد ${{i + 1}} من ${{allWords.length}}: ${{allWords[i].phonetic}}`;
                progressBar.style.width = ((i + 1) / allWords.length * 100) + '%';
                
                playWord(i);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }}
            
            statusEl.textContent = '✅ تم توليد جميع الأصوات!';
        }}
        
        // تحميل البيانات
        function downloadData() {{
            const data = {{
                vocabulary: vocabulary,
                audioFiles: audioData,
                totalWords: allWords.length,
                generatedAt: new Date().toISOString()
            }};
            
            const blob = new Blob([JSON.stringify(data, null, 2)], {{type: 'application/json'}});
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = 'armenian_audio_data.json';
            a.click();
            
            URL.revokeObjectURL(url);
        }}
        
        // تحضير عند التحميل
        document.addEventListener('DOMContentLoaded', prepareWords);
    </script>
</body>
</html>'''
        
        with open('armenian_audio_generator.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print("✅ تم إنشاء مولد الصوت: armenian_audio_generator.html")

    def save_audio_manifest(self):
        """حفظ ملف معلومات الصوتيات"""
        manifest = {
            'audio_files': self.audio_files,
            'total_downloaded': self.downloaded_count,
            'audio_directory': str(self.audio_dir),
            'extraction_date': time.strftime('%Y-%m-%d %H:%M:%S'),
            'instructions': {
                'ar': 'ضع الملفات الصوتية في مجلد assets/audio في التطبيق',
                'en': 'Place audio files in assets/audio folder in the app'
            }
        }
        
        with open('audio_manifest.json', 'w', encoding='utf-8') as f:
            json.dump(manifest, f, ensure_ascii=False, indent=2)
        
        print("✅ تم حفظ ملف معلومات الصوتيات")

    def run_extraction(self):
        """تشغيل الاستخراج"""
        print("🔊 بدء استخراج الصوتيات الأرمينية")
        print("=" * 50)
        
        # البحث عن الملفات الصوتية
        audio_urls = self.find_audio_urls()
        
        if audio_urls:
            print(f"\n⬇️ تحميل {len(audio_urls)} ملف صوتي...")
            
            for i, url in enumerate(audio_urls[:20], 1):  # أول 20 ملف
                filename = f"armenian_audio_{i:03d}.mp3"
                self.download_audio(url, filename)
                time.sleep(random.uniform(1, 2))
        
        # إنشاء مولد الصوت
        self.create_audio_generator()
        
        # حفظ معلومات الصوتيات
        self.save_audio_manifest()
        
        print(f"\n🎉 تم الانتهاء!")
        print(f"📊 النتائج:")
        print(f"   🔊 ملفات صوتية: {self.downloaded_count}")
        print(f"   📁 مجلد الصوتيات: {self.audio_dir}")
        print(f"   🎵 مولد الصوت: armenian_audio_generator.html")
        print(f"\n📝 التعليمات:")
        print(f"   1. افتح armenian_audio_generator.html")
        print(f"   2. انقر 'توليد جميع الأصوات'")
        print(f"   3. انقر 'تحميل البيانات'")

def main():
    extractor = ArmenianAudioExtractor()
    extractor.run_extraction()

if __name__ == "__main__":
    main()
