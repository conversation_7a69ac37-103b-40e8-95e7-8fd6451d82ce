#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحويل النطق اللاتيني إلى عربي
Test Arabic Phonetic Conversion
"""

import re

def generate_arabic_phonetic(phonetic):
    """تحويل النطق اللاتيني إلى نطق عربي"""
    if not phonetic:
        return ""

    # قاموس تحويل الأحرف اللاتينية إلى عربية
    latin_to_arabic = {
        # حروف العلة
        'a': 'ا', 'e': 'ي', 'i': 'ي', 'o': 'و', 'u': 'و',
        'A': 'ا', 'E': 'ي', 'I': 'ي', 'O': 'و', 'U': 'و',

        # الحروف الساكنة
        'b': 'ب', 'p': 'ب', 'f': 'ف', 'v': 'ف',
        'B': 'ب', 'P': 'ب', 'F': 'ف', 'V': 'ف',

        't': 'ت', 'd': 'د', 'th': 'ث', 'dh': 'ذ',
        'T': 'ت', 'D': 'د', 'Th': 'ث', 'Dh': 'ذ',

        's': 'س', 'z': 'ز', 'sh': 'ش', 'zh': 'ج',
        'S': 'س', 'Z': 'ز', 'Sh': 'ش', 'Zh': 'ج',

        'k': 'ك', 'g': 'ج', 'q': 'ق', 'gh': 'غ',
        'K': 'ك', 'G': 'ج', 'Q': 'ق', 'Gh': 'غ',

        'h': 'ه', 'kh': 'خ', 'ch': 'تش', 'j': 'ج',
        'H': 'ه', 'Kh': 'خ', 'Ch': 'تش', 'J': 'ج',

        'l': 'ل', 'r': 'ر', 'm': 'م', 'n': 'ن',
        'L': 'ل', 'R': 'ر', 'M': 'م', 'N': 'ن',

        'w': 'و', 'y': 'ي', 'x': 'كس', 'c': 'ك',
        'W': 'و', 'Y': 'ي', 'X': 'كس', 'C': 'ك',

        # أحرف خاصة
        'ʿ': 'ع', 'ʾ': 'ء', 'ə': 'ا', 'ě': 'ي',
        'ó': 'و', 'á': 'ا', 'í': 'ي', 'ú': 'و',

        # مجموعات خاصة
        'ng': 'نغ', 'nk': 'نك', 'mp': 'مب', 'nt': 'نت',
        'ts': 'تس', 'dz': 'دز', 'ps': 'بس', 'ks': 'كس'
    }

    # تنظيف النطق
    phonetic_clean = phonetic.strip()

    # تحويل المجموعات الطويلة أولاً
    for latin, arabic in sorted(latin_to_arabic.items(), key=lambda x: len(x[0]), reverse=True):
        phonetic_clean = phonetic_clean.replace(latin, arabic)

    # إزالة الأحرف غير المرغوب فيها
    phonetic_clean = re.sub(r'[^\u0600-\u06FF\u0750-\u077F]', '', phonetic_clean)

    return phonetic_clean if phonetic_clean else fallback_arabic_phonetic(phonetic)

def fallback_arabic_phonetic(phonetic):
    """طريقة احتياطية لتحويل النطق"""
    result = ""
    for char in phonetic.lower():
        if char in 'aeiou':
            result += 'ا'
        elif char in 'bcdfghjklmnpqrstvwxyz':
            result += char
        else:
            result += char
    return result

def test_conversions():
    """اختبار تحويلات مختلفة"""
    test_cases = [
        # أمثلة من الموقع - الأرقام (درس 1)
        ("Meg", "ميك"),
        ("Yerku", "ييركو"),
        ("Yerek", "ييريك"),
        ("Chors", "تشورس"),
        ("Hing", "هينغ"),

        # التحيات (درس 2)
        ("Barev", "باريف"),
        ("Bari luys", "باري لويس"),
        ("Bari yereko", "باري ييريكو"),
        ("Bari gisher", "باري غيشير"),

        # الألوان (درس 3)
        ("Karmir", "كارمير"),
        ("Kanach", "كاناتش"),
        ("Kapuyt", "كابويت"),
        ("Deghyin", "ديغيين"),

        # العائلة (درس 4)
        ("Mayr", "مايير"),
        ("Hayr", "هايير"),
        ("Yeghbayr", "ييغبايير"),
        ("Khoyr", "خويير"),

        # الطعام (درس 5)
        ("Hats", "هاتس"),
        ("Jur", "جور"),
        ("Msis", "مسيس"),
        ("Panir", "بانير"),

        # أمثلة معقدة من دروس متقدمة
        ("Žamacówyc", "جاماكوويك"),
        ("Tankaržek", "تانكارجيك"),
        ("Krçkazard", "كركازارد"),
        ("Verchin", "فيرتشين"),
        ("Ashkhatanq", "اشخاتانك"),

        # كلمات طويلة من الدروس المتقدمة
        ("Hamalsaran", "هامالسران"),
        ("Kentronakan", "كينترونكان"),
        ("Patmutyan", "باتموتيان"),
    ]

    print("🔤 اختبار تحويل النطق اللاتيني إلى عربي")
    print("=" * 50)

    for latin, expected in test_cases:
        result = generate_arabic_phonetic(latin)
        status = "✅" if result else "❌"
        print(f"{status} {latin:15} → {result:15} (متوقع: {expected})")

    print("\n" + "=" * 50)
    print("✅ انتهاء الاختبار")

if __name__ == "__main__":
    test_conversions()
