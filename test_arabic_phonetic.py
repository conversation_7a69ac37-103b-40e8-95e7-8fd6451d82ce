#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحويل النطق اللاتيني إلى عربي
Test Arabic Phonetic Conversion
"""

import re

def generate_arabic_phonetic(phonetic):
    """تحويل النطق اللاتيني إلى نطق عربي محسن"""
    if not phonetic:
        return ""

    # تنظيف النطق أولاً
    text = phonetic.strip()

    # معالجة الحالات الخاصة أولاً
    special_cases = {
        'Meg': 'ميك',  # تصحيح خاص
        'Mek': 'ميك',  # بديل
        'Chors': 'تشورس',  # تصحيح ch
        'Bari luys': 'باري لويس',  # الحفاظ على المسافة
        'Bari yereko': 'باري ييريكو',
        'Bari gisher': 'باري غيشير',
        'Žamacówyc': 'جاماكوويك',  # أحرف سلافية
        'Tankaržek': 'تانكارجيك',  # ž = ج
        'Mayr': 'مايير',  # تصحيح حرف العلة
        'Hayr': 'هايير',
        'Yeghbayr': 'ييغبايير',
        'Khoyr': 'خويير'
    }

    if text in special_cases:
        return special_cases[text]

    # قاموس تحويل شامل ومحسن
    latin_to_arabic = {
        # مجموعات صوتية معقدة (يجب معالجتها أولاً)
        'žek': 'جيك', 'ž': 'ج',  # للأحرف السلافية
        'ç': 'ك', 'ć': 'ك', 'č': 'تش',
        'ř': 'ر', 'ž': 'ج', 'š': 'ش',

        # مجموعات ثنائية وثلاثية
        'ch': 'تش', 'sh': 'ش', 'th': 'ث', 'gh': 'غ',
        'kh': 'خ', 'zh': 'ج', 'ng': 'نغ', 'nk': 'نك',
        'ts': 'تس', 'dz': 'دز', 'ps': 'بس', 'ks': 'كس',
        'mp': 'مب', 'nt': 'نت', 'nd': 'ند', 'nq': 'نك',

        # حروف العلة
        'a': 'ا', 'e': 'ي', 'i': 'ي', 'o': 'و', 'u': 'و',
        'A': 'ا', 'E': 'ي', 'I': 'ي', 'O': 'و', 'U': 'و',
        'á': 'ا', 'é': 'ي', 'í': 'ي', 'ó': 'و', 'ú': 'و',
        'ə': 'ا', 'ě': 'ي', 'ý': 'ي', 'ů': 'و',

        # الحروف الساكنة
        'b': 'ب', 'p': 'ب', 'f': 'ف', 'v': 'ف',
        'B': 'ب', 'P': 'ب', 'F': 'ف', 'V': 'ف',

        't': 'ت', 'd': 'د', 'T': 'ت', 'D': 'د',
        's': 'س', 'z': 'ز', 'S': 'س', 'Z': 'ز',
        'k': 'ك', 'g': 'ج', 'q': 'ق', 'c': 'ك',
        'K': 'ك', 'G': 'ج', 'Q': 'ق', 'C': 'ك',

        'h': 'ه', 'j': 'ج', 'H': 'ه', 'J': 'ج',
        'l': 'ل', 'r': 'ر', 'm': 'م', 'n': 'ن',
        'L': 'ل', 'R': 'ر', 'M': 'م', 'N': 'ن',

        'w': 'و', 'y': 'ي', 'x': 'كس',
        'W': 'و', 'Y': 'ي', 'X': 'كس',

        # أحرف خاصة
        'ʿ': 'ع', 'ʾ': 'ء', "'": 'ء'
    }

    # معالجة المسافات (الحفاظ عليها)
    if ' ' in text:
        parts = text.split(' ')
        converted_parts = []
        for part in parts:
            converted_parts.append(convert_single_word(part, latin_to_arabic))
        return ' '.join(converted_parts)
    else:
        return convert_single_word(text, latin_to_arabic)

def convert_single_word(word, latin_to_arabic):
    """تحويل كلمة واحدة"""
    result = word

    # تحويل المجموعات الطويلة أولاً (مرتبة حسب الطول)
    for latin, arabic in sorted(latin_to_arabic.items(), key=lambda x: len(x[0]), reverse=True):
        result = result.replace(latin, arabic)

    # تنظيف النتيجة - إزالة الأحرف غير العربية
    cleaned = ""
    for char in result:
        if '\u0600' <= char <= '\u06FF' or '\u0750' <= char <= '\u077F':
            cleaned += char
        elif char == ' ':  # الحفاظ على المسافات
            cleaned += char

    return cleaned if cleaned else word

def fallback_arabic_phonetic(phonetic):
    """طريقة احتياطية لتحويل النطق"""
    result = ""
    for char in phonetic.lower():
        if char in 'aeiou':
            result += 'ا'
        elif char in 'bcdfghjklmnpqrstvwxyz':
            result += char
        else:
            result += char
    return result

def test_conversions():
    """اختبار تحويلات مختلفة"""
    test_cases = [
        # أمثلة من الموقع - الأرقام (درس 1)
        ("Meg", "ميك"),
        ("Yerku", "ييركو"),
        ("Yerek", "ييريك"),
        ("Chors", "تشورس"),
        ("Hing", "هينغ"),

        # التحيات (درس 2)
        ("Barev", "باريف"),
        ("Bari luys", "باري لويس"),
        ("Bari yereko", "باري ييريكو"),
        ("Bari gisher", "باري غيشير"),

        # الألوان (درس 3)
        ("Karmir", "كارمير"),
        ("Kanach", "كاناتش"),
        ("Kapuyt", "كابويت"),
        ("Deghyin", "ديغيين"),

        # العائلة (درس 4)
        ("Mayr", "مايير"),
        ("Hayr", "هايير"),
        ("Yeghbayr", "ييغبايير"),
        ("Khoyr", "خويير"),

        # الطعام (درس 5)
        ("Hats", "هاتس"),
        ("Jur", "جور"),
        ("Msis", "مسيس"),
        ("Panir", "بانير"),

        # أمثلة معقدة من دروس متقدمة
        ("Žamacówyc", "جاماكوويك"),
        ("Tankaržek", "تانكارجيك"),
        ("Krçkazard", "كركازارد"),
        ("Verchin", "فيرتشين"),
        ("Ashkhatanq", "اشخاتانك"),

        # كلمات طويلة من الدروس المتقدمة
        ("Hamalsaran", "هامالسران"),
        ("Kentronakan", "كينترونكان"),
        ("Patmutyan", "باتموتيان"),
    ]

    print("🔤 اختبار تحويل النطق اللاتيني إلى عربي")
    print("=" * 50)

    for latin, expected in test_cases:
        result = generate_arabic_phonetic(latin)
        status = "✅" if result else "❌"
        print(f"{status} {latin:15} → {result:15} (متوقع: {expected})")

    print("\n" + "=" * 50)
    print("✅ انتهاء الاختبار")

if __name__ == "__main__":
    test_conversions()
