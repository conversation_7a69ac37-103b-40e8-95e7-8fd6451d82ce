@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    استخراج الصوتيات فقط
echo    Extract Audio Files Only
echo ========================================
echo.
echo 🎵 سيتم استخراج الملفات الصوتية من المفردات الموجودة
echo 📁 يجب أن يكون ملف vocabulary_data_complete.json موجوداً
echo ⏳ هذا قد يستغرق 10-20 دقيقة حسب عدد الملفات
echo.

REM التحقق من وجود ملف المفردات
if not exist "vocabulary_data_complete.json" (
    echo ❌ ملف vocabulary_data_complete.json غير موجود
    echo 💡 يجب تشغيل الاستخراج الشامل أولاً
    echo    انقر نقراً مزدوجاً على run_complete_extraction.bat
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على ملف المفردات
echo.

REM تثبيت المكتبات المطلوبة
echo 📦 تثبيت المكتبات المطلوبة...
pip install requests gtts pygame
echo.

REM تشغيل مستخرج الصوتيات
echo 🎵 بدء استخراج الصوتيات...
python audio_extractor.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء استخراج الصوتيات
    echo 💡 تأكد من:
    echo    - اتصال الإنترنت
    echo    - وجود ملف vocabulary_data_complete.json
    echo    - تثبيت Python بشكل صحيح
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم الانتهاء من استخراج الصوتيات!
echo.
echo 📁 الملفات المُنشأة:
echo    - audio/ (مجلد الملفات الصوتية)
echo    - audio/audio_index.json (فهرس الصوتيات)
echo.
echo 💡 يمكنك الآن تشغيل التطبيق: انقر نقراً مزدوجاً على run_app.bat
echo.
pause
