# 🐍 دليل استخدام ملفات Python

## 🎯 **الملفات المتاحة**

### 🚀 **للتشغيل السريع:**
```bash
python quick_start.py
```
**ما يفعله:**
- يفحص البيانات المستخرجة
- إذا وُجدت: يفتح التطبيق مباشرة
- إذا لم توجد: يسأل عن الاستخراج

---

### 🌐 **لفتح التطبيق فقط:**
```bash
python open_app.py
```
**ما يفعله:**
- يفتح التطبيق في المتصفح
- يعرض إحصائيات البيانات
- يفتح عرض البيانات المستخرجة

---

### 📥 **لاستخراج البيانات فقط:**
```bash
python extract_data.py
```
**ما يفعله:**
- يثبت المكتبات المطلوبة
- يفحص الإنترنت
- يستخرج جميع الـ 125 درس
- يضيف النطق العربي

---

### 🔧 **للإدارة الشاملة:**
```bash
python armenian_app_manager.py
```
**ما يفعله:**
- قائمة تفاعلية كاملة
- فحص النظام
- إدارة الاستخراج
- فتح التطبيق
- تثبيت المكتبات

---

## 🎯 **أيهم أختار؟**

### 🥇 **للمبتدئين:**
```bash
python quick_start.py
```
**الأسهل والأسرع!**

### 🥈 **إذا كانت البيانات موجودة:**
```bash
python open_app.py
```

### 🥉 **للاستخراج الجديد:**
```bash
python extract_data.py
```

### 🏆 **للمتقدمين:**
```bash
python armenian_app_manager.py
```

---

## 📋 **خطوات البدء**

### 1️⃣ **تأكد من Python:**
```bash
python --version
# أو
py --version
```

### 2️⃣ **شغل التطبيق:**
```bash
python quick_start.py
```

### 3️⃣ **استمتع بالتعلم!**
- ابحث بالنطق العربي
- استمع للصوتيات
- تصفح الدروس

---

## 🔧 **حل المشاكل**

### ❌ **"Python was not found"**
```bash
# ثبت Python من:
https://python.org/downloads
# تأكد من تحديد "Add Python to PATH"
```

### ❌ **"No module named 'requests'"**
```bash
# سيتم تثبيت المكتبات تلقائياً
# أو شغل:
pip install requests beautifulsoup4
```

### ❌ **"No internet connection"**
```bash
# تحقق من اتصال الإنترنت
# الاستخراج يحتاج إنترنت
```

---

## 🎉 **النتائج المتوقعة**

### ✅ **بعد الاستخراج:**
- **80+ درس** من موقع LingoHut
- **مئات الكلمات** مع النطق العربي
- **تطبيق تفاعلي** في المتصفح
- **بحث متقدم** بالنطق العربي

### 🔤 **أمثلة النطق العربي:**
- `Mek` → `ميك` (واحد)
- `Barev` → `باريف` (مرحبا)
- `Bari luys` → `باري لويس` (صباح الخير)

---

## 💡 **نصائح**

### ⚡ **للسرعة:**
- استخدم `quick_start.py` دائماً
- احفظ البيانات المستخرجة
- لا تحذف ملفات JSON

### 🔄 **للتحديث:**
- شغل `extract_data.py` مرة أخرى
- سيحدث البيانات تلقائياً
- يمكن إيقاف العملية بـ Ctrl+C

### 🎯 **للتعلم:**
- ابدأ بالكلمات البسيطة
- استخدم النطق العربي
- مارس يومياً 15 دقيقة

---

## 🚀 **ابدأ الآن!**

```bash
python quick_start.py
```

**🎉 استمتع بتعلم الأرمينية مع أول تطبيق بالنطق العربي!**
