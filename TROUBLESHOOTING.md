# 🔧 دليل حل المشاكل - تطبيق تعلم الأرمينية

## 🚨 **المشاكل الشائعة وحلولها**

---

## ❌ **مشكلة: "Python was not found"**

### 🔍 **الأعراض:**
```
'python' is not recognized as an internal or external command
```

### ✅ **الحلول:**

#### **الحل 1: تثبيت Python**
1. انقر نقراً مزدوجاً على: `install_python_auto.bat`
2. أو اذهب إلى: https://python.org/downloads
3. حمل أحدث إصدار
4. **مهم**: ✅ تأكد من تحديد "Add Python to PATH"

#### **الحل 2: إعادة تشغيل النظام**
- أعد تشغيل الكمبيوتر بعد تثبيت Python
- جرب فتح Command Prompt جديد

#### **الحل 3: استخدام py بدلاً من python**
```bash
py --version
py advanced_scraper.py
```

---

## ❌ **مشكلة: فشل تثبيت المكتبات**

### 🔍 **الأعراض:**
```
ERROR: Could not install packages
pip install failed
```

### ✅ **الحلول:**

#### **الحل 1: تشغيل كمدير**
1. انقر بالزر الأيمن على Command Prompt
2. اختر "Run as administrator"
3. شغل الأمر مرة أخرى

#### **الحل 2: استخدام --user**
```bash
pip install --user selenium beautifulsoup4 requests webdriver-manager
```

#### **الحل 3: تحديث pip**
```bash
python -m pip install --upgrade pip
```

#### **الحل 4: استخدام أداة التشخيص**
```bash
python diagnose_system.py
```

---

## ❌ **مشكلة: "Scripts is disabled"**

### 🔍 **الأعراض:**
```
execution of scripts is disabled on this system
```

### ✅ **الحل:**
1. افتح PowerShell كمدير
2. شغل الأمر:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```
3. اكتب `Y` للموافقة

---

## ❌ **مشكلة: فشل الاستخراج**

### 🔍 **الأعراض:**
```
❌ فشل في استخراج المفردات
Connection timeout
```

### ✅ **الحلول:**

#### **الحل 1: فحص الإنترنت**
- تأكد من اتصال الإنترنت
- جرب فتح الموقع في المتصفح: https://lingohut.com

#### **الحل 2: استخدام المستخرج المبسط**
```bash
python simple_scraper.py
```

#### **الحل 3: إعادة المحاولة لاحقاً**
- الموقع قد يكون مشغول
- جرب في وقت آخر

#### **الحل 4: استخدام VPN**
- قد يكون الموقع محجوب في منطقتك
- جرب استخدام VPN

---

## ❌ **مشكلة: ChromeDriver**

### 🔍 **الأعراض:**
```
ChromeDriver not found
WebDriver exception
```

### ✅ **الحلول:**

#### **الحل 1: تثبيت Chrome**
- ثبت Google Chrome من: https://chrome.google.com
- أعد تشغيل الكمبيوتر

#### **الحل 2: تحديث webdriver-manager**
```bash
pip install --upgrade webdriver-manager
```

#### **الحل 3: تحميل يدوي**
1. اذهب إلى: https://chromedriver.chromium.org
2. حمل الإصدار المناسب لـ Chrome
3. ضعه في مجلد Python

---

## ❌ **مشكلة: الملفات لا تُفتح**

### 🔍 **الأعراض:**
```
The term 'run_complete_extraction.bat' is not recognized
```

### ✅ **الحلول:**

#### **الحل 1: استخدام المسار الكامل**
```bash
.\run_complete_extraction.bat
```

#### **الحل 2: فتح من File Explorer**
- انقر نقراً مزدوجاً على الملف مباشرة
- لا تستخدم PowerShell

#### **الحل 3: استخدام الملف المحسن**
```bash
.\run_extraction_fixed.bat
```

---

## 🔧 **أدوات التشخيص**

### 🩺 **أداة التشخيص الشاملة**
```bash
python diagnose_system.py
```
**ما تفعله:**
- تفحص Python والمكتبات
- تختبر الاتصال بالإنترنت
- تتحقق من الموقع المستهدف
- تقترح الحلول

### 🧪 **اختبار سريع**
```bash
python quick_test.py
```
**ما يفعله:**
- يختبر استخراج درس واحد
- يتحقق من النطق العربي
- يعطي نتائج سريعة

---

## 📋 **خطة حل المشاكل المرحلية**

### 🥇 **المرحلة 1: التحقق الأساسي**
1. ✅ تأكد من تثبيت Python
2. ✅ تأكد من اتصال الإنترنت
3. ✅ تأكد من وجود الملفات

### 🥈 **المرحلة 2: التشخيص**
1. شغل: `python diagnose_system.py`
2. اقرأ النتائج بعناية
3. اتبع الاقتراحات

### 🥉 **المرحلة 3: الحلول البديلة**
1. جرب: `run_extraction_fixed.bat`
2. أو: `python simple_scraper.py`
3. أو: استخدم البيانات الموجودة

---

## 🆘 **إذا لم تنجح الحلول**

### 📞 **خطوات الدعم:**

#### **1. جمع المعلومات**
```bash
python diagnose_system.py > diagnosis.txt
```

#### **2. وصف المشكلة**
- ما الخطأ الذي يظهر؟
- متى يحدث الخطأ؟
- ما الخطوات التي جربتها؟

#### **3. الملفات المطلوبة**
- لقطة شاشة للخطأ
- ملف diagnosis.txt
- وصف نظام التشغيل

---

## 💡 **نصائح لتجنب المشاكل**

### ✅ **قبل البدء:**
1. تأكد من اتصال إنترنت مستقر
2. أغلق برامج مكافحة الفيروسات مؤقتاً
3. تأكد من وجود مساحة كافية (500 MB)

### ✅ **أثناء التشغيل:**
1. لا تغلق النافذة أثناء الاستخراج
2. لا تشغل برامج أخرى كثيرة
3. كن صبوراً - العملية تحتاج وقت

### ✅ **بعد الانتهاء:**
1. احفظ نسخة احتياطية من الملفات
2. تأكد من عمل التطبيق
3. جرب البحث والصوتيات

---

## 🎯 **الملفات البديلة للتشغيل**

إذا لم يعمل `run_complete_extraction.bat`:

### 📁 **جرب هذه الملفات بالترتيب:**
1. `run_extraction_fixed.bat` ← **الأفضل**
2. `run_extraction_with_check.bat`
3. `python advanced_scraper.py`
4. `python simple_scraper.py`

### 📱 **لفتح التطبيق فقط:**
1. `open_app.bat`
2. أو افتح `index.html` مباشرة
3. أو افتح `demo_extracted_data.html`

---

## 🎉 **إذا نجح كل شيء**

### ✅ **ستحصل على:**
- ملفات JSON و JavaScript مع النطق العربي
- تطبيق تفاعلي يعمل في المتصفح
- 80+ درس مع مئات الكلمات
- نطق عربي فريد لكل كلمة

### 🚀 **الخطوات التالية:**
1. جرب البحث بالنطق العربي
2. استمع للصوتيات
3. صدر المفردات كملف نصي
4. استمتع بتعلم الأرمينية!

**💪 لا تستسلم! المشاكل التقنية طبيعية ولها حلول دائماً!**
