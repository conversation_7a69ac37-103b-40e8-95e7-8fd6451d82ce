../../Scripts/gtts-cli.exe,sha256=eR7q6GeLwUXX_Z8T17WNmkidUCLuaIJuyOwDXvlTrvg,108412
gTTS-2.5.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
gTTS-2.5.4.dist-info/LICENSE,sha256=Tf7kWll-TgGgaCdEJzYTqTzmB3Zdh99cNzKocr-10aU,1093
gTTS-2.5.4.dist-info/METADATA,sha256=CXzlmtO5Jb7wtwq201-4CsVJzPXqbgRJ6qdk9ASHH_w,4132
gTTS-2.5.4.dist-info/RECORD,,
gTTS-2.5.4.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
gTTS-2.5.4.dist-info/WHEEL,sha256=P9jw-gEje8ByB7_hXoICnHtVCrEwMQh-630tKvQWehc,91
gTTS-2.5.4.dist-info/entry_points.txt,sha256=bI9BIrpE9bB1FVhrm3FHJrfQTsIcnfngqvGqUMNLthM,46
gTTS-2.5.4.dist-info/top_level.txt,sha256=OFgvuVh7ic8wSxEPlCYvHKE8FsIkAr2fdzGCppOFcZI,5
gtts/__init__.py,sha256=4dHB7xRt5IJcV5R3qPAOT9w8saUXKUwNhNzghZjtzso,152
gtts/__pycache__/__init__.cpython-312.pyc,,
gtts/__pycache__/accents.cpython-312.pyc,,
gtts/__pycache__/cli.cpython-312.pyc,,
gtts/__pycache__/lang.cpython-312.pyc,,
gtts/__pycache__/langs.cpython-312.pyc,,
gtts/__pycache__/tts.cpython-312.pyc,,
gtts/__pycache__/utils.cpython-312.pyc,,
gtts/__pycache__/version.cpython-312.pyc,,
gtts/accents.py,sha256=V3LpPxaIyP1byKjJTH45lYanrKRU9Tb5A_z5XsaxFYE,2050
gtts/cli.py,sha256=3LAfzOyNIm0-avf5nf7xXdyfLmz9DTOoBS1Y-lfrZ4M,5697
gtts/lang.py,sha256=k_CHUJTs9JntzI1Q4xuhZNJnoR0N_M0Te88tKwuW9TM,2899
gtts/langs.py,sha256=khUkzXvucdK-vHMVLlzQPuJ6YiQpNUw-TX_-tQJBEMY,1603
gtts/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
gtts/tests/__pycache__/__init__.cpython-312.pyc,,
gtts/tests/__pycache__/test_cli.cpython-312.pyc,,
gtts/tests/__pycache__/test_lang.cpython-312.pyc,,
gtts/tests/__pycache__/test_tts.cpython-312.pyc,,
gtts/tests/__pycache__/test_utils.cpython-312.pyc,,
gtts/tests/input_files/test_cli_test_ascii.txt,sha256=hKI7P_x4Bu7t3iUCxTD-q-LWu8pmFMXUHDDLbNSYDGE,188
gtts/tests/input_files/test_cli_test_utf8.txt,sha256=5I3Fwoy0m1Q_jJRLhjn-Pivyc9g88YVut2BmNT_aLms,232
gtts/tests/test_cli.py,sha256=wl20jpJFkQQgPYfYkGtmNbrseiFpyLWowvGvthwUOQo,7051
gtts/tests/test_lang.py,sha256=VhJGVI8ibms6Gv_cCuz2D4oZcQieTIimUDKj7haixM0,591
gtts/tests/test_tts.py,sha256=GQvXvsC8SqkRb6-7wKXhg4Ck2CSwHYAHTz2QRFPK7Bc,5891
gtts/tests/test_utils.py,sha256=wNDb85XD7S4STiBK8Do9vNl8vOYpvqNmAqUHM7P-21A,1553
gtts/tokenizer/__init__.py,sha256=99kDIHYtzOr9BQgVvYP9fhszuM5YOlsumuLhTmeLvmI,136
gtts/tokenizer/__pycache__/__init__.cpython-312.pyc,,
gtts/tokenizer/__pycache__/core.cpython-312.pyc,,
gtts/tokenizer/__pycache__/pre_processors.cpython-312.pyc,,
gtts/tokenizer/__pycache__/symbols.cpython-312.pyc,,
gtts/tokenizer/__pycache__/tokenizer_cases.cpython-312.pyc,,
gtts/tokenizer/core.py,sha256=mjgq7ThBJDSUmDuZjMtwKl74GSBDcvrSVcUD_6jNsiU,10905
gtts/tokenizer/pre_processors.py,sha256=Ch_ML6TyXCQ_Qu2NP0g5J_fw7jRZiTYrnoEav5YAdTo,1579
gtts/tokenizer/symbols.py,sha256=EkutlUL27oHgk0ntdr9nOZ0PS6-LpQAjhfGme9Ig9mQ,258
gtts/tokenizer/tests/__pycache__/test_core.cpython-312.pyc,,
gtts/tokenizer/tests/__pycache__/test_pre_processors.cpython-312.pyc,,
gtts/tokenizer/tests/__pycache__/test_tokenizer_cases.cpython-312.pyc,,
gtts/tokenizer/tests/test_core.py,sha256=yPQ6zSRvB704FXQJO7MIApXT2JMO3PS66VTUcQHxgeM,2162
gtts/tokenizer/tests/test_pre_processors.py,sha256=8tl2OSLsOS2RGXcPWOFbi9J2ExD38Qb8eZU7PnH6F7Y,798
gtts/tokenizer/tests/test_tokenizer_cases.py,sha256=E6O2g7wSKz6AoHO27CJ4OS7Y7XwWG99c7p2xwaQGZYo,1588
gtts/tokenizer/tokenizer_cases.py,sha256=akFjX_4GlvGAasnpMxwtdP9YBEq3LQ7OCtdMUDUWcpk,1954
gtts/tts.py,sha256=bCco9MikK_Mmq7ew0caOCQAbXiSqfxySOWdMhVoTrj4,13097
gtts/utils.py,sha256=oNDybx-d6MhK8d1oI5sD2fawbFmzMunxwG3V8hf048U,3036
gtts/version.py,sha256=u7qJtvbcGbgT3ue2tQMbSOeGJqqkDTDm-_7HUe49PZA,22
