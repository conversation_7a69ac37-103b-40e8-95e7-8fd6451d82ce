// بيانات المفردات الأرمينية
const vocabularyData = {
    jewelry: [
        {
            armenian: "ծանուցակիցք զարդեր",
            phonetic: "Tankaržek zarder",
            arabic: "محل مجوهرات",
            meaning: "متجر لبيع المجوهرات والحلي",
            lesson: "مجوهرات",
            audio: "tankarzhek-zarder"
        },
        {
            armenian: "ժամացույց",
            phonetic: "Žamacówyc",
            arabic: "ساعة",
            meaning: "أداة لقياس الوقت",
            lesson: "مجوهرات",
            audio: "zhamacowyc"
        },
        {
            armenian: "Կանացի կրծքազարդ",
            phonetic: "Kanaci krç<PERSON>",
            arabic: "بروش",
            meaning: "حلية تُثبت على الملابس",
            lesson: "مجوهرات",
            audio: "kanaci-krckzazard"
        },
        {
            armenian: "Վզնոց",
            phonetic: "Vznoc",
            arabic: "عقد",
            meaning: "حلية تُلبس حول العنق",
            lesson: "مجوهرات",
            audio: "vznoc"
        },
        {
            armenian: "Շղթա",
            phonetic: "Šġta",
            arabic: "سلسلة",
            meaning: "حلقات متصلة من المعدن",
            lesson: "مجوهرات",
            audio: "shghta"
        }
    ],
    greetings: [
        {
            armenian: "Բարև",
            phonetic: "Barev",
            arabic: "مرحبا",
            meaning: "تحية عامة للترحيب",
            lesson: "تحيات",
            audio: "barev"
        },
        {
            armenian: "Բարի լույս",
            phonetic: "Bari luys",
            arabic: "صباح الخير",
            meaning: "تحية صباحية - حرفياً: نور جيد",
            lesson: "تحيات",
            audio: "bari-luys"
        },
        {
            armenian: "Բարի երեկո",
            phonetic: "Bari yereko",
            arabic: "مساء الخير",
            meaning: "تحية مسائية - حرفياً: مساء جيد",
            lesson: "تحيات",
            audio: "bari-yereko"
        }
    ],
    family: [
        {
            armenian: "Ընտանիք",
            phonetic: "Əntaniq",
            arabic: "عائلة",
            meaning: "مجموعة من الأقارب",
            lesson: "العائلة",
            audio: "entaniq"
        },
        {
            armenian: "Մայր",
            phonetic: "Mayr",
            arabic: "أم",
            meaning: "الوالدة",
            lesson: "العائلة",
            audio: "mayr"
        },
        {
            armenian: "Հայր",
            phonetic: "Hayr",
            arabic: "أب",
            meaning: "الوالد",
            lesson: "العائلة",
            audio: "hayr"
        }
    ],
    numbers: [
        {
            armenian: "Մեկ",
            phonetic: "Mek",
            arabic: "واحد",
            meaning: "العدد 1",
            lesson: "الأرقام",
            audio: "mek"
        },
        {
            armenian: "Երկու",
            phonetic: "Yerku",
            arabic: "اثنان",
            meaning: "العدد 2",
            lesson: "الأرقام",
            audio: "yerku"
        },
        {
            armenian: "Երեք",
            phonetic: "Yereq",
            arabic: "ثلاثة",
            meaning: "العدد 3",
            lesson: "الأرقام",
            audio: "yereq"
        }
    ],
    colors: [
        {
            armenian: "Կարմիր",
            phonetic: "Karmir",
            arabic: "أحمر",
            meaning: "لون الدم والورد الأحمر",
            lesson: "الألوان",
            audio: "karmir"
        },
        {
            armenian: "Կանաչ",
            phonetic: "Kanach",
            arabic: "أخضر",
            meaning: "لون الأوراق والعشب",
            lesson: "الألوان",
            audio: "kanach"
        },
        {
            armenian: "Կապույտ",
            phonetic: "Kapuyt",
            arabic: "أزرق",
            meaning: "لون السماء والبحر",
            lesson: "الألوان",
            audio: "kapuyt"
        }
    ]
};

// متغيرات عامة
let currentAudio = null;
let filteredVocabulary = [];
let currentPlayingCard = null;

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
});

function initializeApp() {
    updateStats();
    displayVocabulary('all');
}

function setupEventListeners() {
    // اختيار الدرس
    document.getElementById('lessonSelect').addEventListener('change', function() {
        displayVocabulary(this.value);
    });

    // البحث
    document.getElementById('searchInput').addEventListener('input', function() {
        searchVocabulary(this.value);
    });

    document.getElementById('searchBtn').addEventListener('click', function() {
        const searchTerm = document.getElementById('searchInput').value;
        searchVocabulary(searchTerm);
    });

    // مشغل الصوت العائم
    document.getElementById('playBtn').addEventListener('click', playCurrentAudio);
    document.getElementById('repeatBtn').addEventListener('click', playCurrentAudio);
    document.getElementById('closePlayer').addEventListener('click', closeAudioPlayer);
}

function getAllVocabulary() {
    let allVocab = [];
    for (let category in vocabularyData) {
        allVocab = allVocab.concat(vocabularyData[category]);
    }
    return allVocab;
}

function updateStats() {
    const totalWords = getAllVocabulary().length;
    const totalLessons = Object.keys(vocabularyData).length;
    
    document.getElementById('totalWords').textContent = totalWords;
    document.getElementById('totalLessons').textContent = totalLessons;
}

function displayVocabulary(category) {
    const grid = document.getElementById('vocabularyGrid');
    let vocabToShow = [];

    if (category === 'all') {
        vocabToShow = getAllVocabulary();
    } else if (vocabularyData[category]) {
        vocabToShow = vocabularyData[category];
    }

    filteredVocabulary = vocabToShow;
    renderVocabularyCards(vocabToShow);
}

function searchVocabulary(searchTerm) {
    if (!searchTerm.trim()) {
        const selectedLesson = document.getElementById('lessonSelect').value;
        displayVocabulary(selectedLesson);
        return;
    }

    const allVocab = getAllVocabulary();
    const filtered = allVocab.filter(word => 
        word.arabic.includes(searchTerm) ||
        word.phonetic.toLowerCase().includes(searchTerm.toLowerCase()) ||
        word.lesson.includes(searchTerm) ||
        word.meaning.includes(searchTerm)
    );

    filteredVocabulary = filtered;
    renderVocabularyCards(filtered);
}

function renderVocabularyCards(vocabulary) {
    const grid = document.getElementById('vocabularyGrid');
    
    if (vocabulary.length === 0) {
        grid.innerHTML = `
            <div class="no-results">
                <h3>لا توجد نتائج</h3>
                <p>جرب البحث بكلمات أخرى</p>
            </div>
        `;
        return;
    }

    grid.innerHTML = vocabulary.map((word, index) => `
        <div class="vocab-card" onclick="playWordAudio('${word.audio}', ${index})">
            <button class="play-button" onclick="event.stopPropagation(); playWordAudio('${word.audio}', ${index})">
                ▶️
            </button>
            <div class="lesson-tag">${word.lesson}</div>
            <div class="armenian-text">${word.armenian}</div>
            <div class="phonetic">(${word.phonetic})</div>
            <div class="arabic-meaning">${word.arabic}</div>
            <div class="word-meaning">${word.meaning}</div>
        </div>
    `).join('');
}

function playWordAudio(audioFile, cardIndex) {
    // إيقاف الصوت الحالي إن وجد
    if (currentAudio) {
        currentAudio.pause();
        currentAudio.currentTime = 0;
    }

    // إزالة التأثير من البطاقة السابقة
    if (currentPlayingCard !== null) {
        const prevCard = document.querySelectorAll('.vocab-card')[currentPlayingCard];
        if (prevCard) {
            prevCard.classList.remove('playing');
        }
    }

    // إضافة التأثير للبطاقة الحالية
    const currentCard = document.querySelectorAll('.vocab-card')[cardIndex];
    if (currentCard) {
        currentCard.classList.add('playing');
        currentPlayingCard = cardIndex;
    }

    // تشغيل الصوت الحقيقي
    playRealAudio(audioFile, cardIndex);
    
    // إظهار مشغل الصوت العائم
    showAudioPlayer(filteredVocabulary[cardIndex]);
}

function playRealAudio(audioFile, cardIndex) {
    try {
        // محاولة تشغيل الصوت الحقيقي
        currentAudio = new Audio(`audio/${audioFile}.mp3`);
        
        // إعداد مستمعي الأحداث
        currentAudio.addEventListener('ended', () => {
            const card = document.querySelectorAll('.vocab-card')[cardIndex];
            if (card) {
                card.classList.remove('playing');
            }
            if (currentPlayingCard === cardIndex) {
                currentPlayingCard = null;
            }
        });
        
        currentAudio.addEventListener('error', (e) => {
            console.warn(`تعذر تشغيل الملف الصوتي: ${audioFile}.mp3`);
            // استخدام تحويل النص إلى كلام كبديل
            speakText(filteredVocabulary[cardIndex].phonetic, cardIndex);
        });
        
        // محاولة تشغيل الصوت
        const playPromise = currentAudio.play();
        
        if (playPromise !== undefined) {
            playPromise.catch(error => {
                console.warn('تعذر تشغيل الصوت تلقائياً:', error);
                // استخدام تحويل النص إلى كلام كبديل
                speakText(filteredVocabulary[cardIndex].phonetic, cardIndex);
            });
        }
        
    } catch (error) {
        console.warn(`خطأ في تشغيل الصوت: ${error}`);
        // استخدام تحويل النص إلى كلام كبديل
        speakText(filteredVocabulary[cardIndex].phonetic, cardIndex);
    }
}

function speakText(text, cardIndex) {
    // استخدام Web Speech API كبديل
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(text);
        
        // إعدادات الصوت
        utterance.lang = 'en-US'; // استخدام الإنجليزية للنطق اللاتيني
        utterance.rate = 0.8; // سرعة أبطأ للوضوح
        utterance.pitch = 1;
        utterance.volume = 0.8;
        
        // عند انتهاء النطق
        utterance.onend = () => {
            const card = document.querySelectorAll('.vocab-card')[cardIndex];
            if (card) {
                card.classList.remove('playing');
            }
            if (currentPlayingCard === cardIndex) {
                currentPlayingCard = null;
            }
        };
        
        // تشغيل النطق
        speechSynthesis.speak(utterance);
        
    } else {
        // إذا لم يكن متاحاً، استخدم المحاكاة
        console.log(`Playing audio: ${text}`);
        setTimeout(() => {
            const card = document.querySelectorAll('.vocab-card')[cardIndex];
            if (card) {
                card.classList.remove('playing');
            }
            if (currentPlayingCard === cardIndex) {
                currentPlayingCard = null;
            }
        }, 2000);
    }
}

function showAudioPlayer(word) {
    const player = document.getElementById('audioPlayer');
    const currentWordSpan = document.getElementById('currentWord');
    
    currentWordSpan.textContent = `${word.phonetic} - ${word.arabic}`;
    player.style.display = 'block';
}

function playCurrentAudio() {
    if (currentPlayingCard !== null) {
        const word = filteredVocabulary[currentPlayingCard];
        playWordAudio(word.audio, currentPlayingCard);
    }
}

function closeAudioPlayer() {
    document.getElementById('audioPlayer').style.display = 'none';
    
    // إيقاف الصوت الحالي
    if (currentAudio) {
        currentAudio.pause();
        currentAudio.currentTime = 0;
    }
    
    // إيقاف تحويل النص إلى كلام
    if ('speechSynthesis' in window) {
        speechSynthesis.cancel();
    }
    
    // إزالة التأثير من البطاقة
    if (currentPlayingCard !== null) {
        const card = document.querySelectorAll('.vocab-card')[currentPlayingCard];
        if (card) {
            card.classList.remove('playing');
        }
        currentPlayingCard = null;
    }
}

// دالة لإضافة مفردات جديدة (للاستخدام المستقبلي)
function addNewVocabulary(category, newWords) {
    if (!vocabularyData[category]) {
        vocabularyData[category] = [];
    }
    
    vocabularyData[category] = vocabularyData[category].concat(newWords);
    updateStats();
    
    // تحديث قائمة الدروس
    updateLessonSelect();
}

function updateLessonSelect() {
    const select = document.getElementById('lessonSelect');
    const currentValue = select.value;
    
    // إعادة بناء الخيارات
    select.innerHTML = '<option value="all">جميع الدروس</option>';
    
    for (let category in vocabularyData) {
        const lessonName = vocabularyData[category][0]?.lesson || category;
        select.innerHTML += `<option value="${category}">${lessonName}</option>`;
    }
    
    // استعادة القيمة المحددة
    select.value = currentValue;
}

// دالة لتصدير المفردات كملف نصي
function exportVocabulary() {
    const allVocab = getAllVocabulary();
    let content = 'مفردات اللغة الأرمينية\n';
    content += '===================\n\n';
    
    for (let category in vocabularyData) {
        const lessonName = vocabularyData[category][0]?.lesson || category;
        content += `${lessonName}:\n`;
        content += '-'.repeat(lessonName.length + 1) + '\n';
        
        vocabularyData[category].forEach((word, index) => {
            content += `${index + 1}. ${word.phonetic} - ${word.arabic}\n`;
            content += `   المعنى: ${word.meaning}\n`;
            content += `   الأرمينية: ${word.armenian}\n\n`;
        });
        
        content += '\n';
    }
    
    // تحميل الملف
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'armenian_vocabulary.txt';
    a.click();
    URL.revokeObjectURL(url);
}

// إضافة زر التصدير إلى الواجهة
document.addEventListener('DOMContentLoaded', function() {
    const exportBtn = document.createElement('button');
    exportBtn.textContent = '📥 تصدير المفردات';
    exportBtn.style.cssText = `
        position: fixed;
        top: 20px;
        left: 20px;
        background: #667eea;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 25px;
        cursor: pointer;
        font-family: 'Cairo', sans-serif;
        font-weight: 500;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        z-index: 1000;
    `;
    
    exportBtn.addEventListener('click', exportVocabulary);
    exportBtn.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px)';
        this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
    });
    
    exportBtn.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
    });
    
    document.body.appendChild(exportBtn);
});