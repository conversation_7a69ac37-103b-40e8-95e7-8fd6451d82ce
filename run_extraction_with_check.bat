@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    استخراج شامل مع فحص المتطلبات
echo    Complete Extraction with Requirements Check
echo ========================================
echo.

REM التحقق من وجود Python
echo 🔍 التحقق من تثبيت Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo.
    echo 🐍 يجب تثبيت Python أولاً لتشغيل الاستخراج
    echo.
    echo 📥 خيارات التثبيت:
    echo    1. تثبيت تلقائي: انقر نقراً مزدوجاً على install_python_auto.bat
    echo    2. تثبيت يدوي: اقرأ ملف INSTALL_PYTHON.md
    echo.
    set /p choice="هل تريد فتح دليل التثبيت؟ (y/n): "
    if /i "%choice%"=="y" (
        start INSTALL_PYTHON.md
    )
    echo.
    pause
    exit /b 1
)

echo ✅ Python مثبت: 
python --version

echo.
echo 📦 التحقق من المكتبات المطلوبة...

REM التحقق من المكتبات
python -c "import selenium" >nul 2>&1
if errorlevel 1 (
    echo ❌ مكتبة selenium غير مثبتة
    goto install_packages
)

python -c "import bs4" >nul 2>&1
if errorlevel 1 (
    echo ❌ مكتبة beautifulsoup4 غير مثبتة
    goto install_packages
)

python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo ❌ مكتبة requests غير مثبتة
    goto install_packages
)

echo ✅ جميع المكتبات مثبتة
goto start_extraction

:install_packages
echo.
echo 📦 تثبيت المكتبات المطلوبة...
pip install selenium beautifulsoup4 requests webdriver-manager gtts pygame

if errorlevel 1 (
    echo ❌ فشل في تثبيت المكتبات
    echo 💡 جرب تشغيل Command Prompt كمدير وأدخل:
    echo    pip install --user selenium beautifulsoup4 requests webdriver-manager gtts pygame
    echo.
    pause
    exit /b 1
)

echo ✅ تم تثبيت المكتبات بنجاح

:start_extraction
echo.
echo 🚀 بدء الاستخراج الشامل للـ 125 درس...
echo 🔤 سيتم إضافة النطق العربي لكل كلمة لاتينية
echo 🎵 سيتم استخراج الصوتيات من كل درس
echo ⏳ هذا قد يستغرق 45-60 دقيقة...
echo.
echo أمثلة على النطق العربي:
echo   Meg → ميك
echo   Barev → باريف
echo   Bari luys → باري لويس
echo.

set /p confirm="هل تريد المتابعة؟ (y/n): "
if /i "%confirm%" neq "y" goto :end

echo.
echo 🔍 بدء استخراج جميع الـ 125 درس...
python advanced_scraper.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء الاستخراج
    echo 💡 تحقق من:
    echo    - اتصال الإنترنت
    echo    - عدم حظر الموقع
    echo    - عمل برامج مكافحة الفيروسات
    echo.
    pause
    exit /b 1
)

echo.
echo 🎉 تم الانتهاء من الاستخراج بنجاح!
echo.
echo 📁 الملفات المُنشأة:
echo    - vocabulary_data_complete.js (مع النطق العربي)
echo    - vocabulary_data_complete.json
echo    - armenian_vocabulary_complete.txt
echo.
echo 🎵 لاستخراج الصوتيات:
echo    انقر نقراً مزدوجاً على extract_audio_only.bat
echo.
echo 🌐 لتشغيل التطبيق:
echo    انقر نقراً مزدوجاً على run_app.bat
echo.

:end
pause
