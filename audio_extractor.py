#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مستخرج الصوتيات المتقدم
Advanced Audio Extractor for Armenian Learning App
"""

import os
import requests
import json
import time
from urllib.parse import urljoin, urlparse
import re

class AudioExtractor:
    def __init__(self):
        self.audio_dir = "audio"
        self.downloaded_count = 0
        self.failed_count = 0
        self.session = requests.Session()
        
        # إنشاء مجلد الصوتيات
        if not os.path.exists(self.audio_dir):
            os.makedirs(self.audio_dir)
            print(f"📁 تم إنشاء مجلد {self.audio_dir}")
    
    def download_audio_file(self, audio_url, filename):
        """تحميل ملف صوتي واحد"""
        try:
            # تنظيف اسم الملف
            safe_filename = re.sub(r'[^\w\-_.]', '_', filename)
            if not safe_filename.endswith('.mp3'):
                safe_filename += '.mp3'
            
            file_path = os.path.join(self.audio_dir, safe_filename)
            
            # تحقق من وجود الملف
            if os.path.exists(file_path):
                print(f"⏭️ {safe_filename} موجود مسبقاً")
                return True
            
            # تحميل الملف
            print(f"⬇️ تحميل {safe_filename}...")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = self.session.get(audio_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # حفظ الملف
            with open(file_path, 'wb') as f:
                f.write(response.content)
            
            self.downloaded_count += 1
            print(f"✅ تم تحميل {safe_filename}")
            return True
            
        except Exception as e:
            self.failed_count += 1
            print(f"❌ فشل تحميل {filename}: {e}")
            return False
    
    def extract_audio_from_vocabulary(self, vocabulary_file):
        """استخراج الصوتيات من ملف المفردات"""
        try:
            print(f"🎵 بدء استخراج الصوتيات من {vocabulary_file}")
            
            # تحميل بيانات المفردات
            with open(vocabulary_file, 'r', encoding='utf-8') as f:
                vocabulary_data = json.load(f)
            
            total_audio_files = 0
            
            # حساب إجمالي الملفات الصوتية
            for category, words in vocabulary_data.items():
                for word in words:
                    if word.get('audio_url'):
                        total_audio_files += 1
            
            print(f"📊 وُجد {total_audio_files} ملف صوتي للتحميل")
            
            if total_audio_files == 0:
                print("⚠️ لا توجد ملفات صوتية للتحميل")
                return
            
            # تحميل الملفات
            current_file = 0
            for category, words in vocabulary_data.items():
                print(f"\n📚 معالجة فئة: {category}")
                
                for word in words:
                    audio_url = word.get('audio_url')
                    if audio_url:
                        current_file += 1
                        print(f"[{current_file}/{total_audio_files}] ", end="")
                        
                        # إنشاء اسم الملف
                        filename = word.get('audio', word.get('phonetic', 'unknown'))
                        
                        # تحميل الملف
                        self.download_audio_file(audio_url, filename)
                        
                        # توقف قصير بين التحميلات
                        time.sleep(1)
            
            # إحصائيات نهائية
            print(f"\n📊 إحصائيات التحميل:")
            print(f"   ✅ تم تحميل: {self.downloaded_count}")
            print(f"   ❌ فشل: {self.failed_count}")
            print(f"   📁 المجلد: {self.audio_dir}")
            
        except Exception as e:
            print(f"❌ خطأ في استخراج الصوتيات: {e}")
    
    def generate_audio_with_tts(self, vocabulary_file):
        """إنشاء ملفات صوتية باستخدام TTS للكلمات المفقودة"""
        try:
            from gtts import gTTS
            import pygame
            
            print(f"🎤 بدء إنشاء ملفات صوتية بـ TTS")
            
            # تحميل بيانات المفردات
            with open(vocabulary_file, 'r', encoding='utf-8') as f:
                vocabulary_data = json.load(f)
            
            generated_count = 0
            
            for category, words in vocabulary_data.items():
                for word in words:
                    phonetic = word.get('phonetic', '')
                    audio_filename = word.get('audio', '')
                    
                    if phonetic and audio_filename:
                        file_path = os.path.join(self.audio_dir, f"{audio_filename}.mp3")
                        
                        # إنشاء الملف إذا لم يكن موجوداً
                        if not os.path.exists(file_path):
                            try:
                                print(f"🎤 إنشاء صوت لـ: {phonetic}")
                                
                                # إنشاء TTS
                                tts = gTTS(text=phonetic, lang='en', slow=False)
                                tts.save(file_path)
                                
                                generated_count += 1
                                print(f"✅ تم إنشاء {audio_filename}.mp3")
                                
                                time.sleep(0.5)
                                
                            except Exception as e:
                                print(f"❌ فشل إنشاء صوت لـ {phonetic}: {e}")
            
            print(f"\n🎤 تم إنشاء {generated_count} ملف صوتي بـ TTS")
            
        except ImportError:
            print("⚠️ مكتبة gTTS غير مثبتة. تثبيت...")
            os.system("pip install gtts pygame")
            print("✅ تم تثبيت المكتبات. أعد تشغيل السكريبت.")
        except Exception as e:
            print(f"❌ خطأ في إنشاء الصوتيات: {e}")
    
    def create_audio_index(self):
        """إنشاء فهرس للملفات الصوتية"""
        try:
            audio_files = []
            
            for filename in os.listdir(self.audio_dir):
                if filename.endswith('.mp3'):
                    file_path = os.path.join(self.audio_dir, filename)
                    file_size = os.path.getsize(file_path)
                    
                    audio_files.append({
                        'filename': filename,
                        'name': filename.replace('.mp3', ''),
                        'size': file_size,
                        'path': f"audio/{filename}"
                    })
            
            # حفظ الفهرس
            index_file = os.path.join(self.audio_dir, 'audio_index.json')
            with open(index_file, 'w', encoding='utf-8') as f:
                json.dump(audio_files, f, ensure_ascii=False, indent=2)
            
            print(f"📋 تم إنشاء فهرس الصوتيات: {len(audio_files)} ملف")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء فهرس الصوتيات: {e}")

def main():
    """الدالة الرئيسية"""
    print("🎵 مستخرج الصوتيات المتقدم")
    print("=" * 50)
    
    extractor = AudioExtractor()
    
    # البحث عن ملف المفردات
    vocabulary_files = [
        'vocabulary_data_complete.json',
        'vocabulary_data.json'
    ]
    
    vocabulary_file = None
    for file in vocabulary_files:
        if os.path.exists(file):
            vocabulary_file = file
            break
    
    if not vocabulary_file:
        print("❌ لم يتم العثور على ملف المفردات")
        print("💡 تأكد من تشغيل الاستخراج أولاً")
        return
    
    print(f"📁 استخدام ملف: {vocabulary_file}")
    
    # استخراج الصوتيات من الروابط
    extractor.extract_audio_from_vocabulary(vocabulary_file)
    
    # إنشاء ملفات صوتية للكلمات المفقودة
    print(f"\n🎤 إنشاء ملفات صوتية للكلمات المفقودة...")
    extractor.generate_audio_with_tts(vocabulary_file)
    
    # إنشاء فهرس الصوتيات
    extractor.create_audio_index()
    
    print(f"\n🎉 انتهى استخراج الصوتيات!")

if __name__ == "__main__":
    main()
