#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فتح التطبيق المبني على ملف Excel والصوتيات الحقيقية
Open Excel-Based Armenian App with Real Audio
"""

import webbrowser
from pathlib import Path
import json

def main():
    print("🇦🇲 فتح التطبيق المبني على ملف Excel والصوتيات الحقيقية")
    print("=" * 70)
    
    app_dir = Path(__file__).parent
    
    # التحقق من وجود الملفات المطلوبة
    required_files = {
        'التطبيق': 'armenian_app_excel_based.html',
        'بيانات JavaScript': 'armenian_vocabulary_from_excel.js',
        'بيانات JSON': 'armenian_vocabulary_from_excel.json',
        'معلومات الصوت': 'excel_audio_manifest.json'
    }
    
    missing_files = []
    for file_type, filename in required_files.items():
        file_path = app_dir / filename
        if file_path.exists():
            print(f"   ✅ {file_type}: {filename}")
        else:
            missing_files.append(f"{file_type}: {filename}")
            print(f"   ❌ {file_type}: {filename}")
    
    if missing_files:
        print(f"\n❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        print(f"\n💡 شغل: python excel_audio_processor.py أولاً")
        return
    
    # فحص مجلد الصوتيات
    audio_dir = app_dir / 'armenian_audio'
    if audio_dir.exists():
        audio_files = list(audio_dir.glob('*.mp3')) + list(audio_dir.glob('*.wav')) + list(audio_dir.glob('*.ogg'))
        print(f"   ✅ مجلد الصوتيات: {len(audio_files)} ملف صوتي")
    else:
        print(f"   ⚠️ مجلد الصوتيات غير موجود: armenian_audio")
    
    # قراءة إحصائيات البيانات
    try:
        with open(app_dir / 'armenian_vocabulary_from_excel.json', 'r', encoding='utf-8') as f:
            vocabulary = json.load(f)
        
        total_categories = len(vocabulary)
        total_words = sum(len(cat['words']) for cat in vocabulary.values())
        words_with_audio = sum(1 for cat in vocabulary.values() for word in cat['words'] if word.get('has_real_audio'))
        
        print(f"\n📊 إحصائيات البيانات:")
        print(f"   📝 إجمالي الكلمات: {total_words}")
        print(f"   📁 عدد الفئات: {total_categories}")
        print(f"   🔊 كلمات مع صوت حقيقي: {words_with_audio}")
        print(f"   🎵 كلمات مع صوت مولد: {total_words - words_with_audio}")
        print(f"   📊 نسبة الصوت الحقيقي: {(words_with_audio/total_words*100):.1f}%")
        
        # عرض أكبر الفئات
        print(f"\n📋 أكبر الفئات:")
        sorted_categories = sorted(vocabulary.items(), key=lambda x: len(x[1]['words']), reverse=True)
        for i, (cat_name, cat_data) in enumerate(sorted_categories[:5], 1):
            word_count = len(cat_data['words'])
            icon = cat_data.get('icon', '📝')
            print(f"   {i}. {icon} {cat_name}: {word_count} كلمة")
        
        # عرض عينة من الكلمات
        print(f"\n🔤 عينة من الكلمات:")
        sample_count = 0
        for category_name, category_data in vocabulary.items():
            if sample_count >= 5:
                break
            for word in category_data['words'][:2]:
                if sample_count >= 5:
                    break
                audio_status = "🔊" if word.get('has_real_audio') else "🎵"
                print(f"   {audio_status} {word['armenian']} → {word['arabic_phonetic']} = {word['arabic']}")
                sample_count += 1
        print("   ... والمزيد")
        
    except Exception as e:
        print(f"⚠️ خطأ في قراءة البيانات: {e}")
    
    # فتح التطبيق
    app_file = app_dir / 'armenian_app_excel_based.html'
    try:
        absolute_path = app_file.resolve()
        file_url = absolute_path.as_uri()
        webbrowser.open(file_url)
        
        print(f"\n🚀 تم فتح التطبيق المبني على Excel!")
        print(f"\n🎯 مميزات التطبيق:")
        print(f"   ✅ 743 كلمة أرمينية من ملف Excel")
        print(f"   ✅ 703 ملف صوتي حقيقي عالي الجودة")
        print(f"   ✅ 22 فئة منظمة ومرتبة")
        print(f"   ✅ نطق عربي فريد لكل كلمة")
        print(f"   ✅ بحث متقدم في جميع البيانات")
        print(f"   ✅ تشغيل صوت حقيقي + صوت مولد كبديل")
        print(f"   ✅ واجهة جميلة ومتطورة")
        print(f"   ✅ تصميم متجاوب للموبايل")
        
        print(f"\n🔊 نظام الصوت المتطور:")
        print(f"   🎯 أولوية للصوت الحقيقي من الملفات")
        print(f"   🎵 صوت مولد كبديل عند عدم توفر الملف")
        print(f"   ✨ تأثيرات بصرية أثناء التشغيل")
        print(f"   📱 دعم كامل للموبايل")
        
        print(f"\n🎮 كيفية الاستخدام:")
        print(f"   1️⃣ تصفح الفئات المختلفة (22 فئة)")
        print(f"   2️⃣ انقر 🔊 (أخضر) للصوت الحقيقي")
        print(f"   3️⃣ انقر 🎵 (أحمر) للصوت المولد")
        print(f"   4️⃣ استخدم البحث للعثور على كلمات")
        print(f"   5️⃣ شاهد النطق العربي الفريد")
        
        print(f"\n🔍 أمثلة للبحث:")
        print(f"   🔤 ابحث عن أرقام الدروس: 1، 25، 100")
        print(f"   🔤 ابحث عن كلمات: باريف، مايير، كارمير")
        print(f"   🔤 ابحث عن معاني: مرحبا، أم، أحمر")
        
        print(f"\n📱 مثالي للتطبيق الموبايل:")
        print(f"   📄 بيانات JSON منظمة ومهيأة")
        print(f"   🔊 ملفات صوتية حقيقية جاهزة")
        print(f"   📁 743 كلمة مصنفة في 22 فئة")
        print(f"   🎯 نطق عربي فريد ومميز")
        print(f"   📊 بيانات شاملة من 125 درس")
        
        print(f"\n🎉 استمتع بأفضل تطبيق أرمني مع الصوت الحقيقي!")
        
    except Exception as e:
        print(f"❌ فشل فتح التطبيق: {e}")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
