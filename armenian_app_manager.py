#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير تطبيق تعلم الأرمينية
Armenian Learning App Manager
"""

import os
import sys
import subprocess
import json
import webbrowser
import time
from pathlib import Path

class ArmenianAppManager:
    def __init__(self):
        self.app_dir = Path(__file__).parent.absolute()
        self.python_cmd = self.detect_python()
        
    def detect_python(self):
        """اكتشاف أمر Python الصحيح"""
        for cmd in ['py', 'python', 'python3']:
            try:
                result = subprocess.run([cmd, '--version'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    return cmd
            except:
                continue
        return None
    
    def print_header(self):
        """طباعة رأس التطبيق"""
        print("=" * 60)
        print("🇦🇲 مدير تطبيق تعلم اللغة الأرمينية")
        print("   Armenian Learning App Manager")
        print("=" * 60)
        print()
    
    def check_python(self):
        """فحص Python"""
        print("🐍 فحص Python...")
        if not self.python_cmd:
            print("❌ Python غير مثبت أو غير متاح في PATH")
            print()
            print("💡 لتثبيت Python:")
            print("   1. اذهب إلى: https://python.org/downloads")
            print("   2. حمل أحدث إصدار")
            print("   3. تأكد من تحديد 'Add Python to PATH'")
            print("   4. أعد تشغيل الكمبيوتر")
            return False
        
        try:
            result = subprocess.run([self.python_cmd, '--version'], 
                                  capture_output=True, text=True)
            version = result.stdout.strip()
            print(f"✅ {version}")
            return True
        except Exception as e:
            print(f"❌ خطأ في Python: {e}")
            return False
    
    def install_packages(self):
        """تثبيت المكتبات المطلوبة"""
        print("\n📦 تثبيت المكتبات المطلوبة...")
        
        packages = [
            'requests',
            'beautifulsoup4', 
            'selenium',
            'webdriver-manager'
        ]
        
        for package in packages:
            print(f"   📥 تثبيت {package}...")
            try:
                result = subprocess.run([
                    self.python_cmd, '-m', 'pip', 'install', 
                    package, '--quiet', '--disable-pip-version-check'
                ], capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    print(f"   ✅ {package}")
                else:
                    # محاولة بصلاحيات المستخدم
                    result = subprocess.run([
                        self.python_cmd, '-m', 'pip', 'install', 
                        package, '--user', '--quiet', '--disable-pip-version-check'
                    ], capture_output=True, text=True, timeout=60)
                    
                    if result.returncode == 0:
                        print(f"   ✅ {package} (user)")
                    else:
                        print(f"   ⚠️ {package} - قد يكون مثبت مسبقاً")
                        
            except Exception as e:
                print(f"   ❌ خطأ في تثبيت {package}: {e}")
        
        print("✅ انتهى تثبيت المكتبات")
    
    def check_files(self):
        """فحص الملفات المطلوبة"""
        print("\n📁 فحص الملفات...")
        
        required_files = {
            'index.html': 'التطبيق الرئيسي',
            'script.js': 'منطق التطبيق', 
            'style.css': 'تنسيقات التطبيق',
            'demo_extracted_data.html': 'عرض البيانات'
        }
        
        scraper_files = {
            'advanced_scraper.py': 'المستخرج المتقدم',
            'simple_scraper.py': 'المستخرج المبسط'
        }
        
        # فحص ملفات التطبيق
        for file, desc in required_files.items():
            file_path = self.app_dir / file
            if file_path.exists():
                size = file_path.stat().st_size
                print(f"   ✅ {file} ({size:,} بايت) - {desc}")
            else:
                print(f"   ❌ {file} مفقود - {desc}")
        
        # فحص ملفات الاستخراج
        scraper_available = False
        for file, desc in scraper_files.items():
            file_path = self.app_dir / file
            if file_path.exists():
                print(f"   ✅ {file} - {desc}")
                scraper_available = True
        
        if not scraper_available:
            print("   ❌ لا توجد ملفات استخراج")
        
        return scraper_available
    
    def check_extracted_data(self):
        """فحص البيانات المستخرجة"""
        print("\n📊 فحص البيانات المستخرجة...")
        
        json_file = self.app_dir / 'vocabulary_data_complete.json'
        js_file = self.app_dir / 'vocabulary_data_complete.js'
        
        if json_file.exists():
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                lessons = len(data)
                total_words = sum(len(words) for words in data.values())
                words_with_arabic = sum(
                    1 for words in data.values() 
                    for word in words 
                    if word.get('arabic_phonetic')
                )
                
                print(f"   ✅ البيانات موجودة:")
                print(f"      📚 {lessons} درس")
                print(f"      📝 {total_words} كلمة")
                print(f"      🔤 {words_with_arabic} كلمة بنطق عربي")
                
                return True, lessons, total_words
                
            except Exception as e:
                print(f"   ❌ خطأ في قراءة البيانات: {e}")
                return False, 0, 0
        else:
            print("   ❌ لا توجد بيانات مستخرجة")
            return False, 0, 0
    
    def test_internet(self):
        """اختبار الاتصال بالإنترنت"""
        print("\n🌐 فحص الاتصال بالإنترنت...")
        try:
            import requests
            response = requests.get('https://www.google.com', timeout=10)
            if response.status_code == 200:
                print("   ✅ الإنترنت يعمل")
                return True
            else:
                print(f"   ⚠️ مشكلة في الاتصال: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ لا يوجد اتصال: {e}")
            return False
    
    def run_extraction(self):
        """تشغيل عملية الاستخراج"""
        print("\n🚀 بدء استخراج المفردات الأرمينية...")
        print("🔤 سيتم إضافة النطق العربي لكل كلمة")
        print("⏳ قد يستغرق 30-60 دقيقة...")
        print()
        
        # اختيار المستخرج
        advanced_scraper = self.app_dir / 'advanced_scraper.py'
        simple_scraper = self.app_dir / 'simple_scraper.py'
        
        if advanced_scraper.exists():
            scraper_file = advanced_scraper
            print("📈 استخدام المستخرج المتقدم...")
        elif simple_scraper.exists():
            scraper_file = simple_scraper
            print("📊 استخدام المستخرج المبسط...")
        else:
            print("❌ لا توجد ملفات استخراج")
            return False
        
        try:
            # تشغيل المستخرج
            process = subprocess.Popen([
                self.python_cmd, str(scraper_file)
            ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
               text=True, bufsize=1, universal_newlines=True)
            
            # عرض المخرجات في الوقت الفعلي
            for line in process.stdout:
                print(line.rstrip())
            
            process.wait()
            
            if process.returncode == 0:
                print("\n✅ تم الانتهاء من الاستخراج بنجاح!")
                return True
            else:
                print(f"\n❌ فشل الاستخراج (كود الخطأ: {process.returncode})")
                return False
                
        except Exception as e:
            print(f"\n❌ خطأ في تشغيل الاستخراج: {e}")
            return False
    
    def open_app(self):
        """فتح التطبيق في المتصفح"""
        print("\n🌐 فتح التطبيق في المتصفح...")
        
        # قائمة الملفات للفتح
        files_to_open = []
        
        # التطبيق الرئيسي
        index_file = self.app_dir / 'index.html'
        if index_file.exists():
            files_to_open.append(('التطبيق الرئيسي', index_file))
        
        # عرض البيانات المستخرجة
        demo_file = self.app_dir / 'demo_extracted_data.html'
        if demo_file.exists():
            files_to_open.append(('عرض البيانات المستخرجة', demo_file))
        
        if not files_to_open:
            print("❌ لا توجد ملفات HTML للفتح")
            return False
        
        # فتح الملفات
        for name, file_path in files_to_open:
            try:
                file_url = file_path.as_uri()
                webbrowser.open(file_url)
                print(f"   ✅ تم فتح {name}")
                time.sleep(1)  # توقف قصير بين الفتحات
            except Exception as e:
                print(f"   ❌ فشل فتح {name}: {e}")
        
        return True
    
    def show_menu(self):
        """عرض القائمة الرئيسية"""
        print("\n🎯 اختر العملية المطلوبة:")
        print("   1. فحص النظام والملفات")
        print("   2. استخراج المفردات من الموقع")
        print("   3. فتح التطبيق في المتصفح")
        print("   4. فحص البيانات المستخرجة")
        print("   5. تثبيت المكتبات المطلوبة")
        print("   6. خروج")
        print()
        
        try:
            choice = input("أدخل رقم الخيار (1-6): ").strip()
            return choice
        except KeyboardInterrupt:
            print("\n🛑 تم إلغاء العملية")
            return "6"
    
    def run_system_check(self):
        """فحص شامل للنظام"""
        print("\n🔍 فحص شامل للنظام...")
        print("-" * 40)
        
        # فحص Python
        python_ok = self.check_python()
        
        # فحص الملفات
        files_ok = self.check_files()
        
        # فحص البيانات
        data_ok, lessons, words = self.check_extracted_data()
        
        # فحص الإنترنت
        internet_ok = self.test_internet()
        
        # النتيجة
        print("\n📋 ملخص الفحص:")
        print(f"   🐍 Python: {'✅' if python_ok else '❌'}")
        print(f"   📁 الملفات: {'✅' if files_ok else '❌'}")
        print(f"   📊 البيانات: {'✅' if data_ok else '❌'}")
        print(f"   🌐 الإنترنت: {'✅' if internet_ok else '❌'}")
        
        if data_ok:
            print(f"\n🎉 التطبيق جاهز للاستخدام!")
            print(f"   📚 {lessons} درس متاح")
            print(f"   📝 {words} كلمة مع النطق العربي")
        elif files_ok and internet_ok:
            print(f"\n⚠️ تحتاج لاستخراج البيانات أولاً")
        else:
            print(f"\n❌ يوجد مشاكل تحتاج حل")
        
        return python_ok, files_ok, data_ok, internet_ok
    
    def main_loop(self):
        """الحلقة الرئيسية للتطبيق"""
        self.print_header()
        
        while True:
            choice = self.show_menu()
            
            if choice == "1":
                self.run_system_check()
                
            elif choice == "2":
                # فحص المتطلبات أولاً
                if not self.check_python():
                    continue
                if not self.test_internet():
                    print("❌ يجب توفر اتصال إنترنت للاستخراج")
                    continue
                
                # بدء الاستخراج
                if self.run_extraction():
                    # فتح التطبيق بعد الاستخراج
                    input("\nاضغط Enter لفتح التطبيق...")
                    self.open_app()
                
            elif choice == "3":
                self.open_app()
                
            elif choice == "4":
                self.check_extracted_data()
                
            elif choice == "5":
                if self.check_python():
                    self.install_packages()
                
            elif choice == "6":
                print("\n👋 شكراً لاستخدام تطبيق تعلم الأرمينية!")
                break
                
            else:
                print("❌ خيار غير صحيح، جرب مرة أخرى")
            
            print("\n" + "-" * 60)

def main():
    """الدالة الرئيسية"""
    try:
        manager = ArmenianAppManager()
        manager.main_loop()
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف التطبيق")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
