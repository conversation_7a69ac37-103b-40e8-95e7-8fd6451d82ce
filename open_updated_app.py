#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فتح التطبيق المحدث مع ضمان تحميل البيانات
Open Updated App with Data Loading Guarantee
"""

import os
import webbrowser
import json
from pathlib import Path
import time

def main():
    print("🇦🇲 فتح تطبيق تعلم الأرمينية المحدث")
    print("=" * 45)
    
    app_dir = Path(__file__).parent
    
    # فحص البيانات أولاً
    json_file = app_dir / 'vocabulary_data_complete.json'
    if json_file.exists():
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            lessons = len(data)
            words = sum(len(w) for w in data.values())
            words_with_arabic = sum(
                1 for words_list in data.values() 
                for word in words_list 
                if word.get('arabic_phonetic')
            )
            
            print(f"✅ البيانات المحدثة متوفرة:")
            print(f"   📚 {lessons} درس")
            print(f"   📝 {words} كلمة")
            print(f"   🔤 {words_with_arabic} كلمة بنطق عربي")
            print()
            
        except Exception as e:
            print(f"⚠️ خطأ في قراءة البيانات: {e}")
    else:
        print("❌ ملف البيانات غير موجود")
        print("💡 شغل: python smart_extractor.py")
        return
    
    # فتح التطبيق المحدث
    app_file = app_dir / 'app_updated.html'
    if app_file.exists():
        try:
            file_url = app_file.as_uri()
            webbrowser.open(file_url)
            print("🚀 تم فتح التطبيق المحدث!")
            print()
            print("🎯 ميزات التطبيق المحدث:")
            print("   🔍 بحث متقدم بالنطق العربي")
            print("   📊 إحصائيات دقيقة")
            print("   🎨 واجهة محسنة")
            print("   📱 تصميم متجاوب")
            print()
            print("🔤 جرب البحث عن:")
            print("   - ميك (للبحث عن رقم 1)")
            print("   - باريف (للبحث عن مرحبا)")
            print("   - تشورس (للبحث عن رقم 4)")
            
        except Exception as e:
            print(f"❌ فشل فتح التطبيق: {e}")
    else:
        print("❌ ملف التطبيق المحدث غير موجود")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
