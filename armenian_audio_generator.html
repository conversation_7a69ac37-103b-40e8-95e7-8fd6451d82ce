<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>🔊 مولد الصوت الأرمني</title>
    <style>
        body { font-family: Arial; padding: 20px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .word { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { background: #4CAF50; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #45a049; }
        .progress { background: #ddd; border-radius: 10px; margin: 20px 0; }
        .progress-bar { background: #4CAF50; height: 20px; border-radius: 10px; width: 0%; transition: width 0.3s; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔊 مولد الصوت الأرمني</h1>
        <p>توليد ملفات صوتية لـ 110 كلمة أرمينية</p>
        
        <button class="btn" onclick="generateAll()">🎵 توليد جميع الأصوات</button>
        <button class="btn" onclick="downloadData()">📥 تحميل البيانات</button>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div id="status">جاهز للبدء...</div>
        <div id="wordsList"></div>
    </div>

    <script>
        const vocabulary = {"التحيات والمجاملات": {"icon": "👋", "description": "كلمات الترحيب والتحية والمجاملات", "words": [{"armenian": "Բարև", "phonetic": "Barev", "arabic": "مرحبا", "arabic_phonetic": "باريف", "audio": "barev", "meaning": "كلمة من فئة التحيات والمجاملات", "lesson": "فئة التحيات والمجاملات"}, {"armenian": "Բարի լույս", "phonetic": "Bari luys", "arabic": "صباح الخير", "arabic_phonetic": "باري لويس", "audio": "bari-luys", "meaning": "كلمة من فئة التحيات والمجاملات", "lesson": "فئة التحيات والمجاملات"}, {"armenian": "Բարի երեկո", "phonetic": "Bari yereko", "arabic": "مساء الخير", "arabic_phonetic": "باري ييريكو", "audio": "bari-yereko", "meaning": "كلمة من فئة التحيات والمجاملات", "lesson": "فئة التحيات والمجاملات"}, {"armenian": "Բարի գիշեր", "phonetic": "Bari gisher", "arabic": "تصبح على خير", "arabic_phonetic": "باري جيشير", "audio": "bari-gisher", "meaning": "كلمة من فئة التحيات والمجاملات", "lesson": "فئة التحيات والمجاملات"}, {"armenian": "Ցտեսություն", "phonetic": "Tsdesutyun", "arabic": "وداعاً", "arabic_phonetic": "تسديسوتيون", "audio": "tsdesutyun", "meaning": "كلمة من فئة التحيات والمجاملات", "lesson": "فئة التحيات والمجاملات"}, {"armenian": "Շնորհակալություն", "phonetic": "Shnorhakalutyun", "arabic": "شكراً", "arabic_phonetic": "شنورهاكالوتيون", "audio": "shnorhakalutyun", "meaning": "كلمة من فئة التحيات والمجاملات", "lesson": "فئة التحيات والمجاملات"}, {"armenian": "Ներողություն", "phonetic": "Neroghutyan", "arabic": "عذراً", "arabic_phonetic": "نيروغوتيان", "audio": "neroghutyan", "meaning": "كلمة من فئة التحيات والمجاملات", "lesson": "فئة التحيات والمجاملات"}, {"armenian": "Խնդրեմ", "phonetic": "Khndrem", "arabic": "من فضلك", "arabic_phonetic": "خندريم", "audio": "khndrem", "meaning": "كلمة من فئة التحيات والمجاملات", "lesson": "فئة التحيات والمجاملات"}, {"armenian": "Ինչպես եք", "phonetic": "Inchpes eq", "arabic": "كيف حالك", "arabic_phonetic": "ينتشبيس يq", "audio": "inchpes-eq", "meaning": "كلمة من فئة التحيات والمجاملات", "lesson": "فئة التحيات والمجاملات"}, {"armenian": "Լավ եմ", "phonetic": "Lav em", "arabic": "أنا بخير", "arabic_phonetic": "لاف يم", "audio": "lav-em", "meaning": "كلمة من فئة التحيات والمجاملات", "lesson": "فئة التحيات والمجاملات"}]}, "الأرقام والحساب": {"icon": "🔢", "description": "الأرقام من 1 إلى 100", "words": [{"armenian": "Մեկ", "phonetic": "Mek", "arabic": "واحد", "arabic_phonetic": "ميك", "audio": "mek", "meaning": "كلمة من فئة الأرقام والحساب", "lesson": "فئة الأرقام والحساب"}, {"armenian": "Երկու", "phonetic": "Yerku", "arabic": "اثنان", "arabic_phonetic": "ييركو", "audio": "yerku", "meaning": "كلمة من فئة الأرقام والحساب", "lesson": "فئة الأرقام والحساب"}, {"armenian": "Երեք", "phonetic": "Yereq", "arabic": "ثلاثة", "arabic_phonetic": "ييريك", "audio": "yereq", "meaning": "كلمة من فئة الأرقام والحساب", "lesson": "فئة الأرقام والحساب"}, {"armenian": "Չորս", "phonetic": "Chors", "arabic": "أربعة", "arabic_phonetic": "تشورس", "audio": "chors", "meaning": "كلمة من فئة الأرقام والحساب", "lesson": "فئة الأرقام والحساب"}, {"armenian": "Հինգ", "phonetic": "Hing", "arabic": "خمسة", "arabic_phonetic": "هينغ", "audio": "hing", "meaning": "كلمة من فئة الأرقام والحساب", "lesson": "فئة الأرقام والحساب"}, {"armenian": "Վեց", "phonetic": "Vets", "arabic": "ستة", "arabic_phonetic": "فيتس", "audio": "vets", "meaning": "كلمة من فئة الأرقام والحساب", "lesson": "فئة الأرقام والحساب"}, {"armenian": "Յոթ", "phonetic": "Yot", "arabic": "سبعة", "arabic_phonetic": "يوت", "audio": "yot", "meaning": "كلمة من فئة الأرقام والحساب", "lesson": "فئة الأرقام والحساب"}, {"armenian": "Ութ", "phonetic": "Ut", "arabic": "ثمانية", "arabic_phonetic": "اوت", "audio": "ut", "meaning": "كلمة من فئة الأرقام والحساب", "lesson": "فئة الأرقام والحساب"}, {"armenian": "Ինը", "phonetic": "Iny", "arabic": "تسعة", "arabic_phonetic": "يني", "audio": "iny", "meaning": "كلمة من فئة الأرقام والحساب", "lesson": "فئة الأرقام والحساب"}, {"armenian": "Տաս", "phonetic": "Das", "arabic": "عشرة", "arabic_phonetic": "داس", "audio": "das", "meaning": "كلمة من فئة الأرقام والحساب", "lesson": "فئة الأرقام والحساب"}, {"armenian": "Տասնմեկ", "phonetic": "Dasnmek", "arabic": "أحد عشر", "arabic_phonetic": "داسنميك", "audio": "dasnmek", "meaning": "كلمة من فئة الأرقام والحساب", "lesson": "فئة الأرقام والحساب"}, {"armenian": "Տասներկու", "phonetic": "Dasnerku", "arabic": "اثنا عشر", "arabic_phonetic": "داسنيركو", "audio": "dasnerku", "meaning": "كلمة من فئة الأرقام والحساب", "lesson": "فئة الأرقام والحساب"}, {"armenian": "Քսան", "phonetic": "Ksan", "arabic": "عشرون", "arabic_phonetic": "كسان", "audio": "ksan", "meaning": "كلمة من فئة الأرقام والحساب", "lesson": "فئة الأرقام والحساب"}, {"armenian": "Երեսուն", "phonetic": "Yeresun", "arabic": "ثلاثون", "arabic_phonetic": "ييريسون", "audio": "yeresun", "meaning": "كلمة من فئة الأرقام والحساب", "lesson": "فئة الأرقام والحساب"}, {"armenian": "Հարյուր", "phonetic": "Haryur", "arabic": "مئة", "arabic_phonetic": "هاريور", "audio": "haryur", "meaning": "كلمة من فئة الأرقام والحساب", "lesson": "فئة الأرقام والحساب"}]}, "العائلة والأقارب": {"icon": "👨‍👩‍👧‍👦", "description": "أفراد العائلة والأقارب", "words": [{"armenian": "Մայր", "phonetic": "Mayr", "arabic": "أم", "arabic_phonetic": "مايير", "audio": "mayr", "meaning": "كلمة من فئة العائلة والأقارب", "lesson": "فئة العائلة والأقارب"}, {"armenian": "Հայր", "phonetic": "Hayr", "arabic": "أب", "arabic_phonetic": "هايير", "audio": "hayr", "meaning": "كلمة من فئة العائلة والأقارب", "lesson": "فئة العائلة والأقارب"}, {"armenian": "Եղբայր", "phonetic": "Yeghbayr", "arabic": "أخ", "arabic_phonetic": "ييغبايير", "audio": "yeghbayr", "meaning": "كلمة من فئة العائلة والأقارب", "lesson": "فئة العائلة والأقارب"}, {"armenian": "Քույր", "phonetic": "Khoyr", "arabic": "أخت", "arabic_phonetic": "خوير", "audio": "khoyr", "meaning": "كلمة من فئة العائلة والأقارب", "lesson": "فئة العائلة والأقارب"}, {"armenian": "Որդի", "phonetic": "Vordi", "arabic": "ابن", "arabic_phonetic": "فوردي", "audio": "vordi", "meaning": "كلمة من فئة العائلة والأقارب", "lesson": "فئة العائلة والأقارب"}, {"armenian": "Աղջիկ", "phonetic": "Aghjik", "arabic": "ابنة", "arabic_phonetic": "اغجيك", "audio": "aghjik", "meaning": "كلمة من فئة العائلة والأقارب", "lesson": "فئة العائلة والأقارب"}, {"armenian": "Պապ", "phonetic": "Pap", "arabic": "جد", "arabic_phonetic": "باب", "audio": "pap", "meaning": "كلمة من فئة العائلة والأقارب", "lesson": "فئة العائلة والأقارب"}, {"armenian": "Տատ", "phonetic": "Tat", "arabic": "جدة", "arabic_phonetic": "تات", "audio": "tat", "meaning": "كلمة من فئة العائلة والأقارب", "lesson": "فئة العائلة والأقارب"}, {"armenian": "Ամուսին", "phonetic": "Amusin", "arabic": "زوج", "arabic_phonetic": "اموسين", "audio": "amusin", "meaning": "كلمة من فئة العائلة والأقارب", "lesson": "فئة العائلة والأقارب"}, {"armenian": "Կին", "phonetic": "Kin", "arabic": "زوجة", "arabic_phonetic": "كين", "audio": "kin", "meaning": "كلمة من فئة العائلة والأقارب", "lesson": "فئة العائلة والأقارب"}, {"armenian": "Ընտանիք", "phonetic": "Entaniq", "arabic": "عائلة", "arabic_phonetic": "ينتانيq", "audio": "entaniq", "meaning": "كلمة من فئة العائلة والأقارب", "lesson": "فئة العائلة والأقارب"}, {"armenian": "Հորեղբայր", "phonetic": "Horeghbayr", "arabic": "عم", "arabic_phonetic": "هوريغباير", "audio": "horeghbayr", "meaning": "كلمة من فئة العائلة والأقارب", "lesson": "فئة العائلة والأقارب"}, {"armenian": "Մորեղբայր", "phonetic": "Moreghbayr", "arabic": "خال", "arabic_phonetic": "موريغباير", "audio": "moreghbayr", "meaning": "كلمة من فئة العائلة والأقارب", "lesson": "فئة العائلة والأقارب"}]}, "الألوان": {"icon": "🎨", "description": "الألوان الأساسية والثانوية", "words": [{"armenian": "Կարմիր", "phonetic": "Karmir", "arabic": "أحمر", "arabic_phonetic": "كارمير", "audio": "karmir", "meaning": "كلمة من فئة الألوان", "lesson": "فئة الألوان"}, {"armenian": "Կանաչ", "phonetic": "Kanach", "arabic": "أخضر", "arabic_phonetic": "كاناتش", "audio": "kanach", "meaning": "كلمة من فئة الألوان", "lesson": "فئة الألوان"}, {"armenian": "Կապույտ", "phonetic": "Kapuyt", "arabic": "أزرق", "arabic_phonetic": "كابويت", "audio": "kapuyt", "meaning": "كلمة من فئة الألوان", "lesson": "فئة الألوان"}, {"armenian": "Դեղին", "phonetic": "Deghin", "arabic": "أصفر", "arabic_phonetic": "ديغين", "audio": "deghin", "meaning": "كلمة من فئة الألوان", "lesson": "فئة الألوان"}, {"armenian": "Սև", "phonetic": "Sev", "arabic": "أسود", "arabic_phonetic": "سيف", "audio": "sev", "meaning": "كلمة من فئة الألوان", "lesson": "فئة الألوان"}, {"armenian": "Սպիտակ", "phonetic": "Spitak", "arabic": "أبيض", "arabic_phonetic": "سبيتاك", "audio": "spitak", "meaning": "كلمة من فئة الألوان", "lesson": "فئة الألوان"}, {"armenian": "Մանուշակագույն", "phonetic": "Manushakaguyn", "arabic": "بنفسجي", "arabic_phonetic": "مانوشاكاجوين", "audio": "manushakaguyn", "meaning": "كلمة من فئة الألوان", "lesson": "فئة الألوان"}, {"armenian": "Վարդագույն", "phonetic": "Vardaguyn", "arabic": "وردي", "arabic_phonetic": "فارداجوين", "audio": "vardaguyn", "meaning": "كلمة من فئة الألوان", "lesson": "فئة الألوان"}, {"armenian": "Նարնջագույն", "phonetic": "Narnjaguyn", "arabic": "برتقالي", "arabic_phonetic": "نارنجاجوين", "audio": "narnjaguyn", "meaning": "كلمة من فئة الألوان", "lesson": "فئة الألوان"}, {"armenian": "Գորշ", "phonetic": "Gorsh", "arabic": "رمادي", "arabic_phonetic": "جورش", "audio": "gorsh", "meaning": "كلمة من فئة الألوان", "lesson": "فئة الألوان"}]}, "الطعام والشراب": {"icon": "🍽️", "description": "الأطعمة والمشروبات", "words": [{"armenian": "Հաց", "phonetic": "Hats", "arabic": "خبز", "arabic_phonetic": "هاتس", "audio": "hats", "meaning": "كلمة من فئة الطعام والشراب", "lesson": "فئة الطعام والشراب"}, {"armenian": "Ջուր", "phonetic": "Jur", "arabic": "ماء", "arabic_phonetic": "جور", "audio": "jur", "meaning": "كلمة من فئة الطعام والشراب", "lesson": "فئة الطعام والشراب"}, {"armenian": "Կաթ", "phonetic": "Kat", "arabic": "حليب", "arabic_phonetic": "كات", "audio": "kat", "meaning": "كلمة من فئة الطعام والشراب", "lesson": "فئة الطعام والشراب"}, {"armenian": "Միս", "phonetic": "Mis", "arabic": "لحم", "arabic_phonetic": "ميس", "audio": "mis", "meaning": "كلمة من فئة الطعام والشراب", "lesson": "فئة الطعام والشراب"}, {"armenian": "Ձուկ", "phonetic": "Dzuk", "arabic": "سمك", "arabic_phonetic": "دزوك", "audio": "dzuk", "meaning": "كلمة من فئة الطعام والشراب", "lesson": "فئة الطعام والشراب"}, {"armenian": "Բանջարեղեն", "phonetic": "Banjareghen", "arabic": "خضار", "arabic_phonetic": "بانجاريغين", "audio": "banjareghen", "meaning": "كلمة من فئة الطعام والشراب", "lesson": "فئة الطعام والشراب"}, {"armenian": "Մրգեր", "phonetic": "Mrger", "arabic": "فواكه", "arabic_phonetic": "مرجير", "audio": "mrger", "meaning": "كلمة من فئة الطعام والشراب", "lesson": "فئة الطعام والشراب"}, {"armenian": "Բրինձ", "phonetic": "Brindz", "arabic": "أرز", "arabic_phonetic": "بريندز", "audio": "brindz", "meaning": "كلمة من فئة الطعام والشراب", "lesson": "فئة الطعام والشراب"}, {"armenian": "Թեյ", "phonetic": "Tey", "arabic": "شاي", "arabic_phonetic": "تيي", "audio": "tey", "meaning": "كلمة من فئة الطعام والشراب", "lesson": "فئة الطعام والشراب"}, {"armenian": "Սուրճ", "phonetic": "Surch", "arabic": "قهوة", "arabic_phonetic": "سورتش", "audio": "surch", "meaning": "كلمة من فئة الطعام والشراب", "lesson": "فئة الطعام والشراب"}, {"armenian": "Կարագ", "phonetic": "Karag", "arabic": "زبدة", "arabic_phonetic": "كاراج", "audio": "karag", "meaning": "كلمة من فئة الطعام والشراب", "lesson": "فئة الطعام والشراب"}, {"armenian": "Պանիր", "phonetic": "Panir", "arabic": "جبن", "arabic_phonetic": "بانير", "audio": "panir", "meaning": "كلمة من فئة الطعام والشراب", "lesson": "فئة الطعام والشراب"}]}, "الجسم وأجزاؤه": {"icon": "👤", "description": "أجزاء الجسم", "words": [{"armenian": "Գլուխ", "phonetic": "Glukh", "arabic": "رأس", "arabic_phonetic": "جلوخ", "audio": "glukh", "meaning": "كلمة من فئة الجسم وأجزاؤه", "lesson": "فئة الجسم وأجزاؤه"}, {"armenian": "Աչք", "phonetic": "Achq", "arabic": "عين", "arabic_phonetic": "اتشq", "audio": "achq", "meaning": "كلمة من فئة الجسم وأجزاؤه", "lesson": "فئة الجسم وأجزاؤه"}, {"armenian": "Քիթ", "phonetic": "Kit", "arabic": "أنف", "arabic_phonetic": "كيت", "audio": "kit", "meaning": "كلمة من فئة الجسم وأجزاؤه", "lesson": "فئة الجسم وأجزاؤه"}, {"armenian": "Բերան", "phonetic": "Beran", "arabic": "فم", "arabic_phonetic": "بيران", "audio": "beran", "meaning": "كلمة من فئة الجسم وأجزاؤه", "lesson": "فئة الجسم وأجزاؤه"}, {"armenian": "Ձեռք", "phonetic": "Dzerrq", "arabic": "يد", "arabic_phonetic": "دزيررq", "audio": "dzerrq", "meaning": "كلمة من فئة الجسم وأجزاؤه", "lesson": "فئة الجسم وأجزاؤه"}, {"armenian": "Ոտք", "phonetic": "Votq", "arabic": "قدم", "arabic_phonetic": "فوتq", "audio": "votq", "meaning": "كلمة من فئة الجسم وأجزاؤه", "lesson": "فئة الجسم وأجزاؤه"}, {"armenian": "Ականջ", "phonetic": "Akanj", "arabic": "أذن", "arabic_phonetic": "اكانج", "audio": "akanj", "meaning": "كلمة من فئة الجسم وأجزاؤه", "lesson": "فئة الجسم وأجزاؤه"}, {"armenian": "Մազ", "phonetic": "Maz", "arabic": "شعر", "arabic_phonetic": "ماز", "audio": "maz", "meaning": "كلمة من فئة الجسم وأجزاؤه", "lesson": "فئة الجسم وأجزاؤه"}, {"armenian": "Դեմք", "phonetic": "Demq", "arabic": "وجه", "arabic_phonetic": "ديمq", "audio": "demq", "meaning": "كلمة من فئة الجسم وأجزاؤه", "lesson": "فئة الجسم وأجزاؤه"}, {"armenian": "Մարմին", "phonetic": "Marmin", "arabic": "جسم", "arabic_phonetic": "مارمين", "audio": "marmin", "meaning": "كلمة من فئة الجسم وأجزاؤه", "lesson": "فئة الجسم وأجزاؤه"}]}, "الوقت والزمن": {"icon": "⏰", "description": "الوقت والتاريخ", "words": [{"armenian": "Ժամ", "phonetic": "Zham", "arabic": "ساعة", "arabic_phonetic": "زهام", "audio": "zham", "meaning": "كلمة من فئة الوقت والزمن", "lesson": "فئة الوقت والزمن"}, {"armenian": "Րոպե", "phonetic": "Rope", "arabic": "دقيقة", "arabic_phonetic": "روبي", "audio": "rope", "meaning": "كلمة من فئة الوقت والزمن", "lesson": "فئة الوقت والزمن"}, {"armenian": "Օր", "phonetic": "Or", "arabic": "يوم", "arabic_phonetic": "ور", "audio": "or", "meaning": "كلمة من فئة الوقت والزمن", "lesson": "فئة الوقت والزمن"}, {"armenian": "Շաբաթ", "phonetic": "Shabat", "arabic": "أسبوع", "arabic_phonetic": "شابات", "audio": "shabat", "meaning": "كلمة من فئة الوقت والزمن", "lesson": "فئة الوقت والزمن"}, {"armenian": "Ամիս", "phonetic": "Amis", "arabic": "شهر", "arabic_phonetic": "اميس", "audio": "amis", "meaning": "كلمة من فئة الوقت والزمن", "lesson": "فئة الوقت والزمن"}, {"armenian": "Տարի", "phonetic": "Tari", "arabic": "سنة", "arabic_phonetic": "تاري", "audio": "tari", "meaning": "كلمة من فئة الوقت والزمن", "lesson": "فئة الوقت والزمن"}, {"armenian": "Առավոտ", "phonetic": "Aravot", "arabic": "صباح", "arabic_phonetic": "ارافوت", "audio": "aravot", "meaning": "كلمة من فئة الوقت والزمن", "lesson": "فئة الوقت والزمن"}, {"armenian": "Երեկո", "phonetic": "Yereko", "arabic": "مساء", "arabic_phonetic": "ييريكو", "audio": "yereko", "meaning": "كلمة من فئة الوقت والزمن", "lesson": "فئة الوقت والزمن"}, {"armenian": "Գիշեր", "phonetic": "Gisher", "arabic": "ليل", "arabic_phonetic": "جيشير", "audio": "gisher", "meaning": "كلمة من فئة الوقت والزمن", "lesson": "فئة الوقت والزمن"}, {"armenian": "Այսօր", "phonetic": "Aysor", "arabic": "اليوم", "arabic_phonetic": "ايسور", "audio": "aysor", "meaning": "كلمة من فئة الوقت والزمن", "lesson": "فئة الوقت والزمن"}]}, "الطبيعة والطقس": {"icon": "🌳", "description": "الطبيعة والطقس", "words": [{"armenian": "Արև", "phonetic": "Arev", "arabic": "شمس", "arabic_phonetic": "اريف", "audio": "arev", "meaning": "كلمة من فئة الطبيعة والطقس", "lesson": "فئة الطبيعة والطقس"}, {"armenian": "Լուսին", "phonetic": "Lusin", "arabic": "قمر", "arabic_phonetic": "لوسين", "audio": "lusin", "meaning": "كلمة من فئة الطبيعة والطقس", "lesson": "فئة الطبيعة والطقس"}, {"armenian": "Աստղ", "phonetic": "Astgh", "arabic": "نجم", "arabic_phonetic": "استغ", "audio": "astgh", "meaning": "كلمة من فئة الطبيعة والطقس", "lesson": "فئة الطبيعة والطقس"}, {"armenian": "Անձրև", "phonetic": "Andzrev", "arabic": "مطر", "arabic_phonetic": "اندزريف", "audio": "andzrev", "meaning": "كلمة من فئة الطبيعة والطقس", "lesson": "فئة الطبيعة والطقس"}, {"armenian": "Ձյուն", "phonetic": "Dzyun", "arabic": "ثلج", "arabic_phonetic": "دزيون", "audio": "dzyun", "meaning": "كلمة من فئة الطبيعة والطقس", "lesson": "فئة الطبيعة والطقس"}, {"armenian": "Քամի", "phonetic": "Qami", "arabic": "ريح", "arabic_phonetic": "qامي", "audio": "qami", "meaning": "كلمة من فئة الطبيعة والطقس", "lesson": "فئة الطبيعة والطقس"}, {"armenian": "Ծառ", "phonetic": "Tsar", "arabic": "شجرة", "arabic_phonetic": "تسار", "audio": "tsar", "meaning": "كلمة من فئة الطبيعة والطقس", "lesson": "فئة الطبيعة والطقس"}, {"armenian": "Ծաղիկ", "phonetic": "Tsaghik", "arabic": "زهرة", "arabic_phonetic": "تساغيك", "audio": "tsaghik", "meaning": "كلمة من فئة الطبيعة والطقس", "lesson": "فئة الطبيعة والطقس"}, {"armenian": "Լեռ", "phonetic": "Lerr", "arabic": "جبل", "arabic_phonetic": "ليرر", "audio": "lerr", "meaning": "كلمة من فئة الطبيعة والطقس", "lesson": "فئة الطبيعة والطقس"}, {"armenian": "Ծով", "phonetic": "Tsov", "arabic": "بحر", "arabic_phonetic": "تسوف", "audio": "tsov", "meaning": "كلمة من فئة الطبيعة والطقس", "lesson": "فئة الطبيعة والطقس"}]}, "الأفعال الأساسية": {"icon": "🏃", "description": "الأفعال الأساسية", "words": [{"armenian": "Ուտել", "phonetic": "Utel", "arabic": "يأكل", "arabic_phonetic": "وتيل", "audio": "utel", "meaning": "كلمة من فئة الأفعال الأساسية", "lesson": "فئة الأفعال الأساسية"}, {"armenian": "Խմել", "phonetic": "Khmel", "arabic": "يشرب", "arabic_phonetic": "خميل", "audio": "khmel", "meaning": "كلمة من فئة الأفعال الأساسية", "lesson": "فئة الأفعال الأساسية"}, {"armenian": "Քնել", "phonetic": "Qnel", "arabic": "ينام", "arabic_phonetic": "qنيل", "audio": "qnel", "meaning": "كلمة من فئة الأفعال الأساسية", "lesson": "فئة الأفعال الأساسية"}, {"armenian": "Քայլել", "phonetic": "Qaylel", "arabic": "يمشي", "arabic_phonetic": "qايليل", "audio": "qaylel", "meaning": "كلمة من فئة الأفعال الأساسية", "lesson": "فئة الأفعال الأساسية"}, {"armenian": "Վազել", "phonetic": "Vazel", "arabic": "يجري", "arabic_phonetic": "فازيل", "audio": "vazel", "meaning": "كلمة من فئة الأفعال الأساسية", "lesson": "فئة الأفعال الأساسية"}, {"armenian": "Կարդալ", "phonetic": "Kardal", "arabic": "يقرأ", "arabic_phonetic": "كاردال", "audio": "kardal", "meaning": "كلمة من فئة الأفعال الأساسية", "lesson": "فئة الأفعال الأساسية"}, {"armenian": "Գրել", "phonetic": "Grel", "arabic": "يكتب", "arabic_phonetic": "جريل", "audio": "grel", "meaning": "كلمة من فئة الأفعال الأساسية", "lesson": "فئة الأفعال الأساسية"}, {"armenian": "Խոսել", "phonetic": "Khosel", "arabic": "يتكلم", "arabic_phonetic": "خوسيل", "audio": "khosel", "meaning": "كلمة من فئة الأفعال الأساسية", "lesson": "فئة الأفعال الأساسية"}, {"armenian": "Գնալ", "phonetic": "Gnal", "arabic": "يذهب", "arabic_phonetic": "جنال", "audio": "gnal", "meaning": "كلمة من فئة الأفعال الأساسية", "lesson": "فئة الأفعال الأساسية"}, {"armenian": "Գալ", "phonetic": "Gal", "arabic": "يأتي", "arabic_phonetic": "جال", "audio": "gal", "meaning": "كلمة من فئة الأفعال الأساسية", "lesson": "فئة الأفعال الأساسية"}]}, "كلمات متنوعة": {"icon": "📝", "description": "كلمات متنوعة ومفيدة", "words": [{"armenian": "Այո", "phonetic": "Ayo", "arabic": "نعم", "arabic_phonetic": "ايو", "audio": "ayo", "meaning": "كلمة من فئة كلمات متنوعة", "lesson": "فئة كلمات متنوعة"}, {"armenian": "Ոչ", "phonetic": "Voch", "arabic": "لا", "arabic_phonetic": "فوتش", "audio": "voch", "meaning": "كلمة من فئة كلمات متنوعة", "lesson": "فئة كلمات متنوعة"}, {"armenian": "Լավ", "phonetic": "Lav", "arabic": "جيد", "arabic_phonetic": "لاف", "audio": "lav", "meaning": "كلمة من فئة كلمات متنوعة", "lesson": "فئة كلمات متنوعة"}, {"armenian": "Վատ", "phonetic": "Vat", "arabic": "سيء", "arabic_phonetic": "فات", "audio": "vat", "meaning": "كلمة من فئة كلمات متنوعة", "lesson": "فئة كلمات متنوعة"}, {"armenian": "Մեծ", "phonetic": "Mets", "arabic": "كبير", "arabic_phonetic": "ميتس", "audio": "mets", "meaning": "كلمة من فئة كلمات متنوعة", "lesson": "فئة كلمات متنوعة"}, {"armenian": "Փոքր", "phonetic": "Poqr", "arabic": "صغير", "arabic_phonetic": "بوqر", "audio": "poqr", "meaning": "كلمة من فئة كلمات متنوعة", "lesson": "فئة كلمات متنوعة"}, {"armenian": "Նոր", "phonetic": "Nor", "arabic": "جديد", "arabic_phonetic": "نور", "audio": "nor", "meaning": "كلمة من فئة كلمات متنوعة", "lesson": "فئة كلمات متنوعة"}, {"armenian": "Հին", "phonetic": "Hin", "arabic": "قديم", "arabic_phonetic": "هين", "audio": "hin", "meaning": "كلمة من فئة كلمات متنوعة", "lesson": "فئة كلمات متنوعة"}, {"armenian": "Տուն", "phonetic": "Tun", "arabic": "بيت", "arabic_phonetic": "تون", "audio": "tun", "meaning": "كلمة من فئة كلمات متنوعة", "lesson": "فئة كلمات متنوعة"}, {"armenian": "Դպրոց", "phonetic": "Dprots", "arabic": "مدرسة", "arabic_phonetic": "دبروتس", "audio": "dprots", "meaning": "كلمة من فئة كلمات متنوعة", "lesson": "فئة كلمات متنوعة"}]}};
        let allWords = [];
        let audioData = {};
        
        // تحضير الكلمات
        function prepareWords() {
            allWords = [];
            for (const [category, data] of Object.entries(vocabulary)) {
                data.words.forEach(word => {
                    allWords.push({
                        ...word,
                        category,
                        audioFile: word.phonetic.replace(/[^a-zA-Z0-9]/g, '_') + '.mp3'
                    });
                });
            }
            displayWords();
        }
        
        // عرض الكلمات
        function displayWords() {
            const container = document.getElementById('wordsList');
            container.innerHTML = allWords.map((word, i) => `
                <div class="word">
                    <strong>${word.armenian}</strong> (${word.phonetic}) - ${word.arabic}
                    <button class="btn" onclick="playWord(${i})">🔊 تشغيل</button>
                    <span id="status-${i}"></span>
                </div>
            `).join('');
        }
        
        // تشغيل كلمة
        function playWord(index) {
            const word = allWords[index];
            const utterance = new SpeechSynthesisUtterance(word.phonetic);
            utterance.lang = 'en-US';
            utterance.rate = 0.7;
            utterance.pitch = 1;
            
            utterance.onend = () => {
                document.getElementById(`status-${index}`).textContent = '✅';
                audioData[word.audioFile] = {
                    word: word,
                    generated: true,
                    timestamp: new Date().toISOString()
                };
            };
            
            speechSynthesis.speak(utterance);
        }
        
        // توليد جميع الأصوات
        async function generateAll() {
            const statusEl = document.getElementById('status');
            const progressBar = document.getElementById('progressBar');
            
            for (let i = 0; i < allWords.length; i++) {
                statusEl.textContent = `توليد ${i + 1} من ${allWords.length}: ${allWords[i].phonetic}`;
                progressBar.style.width = ((i + 1) / allWords.length * 100) + '%';
                
                playWord(i);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            
            statusEl.textContent = '✅ تم توليد جميع الأصوات!';
        }
        
        // تحميل البيانات
        function downloadData() {
            const data = {
                vocabulary: vocabulary,
                audioFiles: audioData,
                totalWords: allWords.length,
                generatedAt: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = 'armenian_audio_data.json';
            a.click();
            
            URL.revokeObjectURL(url);
        }
        
        // تحضير عند التحميل
        document.addEventListener('DOMContentLoaded', prepareWords);
    </script>
</body>
</html>