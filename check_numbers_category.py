import re

# قراءة ملف JavaScript
with open('armenian_vocabulary_from_excel.js', 'r', encoding='utf-8') as f:
    content = f.read()

# البحث عن فئة الأرقام
if '"الأرقام والحساب"' in content:
    print("✅ فئة الأرقام موجودة في ملف JavaScript!")
    
    # استخراج محتوى فئة الأرقام
    pattern = r'"الأرقام والحساب":\s*{[^}]*"words":\s*\[(.*?)\]'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        words_content = match.group(1)
        # عد الكلمات
        word_count = words_content.count('"armenian":')
        print(f"📊 عدد الكلمات في فئة الأرقام: {word_count}")
        
        # عرض بعض الكلمات
        arabic_words = re.findall(r'"arabic":\s*"([^"]*)"', words_content)
        print("🔢 كلمات الأرقام:")
        for i, word in enumerate(arabic_words[:5], 1):
            print(f"  {i}. {word}")
    else:
        print("❌ لم يتم العثور على كلمات في فئة الأرقام")
else:
    print("❌ فئة الأرقام غير موجودة في ملف JavaScript!")
    
    # عرض الفئات الموجودة
    categories = re.findall(r'"([^"]+)":\s*{[^}]*"icon":', content)
    print("📋 الفئات الموجودة:")
    for i, cat in enumerate(categories, 1):
        print(f"  {i}. {cat}")
