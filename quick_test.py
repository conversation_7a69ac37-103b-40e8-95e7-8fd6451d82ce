#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لاستخراج درس واحد
"""

import requests
from bs4 import BeautifulSoup
import json
import re

def test_single_lesson():
    """اختبار استخراج درس واحد"""
    print("🧪 اختبار استخراج درس واحد...")

    # رابط الدرس الأول
    lesson_url = "https://www.lingohut.com/ar/v477363/%D8%AF%D8%B1%D9%88%D8%B3-%D9%81%D9%8A-%D8%A7%D9%84%D9%84%D8%BA%D8%A9-%D8%A7%D9%84%D8%A3%D8%B1%D9%85%D9%8A%D9%86%D9%8A%D8%A9-%D9%84%D9%82%D8%A7%D8%A1%D8%B4%D8%AE%D8%B5-%D9%85%D8%A7"

    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

        print(f"🔍 جاري الاتصال بالدرس الأول...")
        response = requests.get(lesson_url, headers=headers, timeout=30)
        response.raise_for_status()

        print(f"✅ تم الاتصال بنجاح!")

        soup = BeautifulSoup(response.content, 'html.parser')

        # البحث عن النصوص الأرمينية
        armenian_pattern = r'[\u0530-\u058F]+'
        armenian_texts = re.findall(armenian_pattern, response.text)

        # البحث عن النطق في الأقواس (تحسين النمط)
        phonetic_pattern = r'\(([A-Za-z][^)]*)\)'
        all_phonetic = re.findall(phonetic_pattern, response.text)

        # تصفية النطق الحقيقي (استبعاد CSS وغيره)
        phonetic_texts = []
        for p in all_phonetic:
            if not any(x in p.lower() for x in ['px', 'deg', 'width', 'height', 'color', 'font', 'margin', 'padding']):
                if len(p) > 1 and len(p) < 20:  # طول معقول للنطق
                    phonetic_texts.append(p)

        # البحث عن النصوص العربية
        arabic_pattern = r'[\u0600-\u06FF]+'
        arabic_texts = re.findall(arabic_pattern, response.text)

        print(f"📝 النتائج:")
        print(f"   🔤 نصوص أرمينية: {len(armenian_texts)}")
        print(f"   🔤 نطق لاتيني: {len(phonetic_texts)}")
        print(f"   🔤 نصوص عربية: {len(arabic_texts)}")

        # عرض أمثلة
        print(f"\n📋 أمثلة:")
        for i in range(min(5, len(armenian_texts), len(phonetic_texts))):
            armenian = armenian_texts[i] if i < len(armenian_texts) else "غير متوفر"
            phonetic = phonetic_texts[i] if i < len(phonetic_texts) else "غير متوفر"
            arabic_phonetic = convert_to_arabic_phonetic(phonetic)

            print(f"   {i+1}. {armenian} ({phonetic}) → {arabic_phonetic}")

        return True

    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def convert_to_arabic_phonetic(phonetic):
    """تحويل النطق اللاتيني إلى عربي"""
    if not phonetic:
        return ""

    # قاموس تحويل مبسط
    latin_to_arabic = {
        'a': 'ا', 'e': 'ي', 'i': 'ي', 'o': 'و', 'u': 'و',
        'b': 'ب', 'p': 'ب', 'f': 'ف', 'v': 'ف',
        't': 'ت', 'd': 'د', 's': 'س', 'z': 'ز',
        'k': 'ك', 'g': 'ج', 'h': 'ه', 'l': 'ل',
        'r': 'ر', 'm': 'م', 'n': 'ن', 'w': 'و', 'y': 'ي'
    }

    result = ""
    for char in phonetic.lower():
        result += latin_to_arabic.get(char, char)

    return result

if __name__ == "__main__":
    test_single_lesson()
