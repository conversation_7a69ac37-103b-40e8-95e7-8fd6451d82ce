# 🇦🇲 تطبيق تعلم اللغة الأرمينية - الإصدار المحدث

تطبيق ويب تفاعلي شامل لتعلم المفردات الأرمينية مع النطق الصحيح والمعاني المفصلة والصوت الحقيقي.

## ✨ المميزات الجديدة والمحدثة

### 🔊 **نظام صوتي محسن**
- **صوت حقيقي**: يحاول تشغيل ملفات MP3 أولاً
- **بديل ذكي**: Web Speech API عند عدم توفر الملفات الصوتية
- **دعم Chrome**: حل مشكلة الصوت في Google Chrome
- **تفاعل تلقائي**: تفعيل الصوت بأول نقرة

### 🔤 **النطق العربي الجديد**
- **تحويل ذكي**: تحويل الكلمات اللاتينية إلى نطق عربي
- **أمثلة واضحة**: Meg → ميك، Barev → باريف
- **4 حقول**: الأرمينية + اللاتينية + النطق العربي + المعنى العربي
- **بحث شامل**: إمكانية البحث بالنطق العربي أيضاً

### 📖 **معاني مفصلة**
- **ترجمة شاملة**: كل كلمة مع معناها المفصل
- **سياق واضح**: شرح استخدام الكلمة
- **أمثلة عملية**: معاني حرفية ومجازية
- **تصنيف موضوعي**: ربط المعنى بالدرس

### 🎵 **استخراج الصوتيات الجديد**
- **صوتيات حقيقية**: استخراج الملفات الصوتية من الموقع الأصلي
- **TTS احتياطي**: إنشاء ملفات صوتية للكلمات المفقودة
- **فهرس صوتي**: تنظيم جميع الملفات الصوتية
- **تحميل ذكي**: تجنب إعادة تحميل الملفات الموجودة

### 🤖 **استخراج شامل محدث**
- **125 درس كامل**: استخراج جميع دروس الموقع مع النطق العربي والصوتيات
- **Selenium WebDriver**: تقنية متقدمة للاستخراج
- **معالجة ذكية**: تنظيف وتصنيف تلقائي للبيانات مع إضافة النطق العربي
- **تحديث تلقائي**: دمج المفردات الجديدة في التطبيق مع النطق العربي والصوتيات

## 🚀 التشغيل السريع

### للاستخراج الشامل مع النطق العربي والصوتيات (أول مرة):
1. انقر نقراً مزدوجاً على [`run_complete_extraction.bat`](run_complete_extraction.bat:1)
2. انتظر حتى انتهاء عملية الاستخراج (قد تستغرق 45-60 دقيقة)
3. سيتم استخراج جميع الـ **125 درس** مع إضافة النطق العربي
4. سيتم استخراج الصوتيات من كل درس
5. سيتم فتح التطبيق تلقائياً مع جميع المفردات والنطق العربي والصوتيات

### للتشغيل العادي:
1. انقر نقراً مزدوجاً على [`run_app.bat`](run_app.bat:1)
2. أو افتح [`index.html`](index.html:1) مباشرة في المتصفح

## 📁 هيكل المشروع المحدث

```
armenian_app/
├── 📄 index.html                    # الصفحة الرئيسية المحدثة
├── 🎨 style.css                     # تنسيقات محسنة
├── ⚙️ script.js                     # منطق التطبيق مع الصوت المحسن
├── 📖 README.md                     # هذا الملف
├── 🐍 scraper.py                    # سكريبت الاستخراج الأساسي
├── 🤖 advanced_scraper.py           # سكريبت الاستخراج المتقدم
├── 🔄 update_vocabulary.py          # تحديث ملف JavaScript
├── 🎵 audio_generator.py            # مولد الملفات الصوتية
├── 📦 requirements.txt              # المكتبات المطلوبة
├── 🚀 run_app.bat                  # تشغيل سريع للتطبيق
├── 🔧 run_complete_extraction.bat   # استخراج شامل
├── 📁 audio/                        # مجلد الملفات الصوتية
│   ├── barev.mp3
│   ├── mayr.mp3
│   └── ...
├── 📊 vocabulary_data_complete.json # بيانات المفردات الكاملة
├── 📝 vocabulary_data_complete.js   # بيانات JavaScript
└── 📄 armenian_vocabulary_complete.txt # ملف نصي شامل
```

## 🔧 إعداد البيئة المحدث

### متطلبات النظام
- **متصفح حديث**: Chrome, Firefox, Safari, Edge
- **Python 3.7+**: للاستخراج التلقائي
- **ChromeDriver**: للاستخراج المتقدم (يتم تثبيته تلقائياً)
- **اتصال إنترنت**: للاستخراج وتحميل الخطوط

### تثبيت المكتبات المحدثة
```bash
pip install -r requirements.txt
```

المكتبات الجديدة:
- `selenium>=4.0.0` - للاستخراج المتقدم
- `webdriver-manager>=3.8.0` - إدارة ChromeDriver
- `gtts>=2.2.3` - تحويل النص إلى كلام
- `pydub>=0.25.1` - معالجة الملفات الصوتية

## 📚 استخراج المفردات الشامل

### الاستخراج المتقدم (جديد)
```bash
python advanced_scraper.py
```

**المميزات الجديدة:**
- 🔍 **استخراج شامل**: جميع الدروس من الموقع
- 🤖 **Selenium WebDriver**: تقنية متقدمة للمواقع التفاعلية
- 📊 **معالجة ذكية**: تنظيف وتصنيف تلقائي
- 💾 **حفظ متعدد**: JSON, JavaScript, TXT
- 📈 **إحصائيات مفصلة**: تقارير شاملة

### تحديث التطبيق
```bash
python update_vocabulary.py
```

**يقوم بـ:**
- 📥 تحميل المفردات المستخرجة
- 🔄 تحديث ملف JavaScript
- 💾 إنشاء نسخة احتياطية
- 📊 عرض الإحصائيات

## 🎵 نظام الصوت المحسن

### إنشاء الملفات الصوتية
```bash
python audio_generator.py
```

**المميزات الجديدة:**
- 🎤 **Google TTS**: صوت عالي الجودة
- 🔧 **تحسين تلقائي**: تطبيع وتحسين الصوت
- 📁 **فهرسة ذكية**: تنظيم الملفات الصوتية
- 🔄 **تحديث JavaScript**: تفعيل الصوت الحقيقي

### كيفية عمل النظام الصوتي
1. **محاولة أولى**: تشغيل ملف MP3 من مجلد `audio/`
2. **بديل ذكي**: Web Speech API للنطق اللاتيني
3. **تفاعل بصري**: تأثيرات على البطاقات
4. **مشغل عائم**: أدوات تحكم متقدمة

## 🎮 المميزات التفاعلية الجديدة

### اختصارات لوحة المفاتيح
- **مسافة**: تشغيل/إيقاف الصوت الحالي
- **Escape**: إغلاق مشغل الصوت
- **Ctrl+F**: التركيز على مربع البحث

### تحسينات الواجهة
- **تأثيرات حركية**: انتقالات سلسة
- **تحميل تدريجي**: تأثير fade-in
- **استجابة محسنة**: تصميم أفضل للموبايل
- **إمكانية الوصول**: دعم قارئات الشاشة

## 📊 البيانات والإحصائيات

### الدروس الحالية (17 كلمة)
- **مجوهرات**: 5 كلمات (ساعة، عقد، بروش، سلسلة، محل مجوهرات)
- **تحيات**: 3 كلمات (مرحبا، صباح الخير، مساء الخير)
- **العائلة**: 3 كلمات (عائلة، أم، أب)
- **الأرقام**: 3 كلمات (واحد، اثنان، ثلاثة)
- **الألوان**: 3 كلمات (أحمر، أخضر، أزرق)

### خطة التوسع الشاملة
- [ ] **50+ درس إضافي** من LingoHut
- [ ] **500+ كلمة أرمينية** مع النطق والمعاني
- [ ] **مواضيع متنوعة**: طعام، حيوانات، مهن، سفر، إلخ
- [ ] **مستويات تعليمية**: مبتدئ، متوسط، متقدم
- [ ] **جمل وعبارات**: تراكيب لغوية كاملة

## 🔄 عملية الاستخراج الشاملة

### خطوات الاستخراج المتقدم
1. **إعداد المتصفح**: تشغيل Chrome بدون واجهة
2. **استكشاف الموقع**: العثور على جميع روابط الدروس
3. **استخراج المحتوى**: تحليل كل صفحة درس
4. **معالجة البيانات**: تنظيف وتصنيف المفردات
5. **إنشاء الملفات**: حفظ بتنسيقات متعددة
6. **تحديث التطبيق**: دمج البيانات الجديدة

### معالجة الأخطاء
- **إعادة المحاولة**: عند فشل تحميل صفحة
- **بدائل متعددة**: طرق مختلفة لاستخراج البيانات
- **تسجيل مفصل**: تتبع العملية والأخطاء
- **استرداد جزئي**: حفظ ما تم استخراجه

## 🛠️ استكشاف الأخطاء المحدث

### مشاكل الصوت
```javascript
// إذا لم يعمل الصوت في Chrome:
// 1. تأكد من السماح بالصوت في المتصفح
// 2. انقر في أي مكان بالصفحة أولاً
// 3. تحقق من وجود ملفات MP3 في مجلد audio/
```

### مشاكل الاستخراج
```bash
# إذا فشل الاستخراج المتقدم:
pip install --upgrade selenium webdriver-manager

# أو استخدم الاستخراج الأساسي:
python scraper.py
```

### مشاكل ChromeDriver
```bash
# تثبيت ChromeDriver تلقائياً:
pip install webdriver-manager

# أو تحميل يدوي من:
# https://chromedriver.chromium.org/
```

## 🤝 المساهمة في المشروع

### إضافة مفردات جديدة
1. **تلقائياً**: استخدم [`advanced_scraper.py`](advanced_scraper.py:1)
2. **يدوياً**: عدّل [`script.js`](script.js:1) في قسم `vocabularyData`
3. **صوتياً**: استخدم [`audio_generator.py`](audio_generator.py:1)

### تحسين الاستخراج
- إضافة مواقع جديدة
- تحسين خوارزميات التحليل
- دعم لغات إضافية
- تحسين دقة الاستخراج

### تطوير المميزات
- **اختبارات تفاعلية**: كويزات ومسابقات
- **نظام نقاط**: تتبع التقدم
- **وضع الليل**: تصميم داكن
- **تطبيق موبايل**: PWA متقدم

## 📞 الدعم والمساعدة

### الحصول على المساعدة
- 📖 **اقرأ هذا الدليل** بعناية
- 🔍 **ابحث في الأكواد** عن أمثلة
- 🧪 **جرب الاستخراج التجريبي** أولاً
- 💬 **اطرح أسئلتك** مع تفاصيل المشكلة

### الإبلاغ عن مشاكل
- **وصف مفصل**: ما المشكلة بالضبط؟
- **خطوات الإعادة**: كيف تحدث المشكلة؟
- **لقطات شاشة**: صور توضيحية
- **معلومات النظام**: نوع المتصفح والنظام

## 🎯 الأهداف المستقبلية

### المرحلة القادمة (الشهر القادم)
- [ ] **استخراج شامل**: جميع دروس LingoHut
- [ ] **500+ كلمة**: مفردات شاملة
- [ ] **ملفات صوتية**: نطق لكل كلمة
- [ ] **اختبارات**: كويزات تفاعلية

### الرؤية طويلة المدى (6 أشهر)
- [ ] **تطبيق PWA**: يعمل بدون إنترنت
- [ ] **دعم متعدد اللغات**: إنجليزي، فرنسي، إلخ
- [ ] **مجتمع متعلمين**: منصة تفاعلية
- [ ] **شهادات إتمام**: نظام تقييم معتمد

### التقنيات المستقبلية
- [ ] **ذكاء اصطناعي**: تخصيص التعلم
- [ ] **الواقع المعزز**: تعلم تفاعلي
- [ ] **التعرف على الصوت**: تقييم النطق
- [ ] **التعلم التكيفي**: مسارات شخصية

## 📄 الترخيص والاستخدام

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والشخصي.

### الاستخدام المسموح
- ✅ **التعلم الشخصي**: استخدام فردي
- ✅ **التعليم**: في المدارس والجامعات
- ✅ **التطوير**: تحسين وإضافة مميزات
- ✅ **المشاركة**: نشر مع ذكر المصدر

### الاستخدام المحظور
- ❌ **الاستخدام التجاري**: بيع التطبيق
- ❌ **انتهاك الحقوق**: نسخ بدون إذن
- ❌ **المحتوى الضار**: استخدام سيء

---

## 🎉 شكر خاص

**🇦🇲 تم إنشاء هذا التطبيق المحدث بحب لمساعدة متعلمي اللغة الأرمينية العرب ❤️**

**🙏 شكر خاص لـ:**
- **LingoHut**: مصدر المفردات الأساسية
- **Google TTS**: خدمة تحويل النص إلى كلام
- **مجتمع المطورين**: الأدوات والمكتبات مفتوحة المصدر

**📧 للتواصل والاستفسارات والاقتراحات:**
- 💌 البريد الإلكتروني: [إضافة بريدك الإلكتروني]
- 💬 التليجرام: [إضافة حسابك]
- 🐙 GitHub: [إضافة رابط المشروع]

**🌟 إذا أعجبك المشروع:**
- ⭐ ضع نجمة على GitHub
- 📢 شاركه مع الأصدقاء
- 💡 اقترح تحسينات جديدة
- 🤝 ساهم في التطوير

**🔮 المستقبل مشرق للتعلم الرقمي! 🚀**