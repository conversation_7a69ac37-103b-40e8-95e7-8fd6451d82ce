#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مستخرج الصوتيات الحقيقية من موقع LingoHut
Real Audio Extractor from LingoHut Website
"""

import requests
from bs4 import BeautifulSoup
import json
import re
import time
import os
from pathlib import Path
from urllib.parse import urljoin, urlparse
import random
import hashlib

class RealAudioExtractor:
    def __init__(self):
        self.base_url = "https://www.lingohut.com"
        self.audio_dir = Path("audio")
        self.audio_dir.mkdir(exist_ok=True)
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Referer': 'https://www.lingohut.com/'
        })
        
        self.audio_mapping = {}
        self.downloaded_count = 0

    def find_audio_in_lessons(self):
        """البحث عن الملفات الصوتية في دروس LingoHut"""
        print("🔍 البحث عن الملفات الصوتية في الدروس...")
        
        audio_urls = []
        
        # البحث في الدروس الأساسية
        lesson_urls = [
            "/ar/v477/1",  # الدرس الأول
            "/ar/v477/2",  # الدرس الثاني
            "/ar/v477/3",  # الدرس الثالث
            "/ar/v477/4",  # الدرس الرابع
            "/ar/v477/5",  # الدرس الخامس
        ]
        
        for lesson_url in lesson_urls:
            try:
                print(f"📖 فحص الدرس: {lesson_url}")
                
                response = self.session.get(self.base_url + lesson_url, timeout=20)
                if response.status_code == 200:
                    audio_links = self.extract_audio_from_lesson(response.text, lesson_url)
                    if audio_links:
                        audio_urls.extend(audio_links)
                        print(f"   ✅ وجد {len(audio_links)} ملف صوتي")
                    else:
                        print(f"   ⚠️ لا توجد ملفات صوتية")
                
                time.sleep(random.uniform(2, 4))
                
            except Exception as e:
                print(f"   ❌ خطأ: {e}")
        
        return audio_urls

    def extract_audio_from_lesson(self, html_content, lesson_url):
        """استخراج الملفات الصوتية من درس واحد"""
        audio_urls = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # البحث عن روابط الصوت المختلفة
            audio_patterns = [
                # روابط مباشرة للملفات الصوتية
                r'https?://[^"\s]+\.(?:mp3|wav|ogg|m4a)',
                r'/audio/[^"\s]+\.(?:mp3|wav|ogg|m4a)',
                r'/sounds?/[^"\s]+\.(?:mp3|wav|ogg|m4a)',
                r'/media/[^"\s]+\.(?:mp3|wav|ogg|m4a)',
                # روابط LingoHut المحتملة
                r'https?://[^"\s]*lingohut[^"\s]*\.(?:mp3|wav|ogg|m4a)',
                r'/[^"\s]*pronunciation[^"\s]*\.(?:mp3|wav|ogg|m4a)',
            ]
            
            # البحث في النص الخام
            for pattern in audio_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    if not match.startswith('http'):
                        match = urljoin(self.base_url, match)
                    audio_urls.append(match)
            
            # البحث في عناصر HTML
            audio_elements = soup.find_all(['audio', 'source'])
            for element in audio_elements:
                src = element.get('src')
                if src:
                    if not src.startswith('http'):
                        src = urljoin(self.base_url, src)
                    audio_urls.append(src)
            
            # البحث في data attributes
            for element in soup.find_all(attrs={'data-audio': True}):
                audio_url = element.get('data-audio')
                if audio_url:
                    if not audio_url.startswith('http'):
                        audio_url = urljoin(self.base_url, audio_url)
                    audio_urls.append(audio_url)
            
            # البحث في onclick events
            for element in soup.find_all(attrs={'onclick': True}):
                onclick = element.get('onclick')
                if onclick and 'audio' in onclick.lower():
                    # استخراج رابط من onclick
                    audio_match = re.search(r'["\']([^"\']*\.(?:mp3|wav|ogg|m4a))["\']', onclick)
                    if audio_match:
                        audio_url = audio_match.group(1)
                        if not audio_url.startswith('http'):
                            audio_url = urljoin(self.base_url, audio_url)
                        audio_urls.append(audio_url)
            
            # البحث في JavaScript
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string:
                    for pattern in audio_patterns:
                        matches = re.findall(pattern, script.string, re.IGNORECASE)
                        for match in matches:
                            if not match.startswith('http'):
                                match = urljoin(self.base_url, match)
                            audio_urls.append(match)
        
        except Exception as e:
            print(f"❌ خطأ في استخراج الصوت: {e}")
        
        return list(set(audio_urls))  # إزالة المكررات

    def download_audio_file(self, audio_url, word_info=None):
        """تحميل ملف صوتي"""
        try:
            print(f"⬇️ تحميل: {audio_url}")
            
            # محاولة تحميل الملف
            response = self.session.get(audio_url, timeout=30, stream=True)
            
            if response.status_code == 200:
                # التحقق من نوع المحتوى
                content_type = response.headers.get('content-type', '').lower()
                if not any(audio_type in content_type for audio_type in ['audio', 'mpeg', 'wav', 'ogg']):
                    print(f"   ⚠️ ليس ملف صوتي: {content_type}")
                    return None
                
                # تحديد اسم الملف
                if word_info:
                    filename = f"{word_info['phonetic'].replace(' ', '_').replace('(', '').replace(')', '')}.mp3"
                else:
                    # استخراج اسم الملف من الرابط
                    parsed_url = urlparse(audio_url)
                    filename = os.path.basename(parsed_url.path)
                    if not filename or '.' not in filename:
                        # إنشاء اسم ملف من hash الرابط
                        url_hash = hashlib.md5(audio_url.encode()).hexdigest()[:8]
                        filename = f"audio_{url_hash}.mp3"
                
                # تنظيف اسم الملف
                filename = re.sub(r'[^\w\-_\.]', '_', filename)
                file_path = self.audio_dir / filename
                
                # حفظ الملف
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                # التحقق من حجم الملف
                file_size = file_path.stat().st_size
                if file_size < 1000:  # أقل من 1KB
                    print(f"   ⚠️ ملف صغير جداً: {file_size} bytes")
                    file_path.unlink()  # حذف الملف
                    return None
                
                print(f"   ✅ تم حفظ: {filename} ({file_size} bytes)")
                
                # حفظ معلومات الملف
                audio_info = {
                    'filename': filename,
                    'url': audio_url,
                    'size': file_size,
                    'path': str(file_path),
                    'content_type': content_type
                }
                
                if word_info:
                    audio_info['word'] = word_info
                
                self.audio_mapping[filename] = audio_info
                self.downloaded_count += 1
                
                return filename
                
            else:
                print(f"   ❌ فشل التحميل: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"   ❌ خطأ في تحميل {audio_url}: {e}")
            return None

    def create_audio_vocabulary_mapping(self):
        """ربط الملفات الصوتية بالمفردات"""
        print("🔗 ربط الملفات الصوتية بالمفردات...")
        
        # قراءة المفردات
        vocab_file = Path('armenian_vocabulary_final.json')
        if not vocab_file.exists():
            print("❌ ملف المفردات غير موجود")
            return {}
        
        with open(vocab_file, 'r', encoding='utf-8') as f:
            vocabulary = json.load(f)
        
        # إنشاء خريطة الصوتيات
        audio_vocabulary = {}
        
        for category_name, category_data in vocabulary.items():
            audio_vocabulary[category_name] = {
                'icon': category_data['icon'],
                'description': category_data['description'],
                'words': []
            }
            
            for word in category_data['words']:
                # البحث عن ملف صوتي مطابق
                audio_filename = None
                phonetic_clean = re.sub(r'[^\w]', '_', word['phonetic'])
                
                # البحث في الملفات المحملة
                for filename, audio_info in self.audio_mapping.items():
                    if phonetic_clean.lower() in filename.lower():
                        audio_filename = filename
                        break
                
                # إذا لم يوجد ملف، استخدم اسم افتراضي
                if not audio_filename:
                    audio_filename = f"{phonetic_clean}.mp3"
                
                word_with_audio = {
                    **word,
                    'audio_file': audio_filename,
                    'has_real_audio': audio_filename in self.audio_mapping,
                    'audio_path': f"audio/{audio_filename}" if audio_filename in self.audio_mapping else None
                }
                
                audio_vocabulary[category_name]['words'].append(word_with_audio)
        
        return audio_vocabulary

    def save_audio_data(self, audio_vocabulary):
        """حفظ بيانات الصوتيات"""
        try:
            # حفظ المفردات مع الصوتيات
            with open('armenian_vocabulary_with_audio.json', 'w', encoding='utf-8') as f:
                json.dump(audio_vocabulary, f, ensure_ascii=False, indent=2)
            
            # حفظ معلومات الملفات الصوتية
            audio_manifest = {
                'audio_files': self.audio_mapping,
                'total_downloaded': self.downloaded_count,
                'audio_directory': str(self.audio_dir),
                'extraction_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                'instructions': {
                    'ar': 'ضع مجلد audio في نفس مكان التطبيق',
                    'en': 'Place audio folder in the same location as the app'
                }
            }
            
            with open('audio_manifest.json', 'w', encoding='utf-8') as f:
                json.dump(audio_manifest, f, ensure_ascii=False, indent=2)
            
            print(f"✅ تم حفظ البيانات:")
            print(f"   📄 armenian_vocabulary_with_audio.json")
            print(f"   📋 audio_manifest.json")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")

    def run_extraction(self):
        """تشغيل استخراج الصوتيات"""
        print("🔊 بدء استخراج الصوتيات الحقيقية")
        print("=" * 50)
        
        start_time = time.time()
        
        # البحث عن الملفات الصوتية
        audio_urls = self.find_audio_in_lessons()
        
        if audio_urls:
            print(f"\n⬇️ تحميل {len(audio_urls)} ملف صوتي...")
            
            # تحميل الملفات
            for i, audio_url in enumerate(audio_urls[:20], 1):  # أول 20 ملف
                print(f"[{i}/20] ", end="")
                self.download_audio_file(audio_url)
                time.sleep(random.uniform(1, 3))
        else:
            print("⚠️ لم يتم العثور على ملفات صوتية")
        
        # ربط الصوتيات بالمفردات
        audio_vocabulary = self.create_audio_vocabulary_mapping()
        
        # حفظ البيانات
        self.save_audio_data(audio_vocabulary)
        
        # إحصائيات نهائية
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n🎉 تم الانتهاء!")
        print(f"📊 النتائج:")
        print(f"   🔊 ملفات صوتية تم تحميلها: {self.downloaded_count}")
        print(f"   📁 مجلد الصوتيات: {self.audio_dir}")
        print(f"   ⏱️ الوقت المستغرق: {duration:.1f} ثانية")
        print(f"\n📝 الملفات المحفوظة:")
        print(f"   📄 armenian_vocabulary_with_audio.json")
        print(f"   📋 audio_manifest.json")
        print(f"   📁 audio/ (مجلد الملفات الصوتية)")

def main():
    extractor = RealAudioExtractor()
    extractor.run_extraction()

if __name__ == "__main__":
    main()
