# 🐍 دليل تثبيت Python لتطبيق تعلم الأرمينية

## ⚠️ **مطلوب: تثبيت Python أولاً**

لتشغيل الاستخراج الشامل للـ 125 درس، تحتاج إلى تثبيت Python أولاً.

---

## 🚀 **طريقة التثبيت السريعة**

### 📥 **الخيار 1: من Microsoft Store (الأسهل)**
1. اضغط على `Windows + R`
2. اكتب `ms-windows-store://pdp/?productid=9NRWMJP3717K`
3. اضغط Enter
4. انقر على "Get" أو "تثبيت"
5. انتظر حتى انتهاء التثبيت

### 📥 **الخيار 2: من الموقع الرسمي**
1. اذهب إلى: https://www.python.org/downloads/
2. انقر على "Download Python 3.11.x" (أحدث إصدار)
3. شغل الملف المحمل
4. **مهم**: ✅ تأكد من تحديد "Add Python to PATH"
5. انقر على "Install Now"

---

## ✅ **التحقق من التثبيت**

بعد التثبيت، افتح Command Prompt واكتب:
```bash
python --version
```

يجب أن ترى شيئاً مثل:
```
Python 3.11.x
```

---

## 🔧 **تثبيت المكتبات المطلوبة**

بعد تثبيت Python، شغل هذا الأمر:
```bash
pip install selenium beautifulsoup4 requests webdriver-manager gtts pygame
```

---

## 🚀 **بعد التثبيت**

1. انقر نقراً مزدوجاً على `run_complete_extraction.bat`
2. سيبدأ استخراج جميع الـ 125 درس
3. انتظر 45-60 دقيقة للانتهاء

---

## 🆘 **حل المشاكل الشائعة**

### ❌ **"Python was not found"**
- تأكد من تحديد "Add Python to PATH" أثناء التثبيت
- أعد تشغيل الكمبيوتر بعد التثبيت
- جرب الأمر `py` بدلاً من `python`

### ❌ **"Scripts is disabled"**
شغل PowerShell كمدير وأدخل:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### ❌ **مشاكل في تثبيت المكتبات**
```bash
pip install --upgrade pip
pip install --user selenium beautifulsoup4 requests webdriver-manager gtts pygame
```

---

## 📞 **الدعم**

إذا واجهت مشاكل في التثبيت:
1. تأكد من اتصال الإنترنت
2. شغل Command Prompt كمدير
3. جرب إعادة تشغيل الكمبيوتر
4. تأكد من عدم وجود برامج مكافحة فيروسات تمنع التثبيت

---

## 🎯 **بعد التثبيت الناجح**

ستتمكن من:
- ✅ استخراج جميع الـ 125 درس
- ✅ الحصول على النطق العربي لكل كلمة
- ✅ تحميل الصوتيات من الموقع
- ✅ تشغيل التطبيق بجميع الميزات

**🚀 ابدأ بتثبيت Python الآن!**
