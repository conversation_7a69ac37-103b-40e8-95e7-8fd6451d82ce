@echo off
chcp 65001 >nul
cd /d "%~dp0"
echo.
echo ========================================
echo    🇦🇲 استخراج المفردات الأرمينية
echo    Armenian Vocabulary Extraction
echo ========================================
echo.
echo 📍 المجلد الحالي: %CD%
echo.

REM التحقق من وجود Python
echo 🔍 التحقق من Python...
py --version >nul 2>&1
if errorlevel 1 (
    python --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Python غير مثبت
        echo.
        echo 💡 لتثبيت Python:
        echo    1. اذهب إلى: https://python.org/downloads
        echo    2. حمل أحدث إصدار
        echo    3. تأكد من تحديد "Add Python to PATH"
        echo    4. أعد تشغيل الكمبيوتر بعد التثبيت
        echo.
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=python
    )
) else (
    set PYTHON_CMD=py
)

echo ✅ Python متوفر
echo.

REM تثبيت المكتبات
echo 📦 تثبيت المكتبات...
%PYTHON_CMD% -m pip install --quiet requests beautifulsoup4 selenium webdriver-manager 2>nul
if errorlevel 1 (
    echo ⚠️ محاولة تثبيت بصلاحيات المستخدم...
    %PYTHON_CMD% -m pip install --user --quiet requests beautifulsoup4 selenium webdriver-manager
)

echo ✅ المكتبات جاهزة
echo.

REM التحقق من ملفات الاستخراج
if exist advanced_scraper.py (
    set SCRAPER=advanced_scraper.py
    echo ✅ سيتم استخدام المستخرج المتقدم
) else if exist simple_scraper.py (
    set SCRAPER=simple_scraper.py
    echo ✅ سيتم استخدام المستخرج المبسط
) else (
    echo ❌ لم يتم العثور على ملفات الاستخراج
    echo 💡 تأكد من وجود الملفات في نفس المجلد
    pause
    exit /b 1
)

echo.
echo 🚀 بدء الاستخراج...
echo 🔤 سيتم إضافة النطق العربي لكل كلمة
echo ⏳ قد يستغرق 30-60 دقيقة...
echo.

REM بدء الاستخراج
%PYTHON_CMD% %SCRAPER%

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في الاستخراج
    echo.
    echo 💡 جرب الحلول التالية:
    echo    1. تحقق من اتصال الإنترنت
    echo    2. أعد المحاولة لاحقاً
    echo    3. جرب المستخرج المبسط
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم الانتهاء من الاستخراج!
echo.

REM فتح التطبيق من المجلد الصحيح
echo 🌐 فتح التطبيق...

if exist "%~dp0index.html" (
    start "" "%~dp0index.html"
    echo ✅ تم فتح التطبيق الرئيسي
) else (
    echo ⚠️ index.html غير موجود في المجلد الحالي
)

if exist "%~dp0demo_extracted_data.html" (
    timeout /t 2 /nobreak >nul
    start "" "%~dp0demo_extracted_data.html"
    echo ✅ تم فتح عرض البيانات
) else (
    echo ⚠️ demo_extracted_data.html غير موجود
)

echo.
echo 🎉 انتهت العملية!
echo.
pause
