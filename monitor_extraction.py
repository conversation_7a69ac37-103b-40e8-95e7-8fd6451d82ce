#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقب عملية الاستخراج
"""

import os
import time
import json
from datetime import datetime

def monitor_extraction():
    """مراقبة تقدم عملية الاستخراج"""
    print("🔍 مراقب عملية الاستخراج")
    print("=" * 50)
    
    files_to_monitor = [
        'vocabulary_data_complete.json',
        'vocabulary_data_complete.js',
        'armenian_vocabulary_complete.txt'
    ]
    
    start_time = datetime.now()
    
    while True:
        print(f"\n⏰ الوقت: {datetime.now().strftime('%H:%M:%S')}")
        print(f"⏳ مدة التشغيل: {datetime.now() - start_time}")
        
        # تحقق من وجود الملفات
        files_found = 0
        for file in files_to_monitor:
            if os.path.exists(file):
                size = os.path.getsize(file)
                print(f"✅ {file}: {size:,} بايت")
                files_found += 1
            else:
                print(f"⏳ {file}: لم يُنشأ بعد")
        
        # تحقق من ملف JSON إذا كان موجوداً
        if os.path.exists('vocabulary_data_complete.json'):
            try:
                with open('vocabulary_data_complete.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                total_words = 0
                lessons_count = len(data)
                
                for lesson_words in data.values():
                    total_words += len(lesson_words)
                
                print(f"📊 الإحصائيات الحالية:")
                print(f"   📚 عدد الدروس: {lessons_count}")
                print(f"   📝 إجمالي الكلمات: {total_words}")
                
                if lessons_count >= 125:
                    print(f"🎉 تم الانتهاء! تم استخراج جميع الـ 125 درس")
                    break
                    
            except Exception as e:
                print(f"⚠️ خطأ في قراءة الملف: {e}")
        
        print(f"📈 التقدم: {files_found}/{len(files_to_monitor)} ملفات")
        
        # انتظار 30 ثانية قبل التحقق مرة أخرى
        time.sleep(30)

if __name__ == "__main__":
    try:
        monitor_extraction()
    except KeyboardInterrupt:
        print(f"\n🛑 تم إيقاف المراقبة")
    except Exception as e:
        print(f"\n❌ خطأ في المراقبة: {e}")
