@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    تطبيق تعلم اللغة الأرمينية
echo    Armenian Learning App
echo ========================================
echo.

echo 🚀 جاري تشغيل التطبيق...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

REM التحقق من وجود ملف المتطلبات
if exist requirements.txt (
    echo 📦 تثبيت المكتبات المطلوبة...
    pip install -r requirements.txt
    echo.
)

REM تشغيل التطبيق في المتصفح
echo 🌐 فتح التطبيق في المتصفح...
start index.html

echo.
echo ✅ تم تشغيل التطبيق بنجاح!
echo.
echo 📋 الخيارات المتاحة:
echo    1. لاستخراج المزيد من المفردات: python scraper.py
echo    2. لإنشاء الملفات الصوتية: python audio_generator.py
echo    3. لإغلاق هذه النافذة: اضغط أي مفتاح
echo.

pause