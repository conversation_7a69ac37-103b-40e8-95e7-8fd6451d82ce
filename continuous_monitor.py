#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقب مستمر للاستخراج
"""

import os
import json
import time
from datetime import datetime

def monitor_continuously():
    """مراقبة مستمرة للتقدم"""
    print("🔄 بدء المراقبة المستمرة للاستخراج")
    print("=" * 60)
    
    start_time = datetime.now()
    last_lessons = 0
    last_words = 0
    
    while True:
        current_time = datetime.now()
        elapsed = current_time - start_time
        
        print(f"\n⏰ {current_time.strftime('%H:%M:%S')} | مدة التشغيل: {str(elapsed).split('.')[0]}")
        
        if os.path.exists('vocabulary_data_complete.json'):
            try:
                with open('vocabulary_data_complete.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                lessons = len(data)
                total_words = sum(len(words) for words in data.values())
                
                # حساب التقدم
                progress_percent = (lessons / 125) * 100
                
                # حساب السرعة
                lessons_per_minute = (lessons - last_lessons) / 2 if last_lessons > 0 else 0
                words_per_minute = (total_words - last_words) / 2 if last_words > 0 else 0
                
                print(f"📊 التقدم: {lessons}/125 درس ({progress_percent:.1f}%)")
                print(f"📝 الكلمات: {total_words}")
                print(f"⚡ السرعة: {lessons_per_minute:.1f} درس/دقيقة | {words_per_minute:.1f} كلمة/دقيقة")
                
                # تقدير الوقت المتبقي
                if lessons_per_minute > 0:
                    remaining_lessons = 125 - lessons
                    estimated_minutes = remaining_lessons / lessons_per_minute
                    print(f"⏳ الوقت المقدر للانتهاء: {estimated_minutes:.0f} دقيقة")
                
                # عرض شريط التقدم
                bar_length = 30
                filled_length = int(bar_length * progress_percent / 100)
                bar = '█' * filled_length + '░' * (bar_length - filled_length)
                print(f"📈 [{bar}] {progress_percent:.1f}%")
                
                # عرض آخر درس
                if data:
                    last_lesson = list(data.keys())[-1]
                    print(f"📖 آخر درس: {last_lesson}")
                
                # تحديث القيم للمقارنة التالية
                last_lessons = lessons
                last_words = total_words
                
                # التحقق من الانتهاء
                if lessons >= 125:
                    print(f"\n🎉 تم الانتهاء من جميع الـ 125 درس!")
                    print(f"📊 النتائج النهائية:")
                    print(f"   📚 الدروس: {lessons}")
                    print(f"   📝 الكلمات: {total_words}")
                    print(f"   ⏱️ الوقت الإجمالي: {str(elapsed).split('.')[0]}")
                    break
                    
            except Exception as e:
                print(f"❌ خطأ في قراءة الملف: {e}")
        else:
            print("⏳ لم يبدأ الاستخراج بعد...")
        
        # فحص الملفات الأخرى
        file_sizes = {}
        for file in ['vocabulary_data_complete.js', 'armenian_vocabulary_complete.txt']:
            if os.path.exists(file):
                size = os.path.getsize(file)
                file_sizes[file] = f"{size:,} بايت"
        
        if file_sizes:
            print(f"📁 أحجام الملفات: {file_sizes}")
        
        # انتظار دقيقتين
        time.sleep(120)

if __name__ == "__main__":
    try:
        monitor_continuously()
    except KeyboardInterrupt:
        print(f"\n🛑 تم إيقاف المراقبة")
    except Exception as e:
        print(f"\n❌ خطأ في المراقبة: {e}")
