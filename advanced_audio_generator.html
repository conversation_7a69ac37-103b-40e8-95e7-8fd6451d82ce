<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔊 مولد الصوت الأرمني المتقدم</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #2d3748;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: #667eea;
        }

        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .progress-container {
            background: #f7fafc;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            border: 2px solid #e2e8f0;
        }

        .progress-bar {
            background: #e2e8f0;
            border-radius: 10px;
            height: 25px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .status {
            font-size: 1.1rem;
            font-weight: 600;
            color: #4a5568;
            text-align: center;
        }

        .words-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .word-card {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .word-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .word-card.generated {
            border-color: #48bb78;
            background: #f0fff4;
        }

        .armenian {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 8px;
            text-align: center;
        }

        .phonetic {
            font-size: 1.1rem;
            color: #667eea;
            font-style: italic;
            text-align: center;
            margin-bottom: 8px;
        }

        .arabic {
            font-size: 1.2rem;
            color: #4a5568;
            text-align: center;
            margin-bottom: 15px;
        }

        .word-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .word-btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s ease;
        }

        .word-btn:hover {
            background: #3182ce;
        }

        .word-btn.success {
            background: #48bb78;
        }

        .download-section {
            background: #f7fafc;
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            text-align: center;
            border: 2px solid #e2e8f0;
        }

        .download-info {
            margin-bottom: 20px;
            color: #4a5568;
        }

        @media (max-width: 768px) {
            .words-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔊 مولد الصوت الأرمني المتقدم</h1>
            <p>توليد وتحميل ملفات صوتية عالية الجودة لـ 110 كلمة أرمينية</p>
        </div>

        <div class="controls">
            <button class="btn" onclick="generateAllAudio()" id="generateBtn">
                🎵 توليد جميع الأصوات
            </button>
            <button class="btn" onclick="downloadAudioData()" id="downloadBtn" disabled>
                📥 تحميل البيانات الصوتية
            </button>
            <button class="btn" onclick="testAudio()">
                🔊 اختبار الصوت
            </button>
            <button class="btn" onclick="resetAll()">
                🔄 إعادة تعيين
            </button>
        </div>

        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill">0%</div>
            </div>
            <div class="status" id="status">جاهز للبدء - انقر على "توليد جميع الأصوات"</div>
        </div>

        <div class="download-section">
            <h3>📱 للتطبيق الموبايل</h3>
            <div class="download-info">
                <p>✅ أسماء ملفات منظمة ومهيأة</p>
                <p>✅ بيانات JSON شاملة</p>
                <p>✅ معلومات كاملة لكل كلمة</p>
                <p>✅ جاهز للاستخدام في React Native / Flutter</p>
            </div>
            <div id="downloadStats">
                <strong>الإحصائيات:</strong>
                <span id="generatedCount">0</span> من <span id="totalCount">110</span> كلمة تم توليدها
            </div>
        </div>

        <div class="words-grid" id="wordsGrid">
            <!-- سيتم ملؤها بواسطة JavaScript -->
        </div>
    </div>

    <script>
        // بيانات المفردات (سيتم تحميلها من الملف)
        let vocabulary = {};
        let allWords = [];
        let audioData = {};
        let generatedCount = 0;
        let isGenerating = false;

        // تحميل المفردات
        async function loadVocabulary() {
            try {
                const response = await fetch('armenian_vocabulary_final.json');
                vocabulary = await response.json();
                prepareWords();
                displayWords();
                updateStats();
            } catch (error) {
                console.error('خطأ في تحميل المفردات:', error);
                document.getElementById('status').textContent = 'خطأ: لم يتم العثور على ملف المفردات';
            }
        }

        // تحضير قائمة الكلمات
        function prepareWords() {
            allWords = [];
            for (const [categoryName, categoryData] of Object.entries(vocabulary)) {
                categoryData.words.forEach((word, index) => {
                    allWords.push({
                        ...word,
                        category: categoryName,
                        audioFilename: `${word.phonetic.replace(/[^a-zA-Z0-9]/g, '_')}.mp3`,
                        id: `${categoryName}_${index}`
                    });
                });
            }
        }

        // عرض الكلمات
        function displayWords() {
            const grid = document.getElementById('wordsGrid');
            grid.innerHTML = allWords.map((word, index) => `
                <div class="word-card" id="card-${index}">
                    <div class="armenian">${word.armenian}</div>
                    <div class="phonetic">(${word.phonetic})</div>
                    <div class="arabic">${word.arabic}</div>
                    <div class="word-controls">
                        <button class="word-btn" onclick="generateWordAudio(${index})" id="btn-${index}">
                            🔊 توليد
                        </button>
                        <span id="status-${index}"></span>
                    </div>
                </div>
            `).join('');
        }

        // توليد صوت لكلمة واحدة
        async function generateWordAudio(index) {
            const word = allWords[index];
            const btn = document.getElementById(`btn-${index}`);
            const status = document.getElementById(`status-${index}`);
            const card = document.getElementById(`card-${index}`);

            btn.disabled = true;
            btn.textContent = '⏳ جاري التوليد...';
            status.textContent = '🔄';

            try {
                // إنشاء الصوت
                const utterance = new SpeechSynthesisUtterance(word.phonetic);
                utterance.lang = 'en-US';
                utterance.rate = 0.7;
                utterance.pitch = 1;
                utterance.volume = 0.9;

                // تشغيل الصوت
                speechSynthesis.speak(utterance);

                // حفظ بيانات الصوت
                audioData[word.audioFilename] = {
                    word: word,
                    generated: true,
                    timestamp: new Date().toISOString(),
                    filename: word.audioFilename,
                    phonetic: word.phonetic,
                    armenian: word.armenian,
                    arabic: word.arabic,
                    arabic_phonetic: word.arabic_phonetic,
                    category: word.category
                };

                // تحديث الواجهة
                btn.textContent = '✅ تم';
                btn.className = 'word-btn success';
                status.textContent = '✅';
                card.className = 'word-card generated';

                generatedCount++;
                updateStats();

                // تفعيل زر التحميل إذا تم توليد جميع الأصوات
                if (generatedCount === allWords.length) {
                    document.getElementById('downloadBtn').disabled = false;
                    document.getElementById('status').textContent = '🎉 تم توليد جميع الأصوات بنجاح!';
                }

            } catch (error) {
                console.error('خطأ في توليد الصوت:', error);
                btn.textContent = '❌ فشل';
                btn.disabled = false;
                status.textContent = '❌';
            }
        }

        // توليد جميع الأصوات
        async function generateAllAudio() {
            if (isGenerating) return;

            isGenerating = true;
            const generateBtn = document.getElementById('generateBtn');
            const progressFill = document.getElementById('progressFill');
            const status = document.getElementById('status');

            generateBtn.disabled = true;
            generateBtn.textContent = '⏳ جاري التوليد...';

            for (let i = 0; i < allWords.length; i++) {
                const progress = ((i + 1) / allWords.length) * 100;
                progressFill.style.width = progress + '%';
                progressFill.textContent = Math.round(progress) + '%';
                
                status.textContent = `توليد ${i + 1} من ${allWords.length}: ${allWords[i].phonetic}`;

                await generateWordAudio(i);
                
                // توقف قصير بين الكلمات
                await new Promise(resolve => setTimeout(resolve, 1500));
            }

            generateBtn.textContent = '✅ تم التوليد';
            status.textContent = '🎉 تم توليد جميع الأصوات بنجاح!';
            isGenerating = false;
        }

        // تحديث الإحصائيات
        function updateStats() {
            document.getElementById('generatedCount').textContent = generatedCount;
            document.getElementById('totalCount').textContent = allWords.length;
        }

        // تحميل البيانات الصوتية
        function downloadAudioData() {
            const completeData = {
                metadata: {
                    title: 'Armenian Audio Data',
                    description: 'Complete audio data for Armenian vocabulary learning app',
                    totalWords: allWords.length,
                    generatedWords: generatedCount,
                    generatedAt: new Date().toISOString(),
                    version: '1.0.0'
                },
                vocabulary: vocabulary,
                audioFiles: audioData,
                instructions: {
                    ar: 'استخدم هذا الملف في تطبيق الموبايل لتشغيل الأصوات',
                    en: 'Use this file in mobile app to play audio files',
                    usage: {
                        react_native: 'Place audio files in assets/audio/ folder',
                        flutter: 'Place audio files in assets/audio/ folder',
                        file_naming: 'Use audioFilename property for file names'
                    }
                }
            };

            const blob = new Blob([JSON.stringify(completeData, null, 2)], {
                type: 'application/json;charset=utf-8'
            });

            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'armenian_complete_audio_data.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            document.getElementById('status').textContent = '✅ تم تحميل ملف البيانات الصوتية الكامل!';
        }

        // اختبار الصوت
        function testAudio() {
            const utterance = new SpeechSynthesisUtterance('Barev - مرحبا');
            utterance.lang = 'en-US';
            utterance.rate = 0.7;
            speechSynthesis.speak(utterance);
        }

        // إعادة تعيين
        function resetAll() {
            if (confirm('هل تريد إعادة تعيين جميع البيانات؟')) {
                audioData = {};
                generatedCount = 0;
                isGenerating = false;
                
                document.getElementById('generateBtn').disabled = false;
                document.getElementById('generateBtn').textContent = '🎵 توليد جميع الأصوات';
                document.getElementById('downloadBtn').disabled = true;
                document.getElementById('progressFill').style.width = '0%';
                document.getElementById('progressFill').textContent = '0%';
                document.getElementById('status').textContent = 'تم إعادة التعيين - جاهز للبدء';
                
                displayWords();
                updateStats();
            }
        }

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', loadVocabulary);
    </script>
</body>
</html>
