<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🇦🇲 تطبيق تعلم الأرمينية - 743 كلمة مع صوت حقيقي</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.8rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            min-width: 140px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }

        .success-banner {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            border: 3px solid #48bb78;
        }

        .success-banner h3 {
            color: #48bb78;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .success-banner p {
            color: #4a5568;
            margin: 8px 0;
            font-size: 1.1rem;
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .search-container {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            min-width: 250px;
            padding: 15px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1.1rem;
            font-family: 'Cairo', sans-serif;
            transition: border-color 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .clear-btn {
            background: #e53e3e;
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 12px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            transition: background 0.3s ease;
        }

        .clear-btn:hover {
            background: #c53030;
        }

        .category-tabs {
            display: flex;
            gap: 12px;
            margin-bottom: 25px;
            flex-wrap: wrap;
            justify-content: center;
            max-height: 200px;
            overflow-y: auto;
        }

        .category-tab {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            font-family: 'Cairo', sans-serif;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            white-space: nowrap;
        }

        .category-tab:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }

        .category-tab.active {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .vocabulary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
        }

        .vocab-card {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 18px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            border: 2px solid transparent;
        }

        .vocab-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .play-button {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 10px rgba(72, 187, 120, 0.3);
            z-index: 10;
        }

        .play-button:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(72, 187, 120, 0.4);
        }

        .play-button:active {
            transform: scale(0.95);
        }

        .play-button.playing {
            background: linear-gradient(135deg, #667eea, #764ba2);
            animation: pulse 1.5s infinite;
        }

        .word-emoji {
            font-size: 4rem;
            text-align: center;
            margin: 20px 0;
            line-height: 1;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .armenian-text {
            font-size: 1.6rem;
            font-weight: 700;
            color: #2d3748;
            text-align: center;
            margin: 15px 0;
            font-family: 'Arial Unicode MS', 'Lucida Grande', sans-serif;
            line-height: 1.3;
            background: rgba(45, 55, 72, 0.05);
            padding: 12px 15px;
            border-radius: 15px;
        }

        .phonetic {
            font-size: 1.1rem;
            color: #667eea;
            text-align: center;
            margin: 10px 0;
            font-style: italic;
            background: rgba(102, 126, 234, 0.1);
            padding: 10px 15px;
            border-radius: 20px;
            font-weight: 500;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .arabic-phonetic {
            font-size: 1.3rem;
            color: #2c5aa0;
            font-weight: 600;
            margin: 12px 0;
            background: linear-gradient(135deg, #e3f2fd, #f8f9fa);
            padding: 12px 15px;
            border-radius: 20px;
            border: 2px solid #bbdefb;
            text-align: center;
            box-shadow: 0 2px 6px rgba(44, 90, 160, 0.1);
        }

        .arabic-meaning {
            font-size: 1.4rem;
            color: #2d3748;
            font-weight: 600;
            text-align: center;
            margin: 15px 0;
            background: rgba(72, 187, 120, 0.1);
            padding: 12px 15px;
            border-radius: 15px;
            border: 1px solid rgba(72, 187, 120, 0.2);
        }



        .audio-status {
            position: fixed;
            bottom: 25px;
            right: 25px;
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            padding: 20px 30px;
            border-radius: 30px;
            font-size: 1.1rem;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            max-width: 350px;
            text-align: center;
        }

        .audio-status.show {
            opacity: 1;
        }

        .audio-status.error {
            background: linear-gradient(135deg, #e53e3e, #c53030);
        }

        .no-results {
            text-align: center;
            color: white;
            padding: 60px 20px;
            font-size: 1.3rem;
        }

        @media (max-width: 768px) {
            .vocabulary-grid {
                grid-template-columns: 1fr;
            }

            .stats {
                flex-direction: column;
                align-items: center;
            }

            .search-container {
                flex-direction: column;
            }

            .search-box {
                min-width: 100%;
            }

            .category-tabs {
                justify-content: flex-start;
                overflow-x: auto;
                padding-bottom: 15px;
            }

            .audio-status {
                bottom: 15px;
                right: 15px;
                left: 15px;
                max-width: none;
            }

            .header h1 {
                font-size: 2.2rem;
            }
        }

        .hidden {
            display: none;
        }

        /* تأثيرات إضافية */
        .vocab-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #48bb78, #38a169);
            border-radius: 18px 18px 0 0;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .vocab-card:hover::before {
            opacity: 1;
        }


    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇦🇲 تطبيق تعلم الأرمينية المتطور</h1>
            <p>743 كلمة أرمينية مع إيموجي تفاعلي ونطق عربي فريد</p>
        </div>

        <div class="success-banner">
            <h3>🎉 بطاقات تعلم أرمينية متطورة!</h3>
            <p>✅ 743 كلمة أرمينية مع إيموجي تفاعلي</p>
            <p>🔤 نطق لاتيني وعربي لكل كلمة</p>
            <p>📁 22 فئة منظمة بتصميم أنيق</p>
            <p>🎯 تصميم نظيف ومركز على التعلم</p>
        </div>

        <div class="stats" id="stats">
            <div class="stat-card">
                <span class="stat-number" id="totalWords">743</span>
                <span class="stat-label">كلمة مع إيموجي</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="totalCategories">22</span>
                <span class="stat-label">فئة منظمة</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">4</span>
                <span class="stat-label">حقول لكل كلمة</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="currentCategoryWords">0</span>
                <span class="stat-label">كلمات الفئة الحالية</span>
            </div>
        </div>

        <div class="controls">
            <div class="search-container">
                <input type="text" id="searchBox" class="search-box" placeholder="🔍 ابحث في 743 كلمة أرمينية مع إيموجي تفاعلي...">
                <button class="clear-btn" onclick="clearSearch()">مسح البحث</button>
            </div>
        </div>

        <div class="category-tabs" id="categoryTabs">
            <!-- سيتم ملؤها بواسطة JavaScript -->
        </div>

        <div id="categoriesContainer">
            <!-- سيتم ملؤها بواسطة JavaScript -->
        </div>
    </div>

    <div class="audio-status" id="audioStatus">
        🔊 جاري تشغيل الصوت...
    </div>

    <script src="armenian_vocabulary_from_excel.js"></script>
    <script>
        let currentCategory = 'all';
        let allWords = [];
        let filteredWords = [];

        function playAudio(word) {
            const audioStatus = document.getElementById('audioStatus');
            const playButton = event ? event.target : null;

            // إزالة حالة التشغيل من جميع الأزرار
            document.querySelectorAll('.play-button').forEach(btn => {
                btn.classList.remove('playing');
            });

            audioStatus.classList.remove('error');
            audioStatus.classList.add('show');

            if (playButton) {
                playButton.classList.add('playing');
            }

            // محاولة تشغيل الصوت الحقيقي أولاً
            if (word.has_real_audio && word.audio_path) {
                try {
                    const audio = new Audio(word.audio_path);

                    audioStatus.innerHTML = `
                        🔊 <strong>${word.arabic_phonetic}</strong><br>
                        <small>صوت حقيقي: ${word.arabic}</small>
                    `;

                    audio.onended = function() {
                        if (playButton) {
                            playButton.classList.remove('playing');
                        }
                        setTimeout(() => {
                            audioStatus.classList.remove('show');
                        }, 2000);
                    };

                    audio.onerror = function() {
                        console.log('فشل تشغيل الصوت الحقيقي، التبديل إلى TTS');
                        playTTSAudio(word, playButton, audioStatus);
                    };

                    audio.play();
                    return;

                } catch (error) {
                    console.log('خطأ في تشغيل الصوت الحقيقي:', error);
                    playTTSAudio(word, playButton, audioStatus);
                    return;
                }
            }

            // استخدام TTS كبديل
            playTTSAudio(word, playButton, audioStatus);
        }

        function playTTSAudio(word, playButton, audioStatus) {
            try {
                if ('speechSynthesis' in window) {
                    speechSynthesis.cancel();

                    const utterance = new SpeechSynthesisUtterance(word.phonetic || word.armenian);
                    utterance.lang = 'en-US';
                    utterance.rate = 0.7;
                    utterance.pitch = 1;
                    utterance.volume = 0.9;

                    audioStatus.innerHTML = `
                        🔊 <strong>${word.arabic_phonetic}</strong><br>
                        <small>صوت مولد: ${word.arabic}</small>
                    `;

                    utterance.onend = function() {
                        if (playButton) {
                            playButton.classList.remove('playing');
                        }
                        setTimeout(() => {
                            audioStatus.classList.remove('show');
                        }, 2000);
                    };

                    utterance.onerror = function() {
                        if (playButton) {
                            playButton.classList.remove('playing');
                        }
                        audioStatus.classList.add('error');
                        audioStatus.innerHTML = `❌ خطأ في تشغيل: ${word.arabic_phonetic}`;
                        setTimeout(() => {
                            audioStatus.classList.remove('show');
                        }, 3000);
                    };

                    speechSynthesis.speak(utterance);
                } else {
                    audioStatus.innerHTML = `📢 ${word.arabic_phonetic}<br><small>${word.arabic}</small>`;
                    setTimeout(() => {
                        audioStatus.classList.remove('show');
                    }, 4000);
                }
            } catch (error) {
                console.log('خطأ في تشغيل TTS:', error);
                if (playButton) {
                    playButton.classList.remove('playing');
                }
                audioStatus.classList.add('error');
                audioStatus.innerHTML = `⚠️ غير متوفر: ${word.arabic_phonetic}`;
                setTimeout(() => {
                    audioStatus.classList.remove('show');
                }, 3000);
            }
        }

        function clearSearch() {
            document.getElementById('searchBox').value = '';
            showCategory(currentCategory);
        }

        function initializeApp() {
            if (typeof window.armenianVocabulary === 'undefined') {
                document.getElementById('categoriesContainer').innerHTML = `
                    <div class="no-results">
                        <h3>❌ لم يتم تحميل البيانات</h3>
                        <p>تأكد من وجود ملف armenian_vocabulary_from_excel.js</p>
                    </div>
                `;
                return;
            }

            processData();
            createCategoryTabs();
            updateStats();
            showCategory('all');

            document.getElementById('searchBox').addEventListener('input', handleSearch);

            console.log('🎉 تم تحميل التطبيق مع 743 كلمة أرمينية و 703 ملف صوتي حقيقي');
        }

        function processData() {
            allWords = [];
            for (const [categoryName, categoryData] of Object.entries(window.armenianVocabulary)) {
                categoryData.words.forEach(word => {
                    allWords.push({
                        ...word,
                        category: categoryName,
                        categoryIcon: categoryData.icon
                    });
                });
            }
        }

        function createCategoryTabs() {
            const tabsContainer = document.getElementById('categoryTabs');

            const allTab = document.createElement('button');
            allTab.className = 'category-tab active';
            allTab.innerHTML = '📚 جميع الفئات (743)';
            allTab.onclick = () => showCategory('all');
            tabsContainer.appendChild(allTab);

            for (const [categoryName, categoryData] of Object.entries(window.armenianVocabulary)) {
                const tab = document.createElement('button');
                tab.className = 'category-tab';
                tab.innerHTML = `${categoryData.icon} ${categoryName} (${categoryData.words.length})`;
                tab.onclick = () => showCategory(categoryName);
                tabsContainer.appendChild(tab);
            }
        }

        function showCategory(categoryName) {
            currentCategory = categoryName;

            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            const container = document.getElementById('categoriesContainer');
            container.innerHTML = '';

            if (categoryName === 'all') {
                for (const [catName, catData] of Object.entries(window.armenianVocabulary)) {
                    createCategorySection(catName, catData, container);
                }
            } else {
                const categoryData = window.armenianVocabulary[categoryName];
                createCategorySection(categoryName, categoryData, container);
            }

            updateCurrentCategoryStats();
        }

        function createCategorySection(categoryName, categoryData, container) {
            const section = document.createElement('div');
            section.className = 'category-section';
            section.innerHTML = `
                <div style="background: rgba(255, 255, 255, 0.95); padding: 25px; border-radius: 15px; margin-bottom: 25px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                    <div style="font-size: 2rem; font-weight: 600; color: #2d3748; margin-bottom: 15px; display: flex; align-items: center; justify-content: center; gap: 15px;">
                        <span>${categoryData.icon}</span>
                        <span>${categoryName}</span>
                        <span style="font-size: 1.2rem; color: #666;">(${categoryData.words.length} كلمة)</span>
                    </div>
                    <div style="color: #666; font-size: 1.1rem; line-height: 1.5;">${categoryData.description}</div>
                </div>
                <div class="vocabulary-grid">
                    ${categoryData.words.map(word => createWordCard(word)).join('')}
                </div>
            `;
            container.appendChild(section);
        }

        function getWordEmoji(armenian, arabic) {
            const text = (arabic + ' ' + armenian).toLowerCase();

            // إيموجي للكلمات الشائعة (بناءً على البيانات الفعلية)
            const emojiMap = {
                // التحيات والمجاملات
                'ما اسمك': '👋', 'اسمي هو': '👋', 'مرحبا': '👋', 'صباح الخير': '🌅',
                'مساء الخير': '🌆', 'تصبح على خير': '🌙', 'أراك لاحقا': '👋', 'أراك غدا': '👋',
                'سررت بلقائك': '🤝', 'سررت برؤيتك': '👁️', 'شكرا': '🙏', 'بخير، شكرا': '👍',
                'كيف حالك': '❓', 'وأنت': '👤', 'من أي بلد أنت': '🌍', 'أين تعيش': '🏠',
                'عذرا': '🙏', 'لم أسمعك': '👂', 'أتمنى لك نهارا سعيدا': '☀️',

                // أيام الأسبوع
                'الاثنين': '📅', 'الثلاثاء': '📅', 'الأربعاء': '📅', 'الخميس': '📅',
                'الجمعة': '📅', 'السبت': '📅', 'الأحد': '📅',

                // الأرقام
                'واحد': '1️⃣', 'اثنان': '2️⃣', 'ثلاثة': '3️⃣', 'أربعة': '4️⃣', 'خمسة': '5️⃣',
                'ستة': '6️⃣', 'سبعة': '7️⃣', 'ثمانية': '8️⃣', 'تسعة': '9️⃣', 'عشرة': '🔟',
                'عشرون': '2️⃣0️⃣', 'ثلاثون': '3️⃣0️⃣', 'مئة': '💯', 'ألف': '🔢',

                // العائلة
                'أم': '👩', 'أب': '👨', 'ابن': '👦', 'ابنة': '👧', 'أخ': '👨‍👦',
                'أخت': '👩‍👧', 'جد': '👴', 'جدة': '👵', 'عائلة': '👨‍👩‍👧‍👦',

                // الألوان
                'أحمر': '🔴', 'أخضر': '🟢', 'أزرق': '🔵', 'أصفر': '🟡', 'أسود': '⚫',
                'أبيض': '⚪', 'بني': '🟤', 'وردي': '🌸', 'بنفسجي': '🟣', 'برتقالي': '🟠',
                'رمادي': '🔘', 'ذهبي': '🟨', 'فضي': '🔘', 'لون': '🎨',

                // الطعام والشراب (بناءً على البيانات الفعلية)
                'خبز': '🍞', 'ماء': '💧', 'شاي': '🍵', 'قهوة': '☕', 'طعام': '🍽️',
                'يأكل': '🍽️', 'يشرب': '🥤', 'منضدة قهوة': '☕', 'شراب غازي': '🥤',
                'أريد كوب ماء': '💧', 'قائمة الطعام': '📋', 'ماذا تحب أن تأكل': '🍽️',
                'ما نوع اللحم': '🥩', 'المزيد من الماء': '💧', 'الفاكهة': '🍎',
                'شريحة لحم': '🥩', 'اللحم نيئ': '🥩', 'السمك طازج': '🐟', 'مخبز': '🍞',
                'فاكهة العاطفة': '🍎',

                // الأسئلة والاستفهام
                'هل هناك': '❓', 'هل يمكنني': '❓', 'هل يمكنك': '❓', 'ماذا': '❓',
                'تيارات خطرة تحت الماء': '🌊', 'أن أرى': '👁️', 'أن تعطيني': '🤲', 'أن تحضر لي': '🤲',

                // الجسم
                'رأس': '👤', 'عين': '👁️', 'أنف': '👃', 'فم': '👄', 'يد': '✋',
                'قدم': '🦶', 'أذن': '👂', 'شعر': '💇', 'وجه': '😊',

                // الملابس
                'قميص': '👕', 'بنطال': '👖', 'فستان': '👗', 'حذاء': '👟',
                'قبعة': '👒', 'معطف': '🧥',

                // البيت
                'بيت': '🏠', 'غرفة': '🛏️', 'مطبخ': '🍳', 'حمام': '🚿', 'سرير': '🛏️',
                'كرسي': '🪑', 'طاولة': '🪑', 'نافذة': '🪟', 'باب': '🚪',

                // الطبيعة
                'شمس': '☀️', 'قمر': '🌙', 'نجم': '⭐', 'مطر': '🌧️', 'ثلج': '❄️',
                'ريح': '💨', 'شجرة': '🌳', 'زهرة': '🌸', 'جبل': '⛰️', 'بحر': '🌊',

                // الحيوانات
                'كلب': '🐕', 'قطة': '🐱', 'حصان': '🐎', 'بقرة': '🐄', 'خروف': '🐑',
                'طائر': '🐦', 'سمكة': '🐟', 'أسد': '🦁', 'فيل': '🐘',

                // الوقت
                'ساعة': '🕐', 'دقيقة': '⏰', 'يوم': '📅', 'أسبوع': '📅', 'شهر': '📅',
                'سنة': '📅', 'صباح': '🌅', 'ظهر': '☀️', 'مساء': '🌆', 'ليل': '🌙',

                // المشاعر
                'سعيد': '😊', 'حزين': '😢', 'غاضب': '😠', 'خائف': '😨',
                'متعب': '😴', 'جائع': '🍽️', 'عطشان': '🥤', 'حب': '❤️',

                // الأفعال
                'ينام': '😴', 'يمشي': '🚶', 'يجري': '🏃', 'يقرأ': '📖', 'يكتب': '✍️',
                'يتكلم': '🗣️', 'يذهب': '➡️', 'يأتي': '⬅️', 'يعمل': '💼', 'يلعب': '🎮',

                // التعليم
                'كتاب': '📖', 'قلم': '✏️', 'مدرسة': '🏫', 'معلم': '👨‍🏫', 'طالب': '👨‍🎓',
                'درس': '📚', 'تعلم': '📚', 'دراسة': '📖',

                // النقل
                'سيارة': '🚗', 'حافلة': '🚌', 'قطار': '🚂', 'طائرة': '✈️',
                'دراجة': '🚲', 'سفينة': '🚢',

                // المهن
                'طبيب': '👨‍⚕️', 'معلم': '👨‍🏫', 'مهندس': '👨‍💻', 'عامل': '👷',
                'طباخ': '👨‍🍳', 'سائق': '👨‍✈️',

                // أيام الأسبوع
                'الاثنين': '📅', 'الثلاثاء': '📅', 'الأربعاء': '📅', 'الخميس': '📅',
                'الجمعة': '📅', 'السبت': '📅', 'الأحد': '📅',

                // الشهور
                'يناير': '🗓️', 'فبراير': '🗓️', 'مارس': '🗓️', 'أبريل': '🗓️',
                'مايو': '🗓️', 'يونيو': '🗓️', 'يوليو': '🗓️', 'أغسطس': '🗓️',
                'سبتمبر': '🗓️', 'أكتوبر': '🗓️', 'نوفمبر': '🗓️', 'ديسمبر': '🗓️',

                // كلمات أخرى
                'نعم': '✅', 'لا': '❌', 'جيد': '👍', 'سيء': '👎', 'كبير': '📏',
                'صغير': '📐', 'جديد': '✨', 'قديم': '🕰️', 'سريع': '⚡', 'بطيء': '🐌'
            };

            // البحث عن إيموجي مناسب
            for (const [keyword, emoji] of Object.entries(emojiMap)) {
                if (text.includes(keyword)) {
                    return emoji;
                }
            }

            // إيموجي افتراضي حسب الفئة (محدث)
            if (text.includes('أم') || text.includes('أب') || text.includes('عائلة')) return '👨‍👩‍👧‍👦';
            if (text.includes('طعام') || text.includes('شراب') || text.includes('يأكل') || text.includes('يشرب')) return '🍽️';
            if (text.includes('لون') || text.includes('أحمر') || text.includes('أزرق')) return '🎨';
            if (text.includes('جسم') || text.includes('رأس') || text.includes('يد')) return '👤';
            if (text.includes('ملابس') || text.includes('قميص')) return '👕';
            if (text.includes('بيت') || text.includes('منزل') || text.includes('غرفة')) return '🏠';
            if (text.includes('طبيعة') || text.includes('شجرة') || text.includes('جبل') || text.includes('شلال')) return '🏔️';
            if (text.includes('حيوان') || text.includes('كلب')) return '🐾';
            if (text.includes('وقت') || text.includes('ساعة') || text.includes('الاثنين')) return '📅';
            if (text.includes('مشاعر') || text.includes('سعيد')) return '😊';
            if (text.includes('كبير') || text.includes('صغير') || text.includes('طويل') || text.includes('قصير')) return '📏';
            if (text.includes('كمبيوتر') || text.includes('شاشة') || text.includes('هاتف')) return '💻';
            if (text.includes('بارد') || text.includes('حار') || text.includes('مظلم')) return '🌡️';
            if (text.includes('سؤال') || text.includes('إجابة') || text.includes('تعليم')) return '🧠';
            if (text.includes('هل هناك') || text.includes('هل يمكنني')) return '❓';
            if (text.includes('نقل') || text.includes('سيارة')) return '🚗';
            if (text.includes('مهنة') || text.includes('طبيب')) return '👷';
            if (text.includes('كرة القدم') || text.includes('رياضة')) return '⚽';

            return '📝'; // إيموجي افتراضي
        }

        function createWordCard(word) {
            const wordEmoji = getWordEmoji(word.armenian, word.arabic);

            return `
                <div class="vocab-card">
                    <button class="play-button" onclick="playAudio(${JSON.stringify(word).replace(/"/g, '&quot;')})">
                        🔊
                    </button>
                    <div class="word-emoji">${wordEmoji}</div>
                    <div class="armenian-text">${word.armenian}</div>
                    <div class="phonetic">${word.phonetic || word.transliteration || ''}</div>
                    <div class="arabic-phonetic">${word.arabic_phonetic}</div>
                    <div class="arabic-meaning">${word.arabic}</div>
                </div>
            `;
        }

        function updateStats() {
            const totalWords = allWords.length;
            const totalCategories = Object.keys(window.armenianVocabulary).length;

            document.getElementById('totalWords').textContent = totalWords;
            document.getElementById('totalCategories').textContent = totalCategories;
        }

        function updateCurrentCategoryStats() {
            let currentWords = 0;
            if (currentCategory === 'all') {
                currentWords = allWords.length;
            } else {
                currentWords = window.armenianVocabulary[currentCategory]?.words.length || 0;
            }
            document.getElementById('currentCategoryWords').textContent = currentWords;
        }

        function handleSearch() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();

            if (!searchTerm) {
                showCategory(currentCategory);
                return;
            }

            const results = allWords.filter(word =>
                word.armenian.toLowerCase().includes(searchTerm) ||
                (word.phonetic && word.phonetic.toLowerCase().includes(searchTerm)) ||
                (word.transliteration && word.transliteration.toLowerCase().includes(searchTerm)) ||
                word.arabic.includes(searchTerm) ||
                word.arabic_phonetic.includes(searchTerm) ||
                (word.lesson_number && word.lesson_number.toString().includes(searchTerm))
            );

            const container = document.getElementById('categoriesContainer');

            if (results.length > 0) {
                container.innerHTML = `
                    <div style="background: rgba(255, 255, 255, 0.95); padding: 25px; border-radius: 15px; margin-bottom: 25px; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                        <div style="font-size: 2rem; font-weight: 600; color: #2d3748; margin-bottom: 15px; display: flex; align-items: center; justify-content: center; gap: 15px;">
                            <span>🔍</span>
                            <span>نتائج البحث</span>
                            <span style="font-size: 1.2rem; color: #666;">(${results.length} كلمة)</span>
                        </div>
                        <div style="color: #666; font-size: 1.1rem; line-height: 1.5;">البحث عن: "${searchTerm}"</div>
                    </div>
                    <div class="vocabulary-grid">
                        ${results.map(word => createWordCard(word)).join('')}
                    </div>
                `;
            } else {
                container.innerHTML = `
                    <div class="no-results">
                        <h3>🔍 لا توجد نتائج</h3>
                        <p>لم يتم العثور على كلمات تحتوي على: "${searchTerm}"</p>
                        <p>جرب البحث بكلمات أخرى أو أرقام الدروس</p>
                    </div>
                `;
            }
        }

        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>
