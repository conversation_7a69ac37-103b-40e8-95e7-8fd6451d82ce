#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فتح مولد الصوت الأرمني
Open Armenian Audio Generator
"""

import webbrowser
from pathlib import Path
import os

def main():
    print("🔊 فتح مولد الصوت الأرمني")
    print("=" * 40)
    
    # التحقق من وجود الملفات
    audio_generator = Path('armenian_audio_generator.html')
    vocab_file = Path('armenian_vocabulary_final.json')
    
    if not audio_generator.exists():
        print("❌ ملف مولد الصوت غير موجود")
        print("💡 شغل: python armenian_audio_extractor.py")
        return
    
    if not vocab_file.exists():
        print("❌ ملف المفردات غير موجود")
        print("💡 شغل: python quick_armenian_extractor.py")
        return
    
    print("✅ جميع الملفات موجودة")
    print()
    
    # فتح مولد الصوت
    try:
        file_url = audio_generator.as_uri()
        webbrowser.open(file_url)
        
        print("🚀 تم فتح مولد الصوت!")
        print()
        print("🎯 كيفية الاستخدام:")
        print("   1️⃣ انقر على 'توليد جميع الأصوات'")
        print("   2️⃣ انتظر حتى يتم توليد جميع الأصوات")
        print("   3️⃣ انقر على 'تحميل البيانات'")
        print("   4️⃣ احفظ ملف armenian_audio_data.json")
        print()
        print("🔊 ميزات مولد الصوت:")
        print("   ✅ توليد صوت لـ 110 كلمة أرمينية")
        print("   ✅ نطق واضح وعالي الجودة")
        print("   ✅ تشغيل فردي لكل كلمة")
        print("   ✅ تحميل البيانات الصوتية")
        print("   ✅ أسماء ملفات منظمة")
        print()
        print("📱 للتطبيق الموبايل:")
        print("   📄 استخدم ملف armenian_audio_data.json")
        print("   🔊 أسماء الملفات جاهزة للاستخدام")
        print("   📁 بيانات منظمة ومهيأة")
        print()
        print("🎉 استمتع بتوليد الأصوات!")
        
    except Exception as e:
        print(f"❌ فشل فتح مولد الصوت: {e}")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
