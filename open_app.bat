@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🇦🇲 تطبيق تعلم اللغة الأرمينية
echo    Armenian Learning App
echo ========================================
echo.
echo 🚀 فتح التطبيق في المتصفح...
echo.

REM فتح التطبيق الرئيسي
echo 📱 فتح التطبيق الرئيسي...
start index.html

REM انتظار ثانيتين
timeout /t 2 /nobreak >nul

REM فتح عرض البيانات المستخرجة
echo 🎉 فتح عرض البيانات المستخرجة...
start demo_extracted_data.html

echo.
echo ✅ تم فتح التطبيق في المتصفح!
echo.
echo 📋 الملفات المفتوحة:
echo    1. index.html - التطبيق الرئيسي
echo    2. demo_extracted_data.html - عرض البيانات المستخرجة
echo.
echo 💡 إذا لم تظهر البيانات المستخرجة:
echo    - تأكد من وجود ملف vocabulary_data_complete.json
echo    - شغل الاستخراج: run_extraction_with_check.bat
echo.
pause
