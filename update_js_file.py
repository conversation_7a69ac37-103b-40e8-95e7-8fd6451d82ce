import json

# قراءة ملف JSON المحدث
with open('armenian_vocabulary_from_excel.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# إنشاء ملف JavaScript جديد
js_content = """// مفردات أرمينية من ملف Excel
// Armenian Vocabulary from Excel File

window.armenianVocabulary = """

js_content += json.dumps(data, ensure_ascii=False, indent=2)
js_content += ";"

# حفظ ملف JavaScript المحدث
with open('armenian_vocabulary_from_excel.js', 'w', encoding='utf-8') as f:
    f.write(js_content)

print("✅ تم تحديث ملف JavaScript بنجاح!")
print(f"📊 عدد الفئات: {len(data)}")

# التحقق من وجود فئة الأرقام
if 'الأرقام والحساب' in data:
    print(f"🔢 فئة الأرقام موجودة مع {len(data['الأرقام والحساب']['words'])} كلمة")
else:
    print("❌ فئة الأرقام غير موجودة!")
