import json

# قراءة البيانات
with open('armenian_vocabulary_from_excel.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print("الفئات الموجودة:")
for i, (cat, info) in enumerate(data.items(), 1):
    print(f"{i}. {cat}: {len(info['words'])} كلمة")

# البحث عن فئة الأرقام
if 'الأرقام والحساب' in data:
    print(f"\n✅ فئة الأرقام موجودة مع {len(data['الأرقام والحساب']['words'])} كلمة:")
    for word in data['الأرقام والحساب']['words']:
        print(f"  - {word['arabic']}")
else:
    print("\n❌ فئة الأرقام غير موجودة!")

# البحث عن كلمات تحتوي على أرقام
print(f"\nالبحث عن كلمات تحتوي على أرقام:")
count = 0
for cat_name, cat_data in data.items():
    for word in cat_data['words']:
        if any(num in word['arabic'] for num in ['واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة', 'عشرة']):
            print(f"  - {word['arabic']} (في فئة: {cat_name})")
            count += 1

print(f"\nإجمالي الكلمات التي تحتوي على أرقام: {count}")
