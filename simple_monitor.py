#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقب مبسط للاستخراج
"""

import os
import json
import time
from datetime import datetime

def check_files():
    """فحص الملفات المُنتجة"""
    print(f"🔍 فحص الملفات - {datetime.now().strftime('%H:%M:%S')}")
    print("=" * 50)
    
    files_to_check = [
        'vocabulary_data_complete.json',
        'vocabulary_data_complete.js', 
        'armenian_vocabulary_complete.txt'
    ]
    
    for file in files_to_check:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file}: {size:,} بايت")
            
            # إذا كان ملف JSON، اقرأ الإحصائيات
            if file.endswith('.json') and size > 100:
                try:
                    with open(file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    lessons = len(data)
                    total_words = sum(len(words) for words in data.values())
                    
                    print(f"   📚 دروس: {lessons}/125")
                    print(f"   📝 كلمات: {total_words}")
                    
                    if lessons > 0:
                        print(f"   📊 متوسط الكلمات/درس: {total_words/lessons:.1f}")
                    
                    # عرض آخر درس
                    if data:
                        last_lesson = list(data.keys())[-1]
                        print(f"   📖 آخر درس: {last_lesson}")
                        
                except Exception as e:
                    print(f"   ⚠️ خطأ في قراءة JSON: {e}")
        else:
            print(f"⏳ {file}: لم يُنشأ بعد")
    
    # فحص مجلد الصوتيات
    if os.path.exists('audio'):
        audio_files = [f for f in os.listdir('audio') if f.endswith('.mp3')]
        print(f"🎵 ملفات صوتية: {len(audio_files)}")
    else:
        print(f"🎵 مجلد الصوتيات: لم يُنشأ بعد")
    
    print()

if __name__ == "__main__":
    check_files()
