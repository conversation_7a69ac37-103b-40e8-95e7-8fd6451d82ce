#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مستخرج مبسط للمفردات الأرمينية
Simple Armenian Vocabulary Scraper
"""

import requests
from bs4 import BeautifulSoup
import json
import re
import time
from datetime import datetime

class SimpleArmenianScraper:
    def __init__(self):
        self.base_url = "https://www.lingohut.com"
        self.main_page = "/ar/l76/%D8%AA%D8%B9%D9%84%D9%85-%D8%A7%D9%84%D9%84%D8%BA%D8%A9-%D8%A7%D9%84%D8%A3%D8%B1%D9%85%D9%8A%D9%86%D9%8A%D8%A9"
        self.vocabulary_data = {}
        self.total_extracted = 0
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)

    def generate_arabic_phonetic(self, phonetic):
        """تحويل النطق اللاتيني إلى عربي"""
        if not phonetic:
            return ""
        
        # قاموس تحويل شامل
        latin_to_arabic = {
            # حروف العلة
            'a': 'ا', 'e': 'ي', 'i': 'ي', 'o': 'و', 'u': 'و',
            'A': 'ا', 'E': 'ي', 'I': 'ي', 'O': 'و', 'U': 'و',
            
            # الحروف الساكنة
            'b': 'ب', 'p': 'ب', 'f': 'ف', 'v': 'ف',
            't': 'ت', 'd': 'د', 's': 'س', 'z': 'ز',
            'k': 'ك', 'g': 'ج', 'h': 'ه', 'l': 'ل',
            'r': 'ر', 'm': 'م', 'n': 'ن', 'w': 'و', 'y': 'ي',
            'j': 'ج', 'c': 'ك', 'q': 'ك', 'x': 'كس',
            
            # حروف خاصة
            'ch': 'تش', 'sh': 'ش', 'th': 'ث', 'gh': 'غ',
            'kh': 'خ', 'zh': 'ج', 'ng': 'نغ',
            
            # رموز خاصة
            'ʿ': 'ع', 'ʾ': 'ء', 'ṛ': 'ر', 'ṭ': 'ت', 'ḍ': 'د',
            'ṣ': 'ص', 'ẓ': 'ظ', 'ḥ': 'ح', 'ḫ': 'خ', 'ġ': 'غ'
        }
        
        # تنظيف النص
        text = phonetic.strip()
        
        # معالجة المجموعات الصوتية أولاً
        for latin, arabic in [('ch', 'تش'), ('sh', 'ش'), ('th', 'ث'), ('gh', 'غ'), ('kh', 'خ'), ('zh', 'ج'), ('ng', 'نغ')]:
            text = text.replace(latin, arabic)
        
        # تحويل الأحرف المفردة
        result = ""
        for char in text:
            if char in latin_to_arabic:
                result += latin_to_arabic[char]
            elif char.isspace():
                result += char
            elif char in '.,!?;:':
                result += char
            else:
                result += char
        
        return result

    def extract_lessons_list(self):
        """استخراج قائمة الدروس"""
        try:
            print("🔍 جاري البحث عن قائمة الدروس...")
            
            response = self.session.get(self.base_url + self.main_page, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # البحث عن روابط الدروس
            lesson_links = soup.find_all('a', href=re.compile(r'/ar/v477'))
            
            lessons = []
            for link in lesson_links:
                href = link.get('href')
                text = link.get_text(strip=True)
                
                if href and text and 'درس' in text:
                    if not href.startswith('http'):
                        href = self.base_url + href
                    
                    lessons.append({
                        'url': href,
                        'title': text
                    })
            
            print(f"✅ تم العثور على {len(lessons)} درس")
            return lessons
            
        except Exception as e:
            print(f"❌ خطأ في استخراج قائمة الدروس: {e}")
            return []

    def extract_vocabulary_from_lesson(self, lesson_url, lesson_title):
        """استخراج المفردات من درس واحد"""
        try:
            print(f"📖 معالجة: {lesson_title}")
            
            response = self.session.get(lesson_url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # البحث عن النصوص الأرمينية
            armenian_pattern = r'[\u0530-\u058F]+'
            armenian_texts = re.findall(armenian_pattern, response.text)
            
            # البحث عن النطق في الأقواس
            phonetic_pattern = r'\(([A-Za-z][^)]*)\)'
            all_phonetic = re.findall(phonetic_pattern, response.text)
            
            # تصفية النطق الحقيقي
            phonetic_texts = []
            for p in all_phonetic:
                if not any(x in p.lower() for x in ['px', 'deg', 'width', 'height', 'color', 'font', 'margin', 'padding']):
                    if len(p) > 1 and len(p) < 30:
                        phonetic_texts.append(p)
            
            # البحث عن المعاني العربية
            arabic_pattern = r'[\u0600-\u06FF]+'
            arabic_texts = re.findall(arabic_pattern, response.text)
            
            # تنظيف وتجميع البيانات
            vocabulary = []
            max_items = min(len(armenian_texts), len(phonetic_texts))
            
            for i in range(max_items):
                armenian = armenian_texts[i] if i < len(armenian_texts) else ""
                phonetic = phonetic_texts[i] if i < len(phonetic_texts) else ""
                arabic = arabic_texts[i] if i < len(arabic_texts) else "غير متوفر"
                
                if armenian and phonetic:
                    # إنشاء النطق العربي
                    arabic_phonetic = self.generate_arabic_phonetic(phonetic)
                    
                    word_data = {
                        'armenian': armenian,
                        'phonetic': phonetic,
                        'arabic': arabic,
                        'arabic_phonetic': arabic_phonetic,
                        'meaning': f"كلمة من {lesson_title}",
                        'lesson': lesson_title,
                        'audio': re.sub(r'[^a-zA-Z0-9]', '-', phonetic.lower())
                    }
                    
                    vocabulary.append(word_data)
            
            print(f"   ✅ تم استخراج {len(vocabulary)} كلمة")
            return vocabulary
            
        except Exception as e:
            print(f"   ❌ خطأ في معالجة {lesson_title}: {e}")
            return []

    def save_data(self):
        """حفظ البيانات في ملفات مختلفة"""
        try:
            # حفظ JSON
            with open('vocabulary_data_complete.json', 'w', encoding='utf-8') as f:
                json.dump(self.vocabulary_data, f, ensure_ascii=False, indent=2)
            
            # حفظ JavaScript
            js_content = f"// بيانات المفردات الأرمينية مع النطق العربي\n"
            js_content += f"// تم الاستخراج في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            js_content += f"window.vocabularyData = {json.dumps(self.vocabulary_data, ensure_ascii=False, indent=2)};\n"
            
            with open('vocabulary_data_complete.js', 'w', encoding='utf-8') as f:
                f.write(js_content)
            
            # حفظ ملف نصي
            text_content = "مفردات اللغة الأرمينية مع النطق العربي\n"
            text_content += "=" * 50 + "\n\n"
            
            for lesson_key, words in self.vocabulary_data.items():
                if words:
                    lesson_title = words[0].get('lesson', lesson_key)
                    text_content += f"{lesson_title}:\n"
                    text_content += "-" * len(lesson_title) + "\n"
                    
                    for i, word in enumerate(words, 1):
                        text_content += f"{i}. {word['armenian']} ({word['phonetic']}) → {word['arabic_phonetic']}\n"
                        text_content += f"   المعنى: {word['arabic']}\n"
                        text_content += f"   الشرح: {word['meaning']}\n\n"
                    
                    text_content += "\n"
            
            with open('armenian_vocabulary_complete.txt', 'w', encoding='utf-8') as f:
                f.write(text_content)
            
            print(f"✅ تم حفظ البيانات في 3 ملفات")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")

    def run_extraction(self):
        """تشغيل عملية الاستخراج الكاملة"""
        print("🚀 بدء الاستخراج المبسط للمفردات الأرمينية")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # استخراج قائمة الدروس
        lessons = self.extract_lessons_list()
        
        if not lessons:
            print("❌ لم يتم العثور على أي دروس")
            return
        
        print(f"📚 سيتم معالجة {len(lessons)} درس")
        print("🔤 سيتم إضافة النطق العربي لكل كلمة")
        print()
        
        # معالجة كل درس
        successful_lessons = 0
        failed_lessons = 0
        
        for i, lesson in enumerate(lessons, 1):
            try:
                print(f"[{i}/{len(lessons)}] ", end="")
                
                vocabulary = self.extract_vocabulary_from_lesson(lesson['url'], lesson['title'])
                
                if vocabulary:
                    lesson_key = f"lesson_{i}_{lesson['title'].replace(' ', '_')}"
                    self.vocabulary_data[lesson_key] = vocabulary
                    self.total_extracted += len(vocabulary)
                    successful_lessons += 1
                else:
                    failed_lessons += 1
                
                # توقف قصير بين الطلبات
                time.sleep(2)
                
                # حفظ التقدم كل 10 دروس
                if i % 10 == 0:
                    self.save_data()
                    print(f"💾 تم حفظ التقدم: {i} درس")
                
            except Exception as e:
                print(f"❌ خطأ في الدرس {i}: {e}")
                failed_lessons += 1
                continue
        
        # حفظ البيانات النهائية
        self.save_data()
        
        # إحصائيات نهائية
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print("🎉 تم الانتهاء من الاستخراج!")
        print(f"📊 النتائج النهائية:")
        print(f"   📝 إجمالي الكلمات: {self.total_extracted}")
        print(f"   ✅ دروس ناجحة: {successful_lessons}")
        print(f"   ❌ دروس فاشلة: {failed_lessons}")
        print(f"   ⏱️ الوقت المستغرق: {duration}")
        print(f"   🎯 معدل النجاح: {(successful_lessons/(successful_lessons+failed_lessons)*100):.1f}%")
        
        print(f"\n📁 الملفات المُنشأة:")
        print(f"   - vocabulary_data_complete.json")
        print(f"   - vocabulary_data_complete.js")
        print(f"   - armenian_vocabulary_complete.txt")

def main():
    """الدالة الرئيسية"""
    scraper = SimpleArmenianScraper()
    scraper.run_extraction()

if __name__ == "__main__":
    main()
