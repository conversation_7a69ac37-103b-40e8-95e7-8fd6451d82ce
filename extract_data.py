#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
استخراج البيانات من موقع LingoHut
Extract Data from LingoHut Website
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def detect_python():
    """اكتشاف أمر Python"""
    for cmd in ['py', 'python', 'python3']:
        try:
            result = subprocess.run([cmd, '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return cmd
        except:
            continue
    return None

def install_packages():
    """تثبيت المكتبات المطلوبة"""
    python_cmd = detect_python()
    if not python_cmd:
        print("❌ Python غير متاح")
        return False
    
    print("📦 تثبيت المكتبات المطلوبة...")
    
    packages = [
        'requests',
        'beautifulsoup4',
        'selenium', 
        'webdriver-manager'
    ]
    
    for package in packages:
        print(f"   📥 {package}...")
        try:
            result = subprocess.run([
                python_cmd, '-m', 'pip', 'install', 
                package, '--quiet', '--disable-pip-version-check'
            ], timeout=60)
            
            if result.returncode == 0:
                print(f"   ✅ {package}")
            else:
                # محاولة بصلاحيات المستخدم
                subprocess.run([
                    python_cmd, '-m', 'pip', 'install', 
                    package, '--user', '--quiet'
                ], timeout=60)
                print(f"   ✅ {package} (user)")
                
        except Exception as e:
            print(f"   ⚠️ {package} - {e}")
    
    print("✅ انتهى تثبيت المكتبات")
    return True

def test_internet():
    """اختبار الإنترنت"""
    print("🌐 فحص الاتصال بالإنترنت...")
    try:
        import requests
        response = requests.get('https://www.google.com', timeout=10)
        if response.status_code == 200:
            print("   ✅ الإنترنت يعمل")
            return True
        else:
            print(f"   ⚠️ مشكلة في الاتصال: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ لا يوجد اتصال: {e}")
        return False

def run_scraper():
    """تشغيل مستخرج البيانات"""
    python_cmd = detect_python()
    app_dir = Path(__file__).parent
    
    # البحث عن ملف الاستخراج
    scraper_files = [
        ('advanced_scraper.py', 'المستخرج المتقدم'),
        ('simple_scraper.py', 'المستخرج المبسط')
    ]
    
    scraper_file = None
    scraper_name = None
    
    for file, name in scraper_files:
        if (app_dir / file).exists():
            scraper_file = app_dir / file
            scraper_name = name
            break
    
    if not scraper_file:
        print("❌ لا توجد ملفات استخراج")
        print("💡 تأكد من وجود advanced_scraper.py أو simple_scraper.py")
        return False
    
    print(f"🚀 بدء الاستخراج باستخدام {scraper_name}...")
    print("🔤 سيتم إضافة النطق العربي لكل كلمة")
    print("⏳ قد يستغرق 30-60 دقيقة...")
    print()
    
    try:
        # تشغيل المستخرج مع عرض المخرجات
        process = subprocess.Popen([
            python_cmd, str(scraper_file)
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
           text=True, bufsize=1, universal_newlines=True)
        
        # عرض المخرجات في الوقت الفعلي
        for line in process.stdout:
            print(line.rstrip())
        
        process.wait()
        
        if process.returncode == 0:
            print("\n✅ تم الانتهاء من الاستخراج بنجاح!")
            return True
        else:
            print(f"\n❌ فشل الاستخراج (كود: {process.returncode})")
            return False
            
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الاستخراج بواسطة المستخدم")
        return False
    except Exception as e:
        print(f"\n❌ خطأ في الاستخراج: {e}")
        return False

def check_results():
    """فحص نتائج الاستخراج"""
    app_dir = Path(__file__).parent
    json_file = app_dir / 'vocabulary_data_complete.json'
    
    if json_file.exists():
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            lessons = len(data)
            words = sum(len(w) for w in data.values())
            words_with_arabic = sum(
                1 for words_list in data.values() 
                for word in words_list 
                if word.get('arabic_phonetic')
            )
            
            print(f"\n📊 نتائج الاستخراج:")
            print(f"   📚 {lessons} درس")
            print(f"   📝 {words} كلمة")
            print(f"   🔤 {words_with_arabic} كلمة بنطق عربي")
            print(f"   📁 الملفات المُنتجة:")
            
            # فحص الملفات المُنتجة
            files = [
                'vocabulary_data_complete.json',
                'vocabulary_data_complete.js',
                'armenian_vocabulary_complete.txt'
            ]
            
            for file in files:
                file_path = app_dir / file
                if file_path.exists():
                    size = file_path.stat().st_size
                    print(f"      ✅ {file} ({size:,} بايت)")
                else:
                    print(f"      ❌ {file} مفقود")
            
            return True
            
        except Exception as e:
            print(f"\n❌ خطأ في قراءة النتائج: {e}")
            return False
    else:
        print("\n❌ لم يتم إنتاج ملف البيانات")
        return False

def main():
    """الدالة الرئيسية"""
    print("🇦🇲 استخراج المفردات الأرمينية من موقع LingoHut")
    print("=" * 55)
    print()
    
    # فحص Python
    python_cmd = detect_python()
    if not python_cmd:
        print("❌ Python غير مثبت أو غير متاح")
        print("💡 ثبت Python من: https://python.org/downloads")
        return
    
    print(f"✅ Python متوفر ({python_cmd})")
    
    # تثبيت المكتبات
    if not install_packages():
        print("❌ فشل في تثبيت المكتبات")
        return
    
    # فحص الإنترنت
    if not test_internet():
        print("❌ يجب توفر اتصال إنترنت للاستخراج")
        return
    
    print()
    print("🎯 كل شيء جاهز للاستخراج!")
    print()
    
    # تأكيد البدء
    try:
        confirm = input("هل تريد بدء الاستخراج؟ (y/n): ").lower()
        if confirm != 'y':
            print("🛑 تم إلغاء العملية")
            return
    except KeyboardInterrupt:
        print("\n🛑 تم إلغاء العملية")
        return
    
    print()
    
    # تشغيل الاستخراج
    if run_scraper():
        # فحص النتائج
        if check_results():
            print("\n🎉 تم الاستخراج بنجاح!")
            print("💡 لفتح التطبيق: python open_app.py")
        else:
            print("\n⚠️ الاستخراج انتهى لكن هناك مشاكل في النتائج")
    else:
        print("\n❌ فشل الاستخراج")
        print("💡 جرب:")
        print("   1. تحقق من اتصال الإنترنت")
        print("   2. أعد المحاولة لاحقاً")
        print("   3. استخدم المستخرج المبسط")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف التطبيق")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    
    input("\nاضغط Enter للخروج...")
